execute if entity @a[name=<PERSON>oc_Cutee,tag=!shield] run tag Ngoc_Cutee add addshield
tag Ngoc_Cutee remove shield
execute if entity @a[name=Ngoc_Cutee,tag=addshield] run tag Ngoc_Cutee add shield
tag Ngoc_Cutee remove addshield

execute if entity @a[name=Ngoc_Cutee,tag=shield] run title @a actionbar [{"text":"SHIELDING ","color":"yellow"},{"text":"ON", "color":"green"}]
execute if entity @a[name=Ngoc_Cutee,tag=!shield] run title @a actionbar [{"text":"SHIELDING ","color":"yellow"},{"text":"OFF", "color":"red"}]

execute as Ngoc_Cutee if entity @s[tag=shield,tag=sword] run item replace entity @a[scores={kitloaded=0}] weapon.offhand with shield[minecraft:unbreakable={}]
execute as Ngoc_Cutee if entity @s[tag=!shield,tag=sword] run item replace entity @a[scores={kitloaded=0}] weapon.offhand with air
execute as Ngoc_Cutee if entity @s[tag=shield] as @a[name=!Ngoc_Cutee] if score @s kitloaded matches 0 run item replace entity @a hotbar.2 with minecraft:diamond_axe[minecraft:unbreakable={}]
execute as Ngoc_Cutee if entity @s[tag=!shield] as @a[name=!Ngoc_Cutee] if score @s kitloaded matches 0 run item replace entity @a hotbar.2 with air

execute as Ngoc_Cutee if entity @s[tag=shield] run tag @s add tempshield
execute as Ngoc_Cutee if entity @s[tag=!shield] run tag @s remove tempshield

execute as Ngoc_Cutee if entity @s[tag=sword,tag=!shield,tag=neth] run item replace entity @s hotbar.0 with minecraft:netherite_sword[minecraft:enchantments={levels:{"minecraft:sharpness":5}},minecraft:unbreakable={}]
execute as Ngoc_Cutee if entity @s[tag=sword,tag=shield,tag=neth] run item replace entity @s hotbar.0 with minecraft:netherite_sword[minecraft:enchantments={levels:{"minecraft:sharpness":5,"minecraft:knockback":1}},minecraft:unbreakable={}]