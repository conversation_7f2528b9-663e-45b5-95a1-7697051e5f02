#forward marker positioned towards bot by 1 block
#execute at @s facing entity Ng<PERSON>_Cutee feet run teleport @e[name=forward,type=marker] ^ ^ ^1
execute at @s run teleport @e[name=bforward,type=marker] ~ ~ ~ facing entity @p[name=!Ng<PERSON>_Cutee]
execute as @e[name=bforward,type=marker] at @s run teleport @s ~ ~ ~ ~ 0
execute as @e[name=bforward,type=marker] at @s run teleport ^ ^ ^1

execute at @s facing entity @p[name=!Ngoc_Cutee] feet run teleport @e[name=bright,type=marker] ^-1 ^ ^

#updated position and old position
scoreboard players operation @s oldx = @s x
scoreboard players operation @s oldy = @s y
scoreboard players operation @s oldz = @s z
execute store result score @s x run data get entity @s Pos[0] 100
execute store result score @s y run data get entity @s Pos[1] 100
execute store result score @s z run data get entity @s Pos[2] 100

#gets motion from old position values
scoreboard players operation @s vx = @s x
scoreboard players operation @s vy = @s y
scoreboard players operation @s vz = @s z
scoreboard players operation @s vx -= @s oldx
scoreboard players operation @s vy -= @s oldy
scoreboard players operation @s vz -= @s oldz

#forward XZ vector towards the bot
execute store result score @s fx run data get entity @e[type=marker,name=bforward,limit=1] Pos[0] 100
execute store result score @s fz run data get entity @e[type=marker,name=bforward,limit=1] Pos[2] 100
scoreboard players operation @s fx -= @s x
scoreboard players operation @s fz -= @s z
#forward dot product
scoreboard players operation @s fx *= @s vx
scoreboard players operation @s fz *= @s vz
scoreboard players operation @s fv = @s fx
scoreboard players operation @s fv += @s fz
scoreboard players operation @s fv /= !fvscale const

#rightward XZ vector tangential to the bot
execute store result score @s rx run data get entity @e[type=marker,name=bright,limit=1] Pos[0] 100
execute store result score @s rz run data get entity @e[type=marker,name=bright,limit=1] Pos[2] 100
scoreboard players operation @s rx -= @s x
scoreboard players operation @s rz -= @s z
#rightward dot product
scoreboard players operation @s rx *= @s vx
scoreboard players operation @s rz *= @s vz
scoreboard players operation @s rv = @s rx
scoreboard players operation @s rv += @s rz
scoreboard players operation @s rv /= !fvscale const

scoreboard players operation !combined fv = @s fv
scoreboard players operation !combined fv -= @p[name=!Ngoc_Cutee] fv
scoreboard players operation !combined rv = @s rv
scoreboard players operation !combined rv -= @p[name=!Ngoc_Cutee] rv