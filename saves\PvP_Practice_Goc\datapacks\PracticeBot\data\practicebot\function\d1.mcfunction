#effect give TheobaldTheBot resistance 1 255 true
#effect give TheobaldTheBot speed 1 1 true
scoreboard players remove TheobaldTheBot hitcd 1
scoreboard players add TheobaldTheBot refill 1
scoreboard players remove @a[name=TheobaldTheBot] strafecd 1
scoreboard players set @a[scores={totem=1..}] refill 0
scoreboard players set @a totem 0

execute as TheobaldTheBot unless entity @s[tag=refilling] run function practicebot:look
execute as TheobaldTheBot if score @s difficulty matches 5 run function practicebot:look

player TheobaldTheBot sprint
player <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> move forward
player Theobal<PERSON>TheBot hotbar 1
execute at TheobaldTheBot if entity @p[name=!TheobaldTheBot,distance=..2] run player <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> move
execute as @a[name=TheobaldTheBot] if score @s difficulty matches 1 if score @s hitcd matches 3.. run player <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> move
execute at @e[name=crystal] if entity @a[name=TheobaldTheBot,distance=..2] run player Theo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> move

execute as TheobaldTheBot if entity @s[scores={strafecd=..0},tag=strafe] run function practicebot:choosedirection
function practicebot:strafe

execute as TheobaldTheBot at @s unless entity @s[tag=refilling] if block ~ ~-.2 ~ netherite_block at @e[name=crystal] if entity @a[name=TheobaldTheBot,distance=..5] run player TheobaldTheBot look at ~ ~1 ~ 
execute as TheobaldTheBot at @s if block ~ ~-.2 ~ netherite_block at @e[name=crystal] if entity @a[name=TheobaldTheBot,distance=..5] run function practicebot:hitcrystal
execute as TheobaldTheBot at @s unless entity @s[tag=refilling] at @p[name=!TheobaldTheBot,distance=4.2..] if block ~ ~-.1 ~ netherite_block if score TheobaldTheBot difficulty matches 2..4 run function practicebot:pearl
execute at TheobaldTheBot at @p[name=!TheobaldTheBot,distance=4.2..] if score TheobaldTheBot difficulty matches 5 run function practicebot:tp

execute as TheobaldTheBot unless entity @s[tag=refilling] if score TheobaldTheBot hitcd matches ..-2 if score TheobaldTheBot difficulty matches 2.. at TheobaldTheBot if entity @p[name=!TheobaldTheBot,distance=..10] run function practicebot:pearl
execute as TheobaldTheBot unless entity @s[tag=refilling] if score @s hitcd matches ..-2 if score @s difficulty matches 1 at @s if entity @p[name=!TheobaldTheBot,distance=..2] if score @s shieldcd matches 1.. run function practicebot:hit
execute as TheobaldTheBot unless entity @s[tag=refilling,tag=shield] if score @s hitcd matches ..-2 if score @s difficulty matches 1 at @s if entity @p[name=!TheobaldTheBot,distance=..2] run function practicebot:hit
execute as TheobaldTheBot unless entity @s[tag=refilling] if score @s hitcd matches ..0 if score @s difficulty matches 2.. at @s if entity @p[name=!TheobaldTheBot,distance=..2.5] run function practicebot:hit
execute as TheobaldTheBot unless entity @s[tag=refilling] if score @s hitcd matches ..0 if score @s difficulty matches 5.. at @s if entity @p[name=!TheobaldTheBot,distance=..3] run function practicebot:hit

#execute as @a[name=!TheobaldTheBot,scores={pops=27..}] run function practicebot:refilltotem
#scoreboard players set @a[name=!TheobaldTheBot,scores={pops=28..}] pops 0

# execute as @a[nbt={HurtTime:9s},name=TheobaldTheBot] on attacker run scoreboard players add @s Score 1
# scoreboard players reset TheobaldTheBot Score

execute if score TheobaldTheBot difficulty matches 1..2 run kill @e[type=marker,scores={crystal=1..}]
kill @e[type=marker,scores={crystal=2..}]

tag TheobaldTheBot remove refilling
execute as TheobaldTheBot if score @s difficulty matches 1..2 if score @s refill matches ..22 run function practicebot:pop
execute as TheobaldTheBot if score @s difficulty matches 3 if score @s refill matches ..18 run function practicebot:pop
execute as TheobaldTheBot if score @s difficulty matches 4 if score @s refill matches ..14 run function practicebot:pop
execute as TheobaldTheBot if score @s difficulty matches 5 run function practicebot:refilltotem