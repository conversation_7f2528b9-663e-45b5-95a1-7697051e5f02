fill -192 70 208 208 61 -192 air
fill 33 146 -643 -166 141 -444 air

tp @a[name=!TheobaldTheBot] 8.5 13 8.5 0 0
kill @e[type=minecraft:end_crystal]
setblock 8 61 8 minecraft:stone_button[face=floor,facing=north,powered=false] keep
player <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ot stop
gamemode adventure @a[name=!TheobaldTheBot]
gamemode creative TheobaldTheBot
tp TheobaldTheBot 8.50 13.00 14.50 180.0 0
player <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> spawn at 8.50 13.00 14.50 facing 180 0
kill @e[type=creeper]
kill @e[type=minecraft:marker]
kill @e[type=ender_pearl]
kill @e[type=arrow]
kill @e[type=tnt_minecart]
kill @e[type=item]
player <PERSON><PERSON><PERSON><PERSON>he<PERSON><PERSON> hotbar 1
scoreboard players set @a kitloaded 0
execute as @a[name=TheobaldTheBot,tag=crystal] run function practicebot:setcrystal
execute as @a[name=TheobaldTheBot,tag=sword] run function practicebot:setsword
execute as @a[name=TheobaldTheBot,tag=sword,tag=!naked] run function practicebot:kits/loadkit
execute as @a[name=TheobaldTheBot,tag=crystal] run function practicebot:kits/loadkit
execute as @a[name=TheobaldTheBot,tag=sword,tag=!naked] run function practicebot:botgear/crystalgear
kill @e[type=marker]
summon minecraft:marker ~ ~ ~ {CustomName:'{"text":"pforward"}'}
summon minecraft:marker ~ ~ ~ {CustomName:'{"text":"pright"}'}
summon minecraft:marker ~ ~ ~ {CustomName:'{"text":"bforward"}'}
summon minecraft:marker ~ ~ ~ {CustomName:'{"text":"bright"}'}
summon minecraft:marker -165.5 140.5 -642.5 {CustomName:"filler"}
scoreboard players set @e[name=filler] layer 204
execute as TheobaldTheBot if score @s holeused matches 1 if entity @s[tag=crystal] as @e[name=filler,limit=1] run function practicebot:hole/fill
scoreboard players set TheobaldTheBot holeused 0

# title @a subtitle {"text":""}
# execute as @a[scores={death=1..}] if score TheobaldTheBot difficulty matches 1.. run title @s title [{"text": "SCORE: ","color": "yellow"},{"score":{"objective":"Score","name":"*"},"color": "green"}]
execute as @a[scores={death=1..},name=!TheobaldTheBot] run tellraw @a {"text": "<TheobaldTheBot> gg ez"}
#execute as @a[name=TheobaldTheBot,tag=crystal,scores={difficulty=1..}] as @a[name=!TheobaldTheBot,limit=1,scores={death=0}] run tellraw @a {"text": "<TheobaldTheBot> wise dodge kid"}
scoreboard players set @a death 0
scoreboard players set TheobaldTheBot hitcd 0
#execute if score TheobaldTheBot difficulty matches 0 run scoreboard players reset @a[name=!TheobaldTheBot] pops
player TheobaldTheBot unsprint
effect give @a instant_health 2 255 true
scoreboard players set TheobaldTheBot combo 0

execute as TheobaldTheBot if entity @s[tag=crit] run tag @s add tempcrit
execute as TheobaldTheBot if entity @s[tag=!crit] run tag @s remove tempcrit

execute as TheobaldTheBot if entity @s[tag=jreset] run tag @s add tempjreset
execute as TheobaldTheBot if entity @s[tag=!jreset] run tag @s remove tempjreset

execute as TheobaldTheBot if entity @s[tag=stap] run tag @s add tempstap
execute as TheobaldTheBot if entity @s[tag=!stap] run tag @s remove tempstap

execute as TheobaldTheBot if entity @s[tag=shield] run tag @s add tempshield
execute as TheobaldTheBot if entity @s[tag=!shield] run tag @s remove tempshield

execute as TheobaldTheBot if entity @s[tag=strafe] run tag @s add tempstrafe
execute as TheobaldTheBot if entity @s[tag=!strafe] run tag @s remove tempstrafe