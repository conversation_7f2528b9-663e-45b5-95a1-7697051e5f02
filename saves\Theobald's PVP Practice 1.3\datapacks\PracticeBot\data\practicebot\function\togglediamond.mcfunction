execute if entity @a[name=<PERSON><PERSON>_Cutee,tag=!diamond] run tag Ng<PERSON>_Cutee add adddiamond
tag Ng<PERSON>_Cutee remove diamond
tag Ng<PERSON>_Cutee remove neth
execute if entity @a[name=<PERSON><PERSON>_Cutee,tag=adddiamond] run tag Ng<PERSON>_Cutee add diamond
execute if entity @a[name=Ng<PERSON>_Cutee,tag=!adddiamond] run tag Ng<PERSON>_Cutee add neth
tag Ng<PERSON>_Cutee remove adddiamond
tag Ng<PERSON>_Cutee remove naked

execute if entity @a[name=Ngoc_Cutee,tag=diamond] run title @a actionbar [{"text":"GEAR CHANGED TO ","color":"yellow"},{"text":"DIAMOND", "color":"aqua"}]
execute if entity @a[name=<PERSON><PERSON>_Cutee,tag=!diamond] run title @a actionbar [{"text":"GEAR CHANGED TO ","color":"yellow"},{"text":"NETHERITE", "color":"dark_purple"}]

function practicebot:botgear/crystalgear