tag <PERSON><PERSON><PERSON><PERSON><PERSON> remove crystal
tag <PERSON><PERSON><PERSON><PERSON>ee add sword
clear @a
effect clear @a
item replace entity @a hotbar.0 with minecraft:diamond_sword[minecraft:unbreakable={}]
item replace entity @a hotbar.1 with minecraft:wooden_sword[minecraft:unbreakable={}]
execute as <PERSON><PERSON>_Cutee if entity @s[tag=shield] run item replace entity @a hotbar.2 with minecraft:diamond_axe[minecraft:unbreakable={}]
execute as Ngoc_Cutee if entity @s[tag=shield] run item replace entity @s hotbar.3 with minecraft:diamond_axe[minecraft:unbreakable={}]
execute as <PERSON>oc_Cutee if entity @s[tag=shield] run item replace entity @a weapon.offhand with shield[minecraft:unbreakable={}]
execute as <PERSON><PERSON>_Cutee if entity @s[tag=!shield] run item replace entity @s weapon.offhand with air
#item replace entity @a inventory.18 with minecraft:diamond_sword{Unbreakable:true,Enchantments:[{id:"knockback",lvl:1}]}
#item replace entity @a inventory.19 with minecraft:wooden_sword{Unbreakable:true,Enchantments:[{id:"knockback",lvl:1}]}
item replace entity @a inventory.8 with minecraft:barrier[minecraft:custom_name="{'text':'CLICK TO RESET','color':'red','bold':true,'italic':false}"]
player Ngoc_Cutee hotbar 1
title @a actionbar [{"text":"MODE CHANGED TO ","color":"yellow"},{"text":"SWORD", "color":"aqua"}]
scoreboard players reset @a pops
scoreboard objectives setdisplay sidebar
scoreboard objectives setdisplay below_name combo