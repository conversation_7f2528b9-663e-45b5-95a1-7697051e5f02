execute store result score <PERSON><PERSON>_Cutee food run data get entity <PERSON><PERSON>_Cutee foodLevel
execute as <PERSON><PERSON><PERSON>Cutee if score @s food matches ..16 run effect give @s minecraft:saturation 1 1 true

execute if score Ng<PERSON>_Cutee difficulty matches 0 run function practicebot:look
execute as <PERSON><PERSON><PERSON>Cutee if score @s difficulty matches 0 run function practicebot:refilltotem
execute as <PERSON><PERSON><PERSON>Cutee if score @s shieldcd matches 1.. run scoreboard players remove @s shieldcd 1

execute if score Ngoc_Cutee difficulty matches 1.. run function practicebot:d1
execute as <PERSON><PERSON><PERSON>Cutee if score @s difficulty matches 0..1 if entity @s[tag=shield] if score @s shieldcd matches 0 run player <PERSON><PERSON>_Cutee hotbar 9
execute as <PERSON><PERSON>_Cutee if score @s difficulty matches 0..1 if entity @s[tag=shield] unless entity @s[tag=refilling] run player <PERSON><PERSON><PERSON><PERSON><PERSON> use continuous

execute at @a[name=!Ngoc_Cutee,x=0,y=0,z=0,distance=..300] run fill ~-5 71 ~-5 ~5 71 ~5 moving_piston replace air 
item modify entity Ngoc_Cutee weapon.mainhand practicebot:refill