{"uuid": "bf6efa9d-f8d4-4f38-8f6c-851fba5fac38", "launcher": {"name": "Minecraft 1.21.1 with <PERSON><PERSON><PERSON>", "pack": "Minecraft", "description": "Minecraft 1.21.1 with <PERSON><PERSON><PERSON>", "packId": 0, "externalPackId": 0, "version": "1.21.1", "enableCurseForgeIntegration": true, "enableEditingMods": true, "loaderVersion": {"version": "0.16.10", "rawVersion": "0.16.10", "recommended": false, "type": "<PERSON><PERSON><PERSON>", "downloadables": {}}, "requiredMemory": 0, "requiredPermGen": 0, "quickPlay": {}, "isDev": false, "isPlayable": true, "assetsMapToResources": false, "overridePaths": [], "checkForUpdates": true, "mods": [{"name": "Mouse Tweaks", "version": "[1.21+ <PERSON><PERSON><PERSON>] Mouse Tweaks 2.26", "optional": true, "file": "MouseTweaks-fabric-mc1.21-2.26.jar", "type": "mods", "description": "Enhances inventory management by adding various functions to the mouse buttons. ", "disabled": false, "userAdded": true, "wasSelected": true, "skipped": false, "curseForgeProjectId": 60089, "curseForgeFileId": 5437294, "curseForgeProject": {"id": 60089, "name": "Mouse Tweaks", "authors": [{"id": 7495712, "name": "YaLTeR", "url": "https://www.curseforge.com/members/yalter"}], "gameId": 432, "summary": "Enhances inventory management by adding various functions to the mouse buttons.", "categories": [{"name": "Miscellaneous", "slug": "mc-miscellaneous", "url": "https://www.curseforge.com/minecraft/mc-mods/mc-miscellaneous", "dateModified": "2014-06-15T04:27:14.543Z", "gameId": 432, "isClass": false, "id": 425, "iconUrl": "https://media.forgecdn.net/avatars/6/40/635351497693711265.png", "parentCategoryId": 6, "classId": 6}, {"name": "Utility & QoL", "slug": "utility-qol", "url": "https://www.curseforge.com/minecraft/mc-mods/utility-qol", "dateModified": "2021-11-17T11:45:09.143Z", "gameId": 432, "isClass": false, "id": 5191, "iconUrl": "https://media.forgecdn.net/avatars/456/558/637727379086405233.png", "parentCategoryId": 6, "classId": 6}, {"name": "Storage", "slug": "storage", "url": "https://www.curseforge.com/minecraft/mc-mods/storage", "dateModified": "2014-05-08T17:41:17.203Z", "gameId": 432, "isClass": false, "id": 420, "iconUrl": "https://media.forgecdn.net/avatars/6/35/635351496772023801.png", "parentCategoryId": 6, "classId": 6}], "status": 4, "primaryCategoryId": 425, "classId": 6, "slug": "mouse-tweaks", "isFeatured": false, "dateModified": "2025-06-24T18:23:13.9Z", "dateCreated": "2013-06-25T13:13:55.087Z", "dateReleased": "2025-06-24T18:18:53.003Z", "links": {"websiteUrl": "https://www.curseforge.com/minecraft/mc-mods/mouse-tweaks", "wikiUrl": "", "issuesUrl": "https://github.com/YaLTeR/MouseTweaks/issues", "sourceUrl": "https://github.com/YaLTeR/MouseTweaks"}, "logo": {"id": 467691, "description": "", "thumbnailUrl": "https://media.forgecdn.net/avatars/thumbnails/467/691/256/256/637749969243449870.png", "title": "637749969243449870.png", "url": "https://media.forgecdn.net/avatars/467/691/637749969243449870.png", "modId": 60089}, "allowModDistribution": true, "screenshots": [{"id": 10571, "description": "A default image for Mouse Tweaks.", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/10/571/310/172/cursebanner.png", "title": "Default Image", "url": "https://media.forgecdn.net/attachments/10/571/cursebanner.png", "modId": 60089}], "mainFileId": 6691723}, "curseForgeFile": {"id": 5437294, "gameId": 432, "isAvailable": true, "displayName": "[1.21+ <PERSON><PERSON><PERSON>] Mouse Tweaks 2.26", "fileName": "MouseTweaks-fabric-mc1.21-2.26.jar", "releaseType": 1, "fileStatus": 4, "fileDate": "2024-06-17T08:20:49.25Z", "fileLength": 75984, "dependencies": [{"fileId": 0, "modId": 308702, "relationType": 2}, {"fileId": 0, "modId": 306612, "relationType": 3}], "alternateFileId": 0, "modules": [{"fingerprint": **********, "name": "META-INF"}, {"fingerprint": 1335286126, "name": "fabric.mod.json"}, {"fingerprint": 1801918641, "name": "mousetweaks-fabric.mixins.json"}, {"fingerprint": 33962436, "name": "mousetweaks.mixins.json"}, {"fingerprint": 2577315203, "name": "mousetweaks.mixins.refmap.json"}, {"fingerprint": 1781016577, "name": "mousetweaks_logo.png"}, {"fingerprint": 2140027510, "name": "pack.mcmeta"}, {"fingerprint": 844545168, "name": "yalter"}], "isServerPack": false, "hashes": [{"value": "ee81af33a5606ef2cd057e075833d7c69836c210", "algo": 1}, {"value": "1d39ead1641ef522bc7ae92383f21da8", "algo": 2}], "sortableGameVersions": [{"gameVersionPadded": "**********.**********", "gameVersion": "1.21", "gameVersionReleaseDate": "2024-06-14T00:00:00Z", "gameVersionName": "1.21"}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-09-01T00:00:00Z", "gameVersionName": "<PERSON><PERSON><PERSON>"}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-12-08T00:00:00Z", "gameVersionName": "Client"}, {"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.1", "gameVersionReleaseDate": "2024-08-08T15:17:53.787Z", "gameVersionName": "1.21.1"}], "gameVersions": ["1.21", "<PERSON><PERSON><PERSON>", "Client", "1.21.1"], "fileFingerprint": 717601083, "modId": 60089}, "modrinthProject": {"id": "aC3cM3Vq", "slug": "mouse-tweaks", "project_type": "mod", "team": "Gn6mAc1d", "title": "Mouse Tweaks", "description": "Enhances inventory management by adding various functions to the mouse buttons. ", "body": "Mouse Tweaks replaces the standard RMB dragging mechanic, adds two new LMB dragging mechanics and an ability to quickly move items with the scroll wheel.\n\n## Installation\n\n1. Install a compatible mod loader:\n    - 1.16.5 and later: install Minecraft Forge or Fabric.\n    - 1.14.4 and later: install Minecraft Forge.\n    - 1.12.2 and earlier: install either Minecraft Forge or LiteLoader (or both).\n2. Put the Mouse Tweaks jar-file into the mods folder in your .minecraft directory.\n\nMouse Tweaks is purely client-side. There is no server-side component.\n\n### Extras\n\n[**Mouse Tweaks API**](https://github.com/YaLTeR/MouseTweaks/tree/master/src/main/java/yalter/mousetweaks/api)\n\nConfiguration file: `.minecraft/config/MouseTweaks.cfg`\n\n## Tweaks\n\n### RMB Tweak\n\nVery similar to the standard RMB dragging mechanic, with one difference: if you drag over a slot multiple times, an item will be put there multiple times. Replaces the standard mechanic if enabled.\n\n**Configuration setting:** `RMBTweak=1`\n\nHold your right mouse button:\n\n![](https://i.imgur.com/Uo7xF.png)\n\nDrag your mouse around the crafting grid:\n\n![](https://i.imgur.com/NCRED.png)\n\nYou can drag your mouse on top of existing items:\n\n![](https://i.imgur.com/6MQv6.png)\n\n### LMB Tweak (with item)\n\nLets you quickly pick up or move items of the same type.\n\n**Configuration setting:** `LMBTweakWithItem=1`\n\nHold your left mouse button to pick up an item:\n\n![](https://i.imgur.com/ziuGG.png?1)\n\nDrag your mouse across the inventory. Items of the same type will be picked up:\n\n![](https://i.imgur.com/JDjsE.png?2)\n\nHold shift and drag. Items of the same type will get \"shift-clicked\":\n\n![](https://i.imgur.com/YrvmT.png?2)\n\n### LMB Tweak (without item)\n\nQuickly move items into another inventory.\n\n**Configuration setting:** `LMBTweakWithoutItem=1`\n\nHold shift, then hold your left mouse button:\n\n*(Mouse cursor is not visible for some reason)*\n\n![](https://i.imgur.com/f9Ejp.png?1)\n\nDrag your mouse across the inventory. Items will get \"shift-clicked\":\n\n*(Mouse cursor is not visible for some reason)*\n\n![](https://i.imgur.com/qBu6k.png?2)\n\n### Wheel Tweak\n\nScroll to quickly move items between inventories. When you scroll down on an item stack, its items will be moved one by one. When you scroll up, items will be moved into it from another inventory.\n\n**Configuration setting:** `WheelTweak=1`\n\n**Configuration setting:** `WheelSearchOrder=1`\n\nWhen you scroll up, the mod will search for items from last to first (when this is set to 1) or from first to last (when this is set to 0).\n\n**Configuration setting:** `WheelScrollDirection=0`\n\nSet this to 1 to invert the default scroll actions. So, when set to 1, scrolling down will pull the items and scrolling up will push the items.\n\nSet this to 2 to enable the inventory position aware scrolling. Scrolling up will push the items into the other inventory if it's above the selected slot, or pull items from the other inventory if it's below the selected slot. Vice versa for scrolling down.\n\n### Obsolete / Removed Settings\n\nThese settings existed in older Mouse Tweaks versions but were removed since.\n\n**Configuration setting:** `OnTickMethodOrder=Forge, LiteLoader`\n\nMouse Tweaks can use multiple APIs for an OnTick method that it requires. You can use this setting to control the API it prefers. This shouldn't really matter at all. If a method isn't supported (for example, you don't have the API installed) the mod will proceed to check the next ones.\n\n**Configuration setting:** `ScrollHandling=0`\n\nToggles between \"smooth scrolling, minor issues\" (0) and \"non-smooth scrolling, no issues\" (1). When set to smooth scrolling, minor issues may be experienced such as scrolling \"through\" JEI or other mods. When set to non-smooth scrolling, those issues will not happen, but the scrolling will be a little non-smooth. Non-smooth scrolling works only with the Forge OnTick method.\n\nThis option is set to smooth scrolling by default because the aforementioned issues require rather specific conditions to trigger and aren't very impactful, while scrolling items is something you do all the time and want the experience to be as good as possible.\n\n## Compatibility\n\nMouse Tweaks is compatible with everything based on `GuiContainer` (as long as the behavior isn't changed too much).\n\nIf your GUI isn't based on `GuiContainer`, or if you want to improve compatibility (making Mouse Tweaks ignore some slot, for example), take a look at the [API documentation](https://github.com/YaLTeR/MouseTweaks/blob/2dc5bb108c2663f9a07b3a181483733a0274b41a/src/api/java/yalter/mousetweaks/api/IMTModGuiContainer3.java).\n\n## Modpacks\n\nFeel free to include Mouse Tweaks in modpacks.\n", "published": "2021-03-20T15:55:49.895120Z", "updated": "2025-06-24T18:19:48.140038Z", "status": "approved", "license": {"id": "BSD-3-<PERSON><PERSON>", "name": "BSD 3-Clause \"New\" or \"Revised\" License"}, "client_side": "required", "server_side": "unsupported", "categories": ["storage", "utility"], "loaders": ["fabric", "forge", "modloader", "neoforge"], "icon_url": "https://cdn.modrinth.com/data/aC3cM3Vq/6c0eaa4e60a9c87f4766f222ff63286f09da32c0_96.webp", "issues_url": "https://github.com/YaLTeR/MouseTweaks/issues", "source_url": "https://github.com/YaLTeR/MouseTweaks", "donation_urls": [{"id": "github", "platform": "<PERSON><PERSON><PERSON>", "url": "https://github.com/sponsors/YaLTeR"}], "gallery": []}, "modrinthVersion": {"id": "ylmBQ38A", "project_id": "aC3cM3Vq", "author_id": "On32SQVu", "name": "[1.21+ <PERSON><PERSON><PERSON>] Mouse Tweaks 2.26", "version_number": "1.21-2.26-fabric", "changelog": "- Updated to Minecraft 1.21.", "date_published": "2024-06-17T08:24:47.086399Z", "version_type": "release", "files": [{"hashes": {"sha512": "1744a48a47aedcbf19a0a93f78473cf0221fc4782852dca7fc02685719174664b4f9d95d353fcfc16902ac3815594511ba6d9ab14391f9b7e25ec9b2e777927a", "sha1": "ee81af33a5606ef2cd057e075833d7c69836c210"}, "url": "https://cdn.modrinth.com/data/aC3cM3Vq/versions/ylmBQ38A/MouseTweaks-fabric-mc1.21-2.26.jar", "filename": "MouseTweaks-fabric-mc1.21-2.26.jar", "primary": true, "size": 75984}, {"hashes": {"sha512": "643d0e77709ccfd4c22a37f1ced073834cce3261af52bff09fd53832a9eb5dcbb276be18300e1a66fe22a0f517f7d3220e40fca88f6c3ce5afcbeb771a0c2e5d", "sha1": "e4edaff414f34f397a25c18c7a578bc88249d494"}, "url": "https://cdn.modrinth.com/data/aC3cM3Vq/versions/ylmBQ38A/MouseTweaks-fabric-mc1.21-2.26-sources.jar", "filename": "MouseTweaks-fabric-mc1.21-2.26-sources.jar", "primary": false, "size": 65179}], "dependencies": [{"project_id": "P7dR8mSH", "dependency_type": "required"}, {"project_id": "mOgUt4GM", "dependency_type": "optional"}], "game_versions": ["1.21", "1.21.1"], "loaders": ["fabric"]}}, {"name": "AppleSkin", "version": "appleskin-fabric-mc1.21-3.0.6.jar", "optional": true, "file": "appleskin-fabric-mc1.21-3.0.6.jar", "type": "mods", "description": "Food/hunger-related HUD improvements", "disabled": false, "userAdded": true, "wasSelected": true, "skipped": false, "curseForgeProjectId": 248787, "curseForgeFileId": 5864741, "curseForgeProject": {"id": 248787, "name": "AppleSkin", "authors": [{"id": 13914149, "name": "squeek502", "url": "https://www.curseforge.com/members/squeek502"}], "gameId": 432, "summary": "Adds some useful information about food/hunger to the HUD", "categories": [{"name": "Map and Information", "slug": "map-information", "url": "https://www.curseforge.com/minecraft/mc-mods/map-information", "dateModified": "2014-05-08T17:42:23.74Z", "gameId": 432, "isClass": false, "id": 423, "iconUrl": "https://media.forgecdn.net/avatars/6/38/635351497437388438.png", "parentCategoryId": 6, "classId": 6}, {"name": "Food", "slug": "mc-food", "url": "https://www.curseforge.com/minecraft/mc-mods/mc-food", "dateModified": "2014-05-08T17:45:26.55Z", "gameId": 432, "isClass": false, "id": 436, "iconUrl": "https://media.forgecdn.net/avatars/6/49/635351499265510402.png", "parentCategoryId": 6, "classId": 6}], "status": 4, "primaryCategoryId": 436, "classId": 6, "slug": "appleskin", "isFeatured": false, "dateModified": "2025-06-22T00:02:40.58Z", "dateCreated": "2016-08-13T19:00:16.187Z", "dateReleased": "2025-06-21T23:58:02.273Z", "links": {"websiteUrl": "https://www.curseforge.com/minecraft/mc-mods/appleskin", "wikiUrl": "", "issuesUrl": "https://github.com/squeek502/AppleSkin/issues", "sourceUrl": "https://github.com/squeek502/AppleSkin"}, "logo": {"id": 47527, "description": "", "thumbnailUrl": "https://media.forgecdn.net/avatars/thumbnails/47/527/256/256/636066936394500688.png", "title": "636066936394500688.png", "url": "https://media.forgecdn.net/avatars/47/527/636066936394500688.png", "modId": 248787}, "allowModDistribution": true, "screenshots": [], "mainFileId": 6680857}, "curseForgeFile": {"id": 5864741, "gameId": 432, "isAvailable": true, "displayName": "appleskin-fabric-mc1.21-3.0.6.jar", "fileName": "appleskin-fabric-mc1.21-3.0.6.jar", "releaseType": 2, "fileStatus": 4, "fileDate": "2024-11-01T06:14:34.05Z", "fileLength": 1082585, "dependencies": [{"fileId": 0, "modId": 306612, "relationType": 3}], "alternateFileId": 0, "modules": [{"fingerprint": 382771236, "name": "META-INF"}, {"fingerprint": 589621802, "name": "LICENSE_appleskin-fabric"}, {"fingerprint": **********, "name": "appleskin-fabric-refmap.json"}, {"fingerprint": **********, "name": "appleskin.jei.mixins.json"}, {"fingerprint": **********, "name": "appleskin.mixins.json"}, {"fingerprint": **********, "name": "assets"}, {"fingerprint": **********, "name": "fabric.mod.json"}, {"fingerprint": **********, "name": "pack.mcmeta"}, {"fingerprint": 925037932, "name": "squeek"}], "isServerPack": false, "hashes": [{"value": "97452cfadfde1f8f8c67838643019eabafa58fbb", "algo": 1}, {"value": "a24c435e84973afd607e20395ecba19d", "algo": 2}], "sortableGameVersions": [{"gameVersionPadded": "**********.**********", "gameVersion": "1.21", "gameVersionReleaseDate": "2024-06-14T00:00:00Z", "gameVersionName": "1.21"}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-09-01T00:00:00Z", "gameVersionName": "<PERSON><PERSON><PERSON>"}, {"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.1", "gameVersionReleaseDate": "2024-08-08T15:17:53.787Z", "gameVersionName": "1.21.1"}], "gameVersions": ["1.21", "<PERSON><PERSON><PERSON>", "1.21.1"], "fileFingerprint": 604462130, "modId": 248787}, "modrinthProject": {"id": "EsAfCjCV", "slug": "appleskin", "project_type": "mod", "team": "2RI7njA8", "title": "AppleSkin", "description": "Food/hunger-related HUD improvements", "body": "Adds various food-related HUD improvements. This is a mostly client-side mod, but it needs to be on the server in order to display accurate saturation/exhaustion values on the client.\n\n---\n\n*Note: AppleSkin provides information about some mechanics that are invisible by default (it does not add or change any mechanics). To read about Minecraft's exhaustion and saturation mechanics, see the [Minecraft wiki article on Hunger](https://minecraft.wiki/w/Hunger#Mechanics)*\n\n---\n\n### Features\n\n* Adds food value information to tooltips:\n\n![](https://i.imgur.com/YksBaUx.png)\n\n* Adds a visualization of saturation and exhaustion to the HUD:\n\n![](https://i.imgur.com/tmImVqo.gif)\n\n* Adds a visualization of potential hunger/saturation restored while holding food:\n\n![](https://i.imgur.com/aHf1QxQ.gif)\n\n* Adds a visualization of potential health restored while holding food:\n\n![](https://i.imgur.com/jUOKFUl.gif)\n\n* Adds hunger/saturation/exhaustion info to the debug overlay (F3)\n* Syncs the value of saturation and exhaustion to the client.\n\n---\n\n### Misc\nYou are welcome to add this mod to any modpack you'd like.\n\n---\n\n### Use on vanilla servers\n\nOne of the following plugins can be used to get the saturation/exhaustion to work when connecting to vanilla Minecraft servers:\n\n- https://github.com/jmattingley23/AppleSkinSpigot\n- https://www.curseforge.com/minecraft/bukkit-plugins/paperapple\n", "published": "2021-11-20T23:37:39.568172Z", "updated": "2025-06-21T23:56:51.759781Z", "status": "approved", "license": {"id": "Unlicense", "name": "The Unlicense"}, "client_side": "optional", "server_side": "optional", "categories": ["food", "utility"], "loaders": ["fabric", "forge", "neoforge", "quilt"], "icon_url": "https://cdn.modrinth.com/data/EsAfCjCV/icon.png", "issues_url": "https://github.com/squeek502/AppleSkin/issues", "source_url": "https://github.com/squeek502/AppleSkin", "donation_urls": [{"id": "github", "platform": "<PERSON><PERSON><PERSON>", "url": "https://github.com/sponsors/squeek502"}], "gallery": []}, "modrinthVersion": {"id": "b5ZiCjAr", "project_id": "EsAfCjCV", "author_id": "qB1TNdUc", "name": "appleskin-fabric-mc1.21-3.0.6", "version_number": "3.0.6+mc1.21", "changelog": "Fixed AppleSkin tooltips not showing up in the JEI side pane", "date_published": "2024-11-01T06:22:22.888816Z", "version_type": "release", "files": [{"hashes": {"sha1": "97452cfadfde1f8f8c67838643019eabafa58fbb", "sha512": "accbb36b863bdeaaeb001f7552534f3bdf0f27556795cf8e813f9b32e7732450ec5133da5e0ec9b92dc22588c48ffb61577c375f596dc351f15c15ce6a6f4228"}, "url": "https://cdn.modrinth.com/data/EsAfCjCV/versions/b5ZiCjAr/appleskin-fabric-mc1.21-3.0.6.jar", "filename": "appleskin-fabric-mc1.21-3.0.6.jar", "primary": true, "size": 1082585}], "dependencies": [{"project_id": "P7dR8mSH", "dependency_type": "required"}], "game_versions": ["1.21", "1.21.1", "1.21.2"], "loaders": ["fabric"]}}, {"name": "Nvidium", "version": "Nvidium 0.3.1", "optional": true, "file": "nvidium-0.3.1.jar", "type": "mods", "description": "Fast nvidia only rendering engine for sodium", "disabled": false, "userAdded": true, "wasSelected": true, "skipped": false, "modrinthProject": {"id": "SfMw2IZN", "slug": "nvidium", "project_type": "mod", "team": "JbFXqWoE", "title": "Nvidium", "description": "Fast nvidia only rendering engine for sodium", "body": "![Nvidium rendering huge amounts of terrain geometry](https://i.imgur.com/k8A1buX.jpg)\n\nNvidium is a replacement rendering backend for sodium that uses nvidia only opengl extensions to increase fps by a significant amount.\n\n## Compatibility\nThis mod explicitly requires an nvidia gpu that supports mesh shaders. \nThis feature was introduced in turing architecture, anything that is a 16xx series or newer (20xx series also works) that supports mesh shaders. \n\n### Q: Will this mod work on my non-Nvidia system?\n### A: No, the mod is not functional on non-Nvidia systems, but the Nvidium will automatically disable itself. Your gameplay will not be affected. \n\n## Warning\nThis mod uses uncommon technology (mesh shaders). This may result in minecraft terminating unexpectedly.\n\n## How does it work\nWith mesh shaders, a near fully gpu driven rendering pipeline is used, enabling very fast and performant geometry culling of terrain meaning your gpu can work much more efficiently.\n\n# Requires sodium to run\n## Disables itself when iris is actively using shaders\n\n\n", "published": "2023-04-16T05:01:19.380796Z", "updated": "2024-09-03T14:46:04.720980Z", "status": "approved", "license": {"id": "LGPL-3.0-only", "name": "GNU Lesser General Public License v3.0 only"}, "client_side": "required", "server_side": "unsupported", "categories": ["optimization"], "loaders": ["fabric", "quilt"], "icon_url": "https://cdn.modrinth.com/data/SfMw2IZN/2db76d464a0f67cdb9e30fd99040eb096ac62016_96.webp", "issues_url": "https://github.com/MCRcortex/nvidium/issues", "source_url": "https://github.com/MCRcortex/nvidium", "discord_url": "https://discord.gg/VdhYSFxtGa", "donation_urls": [{"id": "patreon", "platform": "Patreon", "url": "https://patreon.com/mcrcortex/"}], "gallery": [{"url": "https://cdn.modrinth.com/data/SfMw2IZN/images/843f9eb9fe3b4f892ea4640dcfa978b36f33a180_350.webp", "raw_url": "https://cdn.modrinth.com/data/SfMw2IZN/images/b29744d633f6faac9a4b3c0cc88d740a57a0629f.png", "featured": false, "title": "Updated nvidium ", "description": "Updated nvidium with mipping and memory culling", "created": "2023-05-01T04:54:26.844329Z", "ordering": -1.0}, {"url": "https://cdn.modrinth.com/data/SfMw2IZN/images/197be2e0e47e64062408897e78e4fff912aef204_350.webp", "raw_url": "https://cdn.modrinth.com/data/SfMw2IZN/images/457b8ac2059b035d6a2465639692ea65aa78e793.jpeg", "featured": false, "title": "Far rendering", "description": "Nvidium with chunk unloading disabled", "created": "2023-04-17T15:15:12.070333Z", "ordering": 0.0}, {"url": "https://cdn.modrinth.com/data/SfMw2IZN/images/6f47ea10baa32758a539b4ba4695ec3cf4b81666_350.webp", "raw_url": "https://cdn.modrinth.com/data/SfMw2IZN/images/53e7002010cb8b73d31bd4c74fadbda61b60a847.png", "featured": false, "title": "Terrain rendering", "description": "Far terrain rendering", "created": "2023-04-23T11:55:41.891758Z", "ordering": 0.0}, {"url": "https://cdn.modrinth.com/data/SfMw2IZN/images/9a5843b6c9b58e06674e162c5eac85dc6b1d2da9_350.webp", "raw_url": "https://cdn.modrinth.com/data/SfMw2IZN/images/d2d24a75bb2e496a54edd89ad9498773605ca72b.jpeg", "featured": true, "title": "Hermitcraft season 8", "description": "Hermitcraft season 8 world rendered in realtime at 80fps @4k", "created": "2023-07-30T12:30:16.279570Z", "ordering": 0.0}]}, "modrinthVersion": {"id": "3L83QwKZ", "project_id": "SfMw2IZN", "author_id": "f45kBWbv", "name": "Nvidium 0.3.1", "version_number": "0.3.1", "changelog": "### NOT compatible with sodium 0.6.0\n\n* Up to 35% performance improvement\n  * Removed vertex attributes and implemented quad pulling in fragment shader\n  * Implemented subpixel micro-triangle culling \n\n* Fixed lighting being brighter than vanilla/sodium\n* Fixed issue where memory could not be unloaded fast enough leading to crash\n* Fixed world icon not generating\n* Fixed accidental unmarking of non resident buffer in PersistentClientMappedBuffer\n* Fixed issues with lambdadynamiclights\n* More error tracking and logging\n* Added commit hash to f3\n* Cleaned up f3 info\n* Improved quad lighting precision\n* Respect sodium block face culling option", "date_published": "2024-09-03T14:46:04.720980Z", "version_type": "release", "files": [{"hashes": {"sha1": "06ebc672c1bc833b674427d66c3d64036e1e4dcd", "sha512": "1da6ab0a179dd18035139e4562cd8921803fbeff9ac7a2e6af216c4ab4b6baecf755970a4b75ebfa645bac74bdfc3d8d9565419f5d401fda844ff4201b965413"}, "url": "https://cdn.modrinth.com/data/SfMw2IZN/versions/3L83QwKZ/nvidium-0.3.1.jar", "filename": "nvidium-0.3.1.jar", "primary": true, "size": 158963}], "dependencies": [{"version_id": "RncWhTxD", "project_id": "AANobbMI", "dependency_type": "required"}], "game_versions": ["1.21", "1.21.1"], "loaders": ["fabric", "quilt"]}}, {"name": "FPS Reducer", "version": "FPS Reducer fabric 1.21-2.9", "optional": true, "file": "FpsReducer2-fabric-1.21-2.9.jar", "type": "mods", "description": "Reduce GPU and CPU usage automatically when no user operation exists.", "disabled": false, "userAdded": true, "wasSelected": true, "skipped": false, "curseForgeProjectId": 280294, "curseForgeFileId": 5427187, "curseForgeProject": {"id": 280294, "name": "FPS Reducer", "authors": [{"id": 34993322, "name": "bre2el", "url": "https://www.curseforge.com/members/bre2el"}], "gameId": 432, "summary": "Reduce GPU and CPU usage automatically when no user operation exists.", "categories": [{"name": "Utility & QoL", "slug": "utility-qol", "url": "https://www.curseforge.com/minecraft/mc-mods/utility-qol", "dateModified": "2021-11-17T11:45:09.143Z", "gameId": 432, "isClass": false, "id": 5191, "iconUrl": "https://media.forgecdn.net/avatars/456/558/637727379086405233.png", "parentCategoryId": 6, "classId": 6}, {"name": "Miscellaneous", "slug": "mc-miscellaneous", "url": "https://www.curseforge.com/minecraft/mc-mods/mc-miscellaneous", "dateModified": "2014-06-15T04:27:14.543Z", "gameId": 432, "isClass": false, "id": 425, "iconUrl": "https://media.forgecdn.net/avatars/6/40/635351497693711265.png", "parentCategoryId": 6, "classId": 6}], "status": 4, "primaryCategoryId": 425, "classId": 6, "slug": "fps-reducer", "isFeatured": false, "dateModified": "2025-06-21T22:16:07.533Z", "dateCreated": "2017-10-20T15:19:56.06Z", "dateReleased": "2025-06-21T21:48:33.777Z", "links": {"websiteUrl": "https://www.curseforge.com/minecraft/mc-mods/fps-reducer", "wikiUrl": ""}, "logo": {"id": 127648, "description": "", "thumbnailUrl": "https://media.forgecdn.net/avatars/thumbnails/127/648/256/256/636440915960912895.png", "title": "636440915960912895.png", "url": "https://media.forgecdn.net/avatars/127/648/636440915960912895.png", "modId": 280294}, "allowModDistribution": true, "screenshots": [], "mainFileId": 6680545}, "curseForgeFile": {"id": 5427187, "gameId": 432, "isAvailable": true, "displayName": "FpsReducer2-fabric-1.21-2.9.jar", "fileName": "FpsReducer2-fabric-1.21-2.9.jar", "releaseType": 1, "fileStatus": 4, "fileDate": "2024-06-14T09:41:56.473Z", "fileLength": 124879, "dependencies": [{"fileId": 0, "modId": 306612, "relationType": 3}], "alternateFileId": 0, "modules": [{"fingerprint": **********, "name": "META-INF"}, {"fingerprint": 985183819, "name": "FpsReducer2-fabric-refmap.json"}, {"fingerprint": 1863686335, "name": "assets"}, {"fingerprint": 312815117, "name": "bre2el"}, {"fingerprint": 4122016140, "name": "fabric.mod.json"}, {"fingerprint": 1303396050, "name": "fpsreducer.accesswidener"}, {"fingerprint": 208568491, "name": "fpsreducer.json"}], "isServerPack": false, "hashes": [{"value": "9219d7f657f4adb61315cd15023265d1070d24ca", "algo": 1}, {"value": "a8520cd58f64490f15fdb7195992f5a1", "algo": 2}], "sortableGameVersions": [{"gameVersionPadded": "**********.**********", "gameVersion": "1.21", "gameVersionReleaseDate": "2024-06-14T00:00:00Z", "gameVersionName": "1.21"}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-09-01T00:00:00Z", "gameVersionName": "<PERSON><PERSON><PERSON>"}, {"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.1", "gameVersionReleaseDate": "2024-08-08T15:17:53.787Z", "gameVersionName": "1.21.1"}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-05-26T00:00:00Z", "gameVersionName": "Quilt"}], "gameVersions": ["1.21", "<PERSON><PERSON><PERSON>", "1.21.1", "Quilt"], "fileFingerprint": 2101455249, "modId": 280294}, "modrinthProject": {"id": "iZ10HXDj", "slug": "fps-reducer", "project_type": "mod", "team": "nlZ5ReeL", "title": "FPS Reducer", "description": "Reduce GPU and CPU usage automatically when no user operation exists.", "body": "## Description\nThis mod reduces unnecessary GPU and CPU load by reducing the frame rate automatically when the client window is inactive or you are not operating the Minecraft for a certain period of time (optional from v2.0). As soon as the user performs any operation, it returns to the original frame rate.\n\n## Features\n- Reduce FPS automatically under the following conditions.\n    - No user operation for the specified time.\n    - The game window is inactive or minimized.\n- Suppress sound volume if the game window is inactive or minimized.\n- Display the current FPS on the screen edge.\\\n    ![Current FPS indication](https://i.imgur.com/Yc2EwAH.jpg)\n- Display CPU usage of client thread. (v1.14~)\\\n    ![CPU usage indication](https://i.imgur.com/CkuuwJj.png)\n\n## Recommended in the following cases\n- When leaving the game running without the operation, for example with automated agriculture and/or industry.\n- When doing other work concurrently with running Minecraft in background.\n- When you want to extend the lifespan of your PC even a little.\n- When you want to save unnecessary power.\n- When the room is hot...\n\n## Usage\nBy default, if there is no operation for 5 minutes the frame rate will drop to 10.\\\nPressing [PAUSE] key reduces FPS manually. (new feature in v1.16)\\\nPressing [END] key opens the configuration GUI by default.\\\n![Mod configuration GUI](https://i.imgur.com/aHob2HN.jpg)\n\n## Note\nThis is a client-side mod, so it is not necessary to install it on the server.\n\n## Modpacks\nFeel free to use this mod in any modpacks.\n\n## Special Thanks\n- Thanks to RoCoKo for Turkish translations.\n- Thanks to Grreg21 for Polish translations.\n- Thanks to cutedobe and StarsShine17423 for Simplified Chinese translation.\n- Thanks to cutedobe and StarsShine17423 for Traditional Chinese translation.\n- Thanks to agent_rby_ and Secret_Rabbit for Russian translation.\n- Thanks to b14st0 for French translation.\n- Thanks to Kazuya79H for Brazilian Portuguese translation.\n- Thanks to AlexDerProGamer_mc for German translation.\n- Thanks to 귤러리 for Korean translation.\n- Thanks to AceDragoonOfficial for Ukrainian translation.\n- Thanks to Giedo for Dutch translation.\n- Thanks to iwafflebro for Spanish translation.\n- Thanks to 000729735256 for Classical Chinese translation.", "published": "2023-09-06T12:46:51.493326Z", "updated": "2025-06-21T21:49:39.227352Z", "status": "approved", "license": {"id": "LicenseRef-All-Rights-Reserved", "name": ""}, "client_side": "required", "server_side": "unsupported", "categories": ["optimization"], "loaders": ["fabric", "forge", "neoforge", "quilt"], "icon_url": "https://cdn.modrinth.com/data/iZ10HXDj/3d82fc3ce8a943d8065a4b5532f932dba144e128_96.webp", "issues_url": "https://legacy.curseforge.com/minecraft/mc-mods/fps-reducer/issues", "donation_urls": [], "gallery": []}, "modrinthVersion": {"id": "9ewqcOwy", "project_id": "iZ10HXDj", "author_id": "c9EEp7Fr", "name": "FPS Reducer fabric 1.21-2.9", "version_number": "1.21-2.9", "changelog": "v2.9\n- Initial release for 1.21 fabric.", "date_published": "2024-06-14T09:43:49.419935Z", "version_type": "release", "files": [{"hashes": {"sha1": "9219d7f657f4adb61315cd15023265d1070d24ca", "sha512": "b4f5c1192c0756ee9226b6cdfa230c49efa5049c9570a9c3f5e7d14250716600e9cc630fe4cf4c26334cc09bfb97705a4f785ad64d7262c1b6394de508c2423a"}, "url": "https://cdn.modrinth.com/data/iZ10HXDj/versions/9ewqcOwy/FpsReducer2-fabric-1.21-2.9.jar", "filename": "FpsReducer2-fabric-1.21-2.9.jar", "primary": true, "size": 124879}], "dependencies": [{"project_id": "P7dR8mSH", "dependency_type": "required"}], "game_versions": ["1.21", "1.21.1"], "loaders": ["fabric", "quilt"]}}, {"name": "Indium", "version": "Indium 1.0.35 for Minecraft 1.21.x/Sodium 0.5.11", "optional": true, "file": "indium-1.0.35+mc1.21.jar", "type": "mods", "description": "Sodium addon providing support for the Fabric Rendering API, based on Indigo", "disabled": false, "userAdded": true, "wasSelected": true, "skipped": false, "curseForgeProjectId": 459496, "curseForgeFileId": 5632454, "curseForgeProject": {"id": 459496, "name": "Indium", "authors": [{"id": 9006466, "name": "comp500", "url": "https://www.curseforge.com/members/comp500"}], "gameId": 432, "summary": "Sodium addon providing support for the Fabric Rendering API, based on Indigo", "categories": [{"name": "API and Library", "slug": "library-api", "url": "https://www.curseforge.com/minecraft/mc-mods/library-api", "dateModified": "2014-05-23T03:21:44.06Z", "gameId": 432, "isClass": false, "id": 421, "iconUrl": "https://media.forgecdn.net/avatars/6/36/635351496947765531.png", "parentCategoryId": 6, "classId": 6}, {"name": "Addons", "slug": "mc-addons", "url": "https://www.curseforge.com/minecraft/mc-mods/mc-addons", "dateModified": "2014-05-08T17:09:48.63Z", "gameId": 432, "isClass": false, "id": 426, "iconUrl": "https://media.forgecdn.net/avatars/5/998/635351477886290676.png", "parentCategoryId": 6, "classId": 6}], "status": 4, "primaryCategoryId": 426, "classId": 6, "slug": "indium", "isFeatured": false, "dateModified": "2025-02-25T22:54:08.807Z", "dateCreated": "2021-03-19T04:07:54.103Z", "dateReleased": "2025-02-25T22:48:50.77Z", "links": {"websiteUrl": "https://www.curseforge.com/minecraft/mc-mods/indium", "wikiUrl": "", "issuesUrl": "https://github.com/comp500/Indium/issues", "sourceUrl": "https://github.com/comp500/Indium"}, "logo": {"id": 408469, "description": "", "thumbnailUrl": "https://media.forgecdn.net/avatars/thumbnails/408/469/256/256/637622340354039262.png", "title": "637622340354039262.png", "url": "https://media.forgecdn.net/avatars/408/469/637622340354039262.png", "modId": 459496}, "allowModDistribution": true, "screenshots": [], "mainFileId": 6237037}, "curseForgeFile": {"id": 5632454, "gameId": 432, "isAvailable": true, "displayName": "Indium 1.0.35 for Minecraft 1.21.x/Sodium 0.5.11", "fileName": "indium-1.0.35+mc1.21.jar", "releaseType": 1, "fileStatus": 4, "fileDate": "2024-08-15T17:24:26.51Z", "fileLength": 105000, "dependencies": [{"fileId": 0, "modId": 394468, "relationType": 3}], "alternateFileId": 0, "modules": [{"fingerprint": **********, "name": "META-INF"}, {"fingerprint": **********, "name": "LICENSE"}, {"fingerprint": **********, "name": "assets"}, {"fingerprint": **********, "name": "fabric.mod.json"}, {"fingerprint": **********, "name": "indium-refmap.json"}, {"fingerprint": **********, "name": "indium.mixins.json"}, {"fingerprint": 769237869, "name": "link"}], "isServerPack": false, "hashes": [{"value": "1bf4b5db91f4e6d7a4f94fe2e96525d22df1cacb", "algo": 1}, {"value": "db92c620cb72a41403b928a0b45955d0", "algo": 2}], "sortableGameVersions": [{"gameVersionPadded": "**********.**********", "gameVersion": "1.21", "gameVersionReleaseDate": "2024-06-14T00:00:00Z", "gameVersionName": "1.21"}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-09-01T00:00:00Z", "gameVersionName": "<PERSON><PERSON><PERSON>"}, {"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.1", "gameVersionReleaseDate": "2024-08-08T15:17:53.787Z", "gameVersionName": "1.21.1"}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-05-26T00:00:00Z", "gameVersionName": "Quilt"}], "gameVersions": ["1.21", "<PERSON><PERSON><PERSON>", "1.21.1", "Quilt"], "fileFingerprint": 1980880126, "modId": 459496}, "modrinthProject": {"id": "Orvt0mRa", "slug": "indium", "project_type": "mod", "team": "X5s94xjx", "title": "Indium", "description": "Sodium addon providing support for the Fabric Rendering API, based on Indigo", "body": "# Since Sodium 0.6.0, Indium is no longer required!\nSodium now has support for the Fabric Rendering API built in, so you no longer need Indium.\n\nIndium also isn't compatible with Sodium 0.6.0+, so don't use it :)\n\n<details>\n<summary>Original description</summary>\n\nIndium is an addon for the rendering optimisation mod [Sodium](https://modrinth.com/mod/sodium), providing support for the Fabric Rendering API. The Fabric Rendering API is required for many mods that use advanced rendering effects, and is currently not supported by Sodium directly. Indium is based upon the reference implementation Indigo, which is part of Fabric API with source available [here](https://github.com/FabricMC/fabric/tree/1.17/fabric-renderer-indigo). (licensed Apache 2.0)\n\n# Frequently Asked Questions\n## Which mods require Indium?\nAny mod that uses the Fabric Rendering API will require Indium when used with Sodium. These include: Campanion, Bits and Chisels, LambdaBetterGrass,\nContinuity, Packages, and many more. Some of these mods may function without an implementation of the Fabric Rendering API, but have broken textures and models.\n\n## Does Indium affect performance?\nIndium's impact on performance should be negligible, however mods that use the Fabric Rendering API could themselves impact performance. Indium will not provide a performance benefit over using only Sodium.\n\n## Is Indium a replacement for Sodium?\nNo, Indium is an addon mod for Sodium - you must use both mods together to get Fabric Rendering API support with Sodium's rendering optimisations.\n\n## Do I need Indium if I don't use Sodium?\nNo, Indigo is provided as part of Fabric API as the reference implementation of the Fabric Rendering API. Indigo disables itself when Sodium is installed.\n\n## Is Fabric API required?\nYes. To use Sodium with mods that require the Fabric Rendering API, you need *both* Fabric API to provide the rendering API and Indium to implement the rendering API.\n\n## Will it be merged into Sodium?\nFabric Rendering API support is not currently a priority for the Sodium developers, and will not be explored until Sodium is in a more stable state.\n\n## Which versions of Sodium are supported?\nSodium 0.2.0 or newer is required for all versions of Indium. I support the latest stable versions of Sodium for 1.18 and 1.19. Iris may not always be compatible with the latest release or build of Indium - please ask the Iris support channels if you have issues.\n\n</details>", "published": "2021-07-19T17:56:39.449041Z", "updated": "2025-02-25T22:48:55.052967Z", "status": "approved", "license": {"id": "Apache-2.0", "name": "Apache License 2.0"}, "client_side": "required", "server_side": "unsupported", "categories": ["utility"], "loaders": ["fabric", "quilt"], "icon_url": "https://cdn.modrinth.com/data/Orvt0mRa/13f4ab01fac350702ba20871c9f964064073a5e2_96.webp", "issues_url": "https://github.com/comp500/Indium/issues", "source_url": "https://github.com/comp500/Indium", "donation_urls": [], "gallery": []}, "modrinthVersion": {"id": "Z8VpxxGh", "project_id": "Orvt0mRa", "author_id": "M6f6rkfI", "name": "Indium 1.0.35 for Minecraft 1.21.x/Sodium 0.5.11", "version_number": "1.0.35+mc1.21", "changelog": "- Bump version, mark 1.21.1 as supported (comp500)\n- Readme fix (#337) (<PERSON><PERSON><PERSON><PERSON><PERSON>)\n- Update to support FRAPI 3.4.0 (#335) (PepperCode1)\n\nSee the full changes on Github: https://github.com/comp500/Indium/commits/1.0.35+mc1.21", "date_published": "2024-08-15T17:24:32.161310Z", "version_type": "release", "files": [{"hashes": {"sha512": "5f5e8a6ce29e6faecf442fe1fad76a3dd3631d83daa30423ba5cdb2cacf7efd92c16f05b65c3270faa23e4a803f392db9af40a030b81ea898550eded74ea8706", "sha1": "1bf4b5db91f4e6d7a4f94fe2e96525d22df1cacb"}, "url": "https://cdn.modrinth.com/data/Orvt0mRa/versions/Z8VpxxGh/indium-1.0.35%2Bmc1.21.jar", "filename": "indium-1.0.35+mc1.21.jar", "primary": true, "size": 105000}], "dependencies": [{"version_id": "RncWhTxD", "project_id": "AANobbMI", "dependency_type": "required"}], "game_versions": ["1.21", "1.21.1"], "loaders": ["fabric", "quilt"]}}, {"name": "Distant Horizons: A Level of Detail mod", "version": "1.21.1 Alpha 2.2.1 neo/fabric", "optional": true, "file": "DistantHorizons-2.2.1-a-1.21.1-neo-fabric.jar", "type": "mods", "description": "Increase view distance without harming performance.", "disabled": true, "userAdded": true, "wasSelected": true, "skipped": false, "curseForgeProjectId": 508933, "curseForgeFileId": 5689286, "curseForgeProject": {"id": 508933, "name": "Distant Horizons: A Level of Detail mod", "authors": [{"id": 17079937, "name": "<PERSON><PERSON>", "url": "https://www.curseforge.com/members/james_seibel"}], "gameId": 432, "summary": "Increase view distance without harming performance.", "categories": [{"name": "Utility & QoL", "slug": "utility-qol", "url": "https://www.curseforge.com/minecraft/mc-mods/utility-qol", "dateModified": "2021-11-17T11:45:09.143Z", "gameId": 432, "isClass": false, "id": 5191, "iconUrl": "https://media.forgecdn.net/avatars/456/558/637727379086405233.png", "parentCategoryId": 6, "classId": 6}], "status": 4, "primaryCategoryId": 5191, "classId": 6, "slug": "distant-horizons", "isFeatured": false, "dateModified": "2024-09-17T12:13:06.45Z", "dateCreated": "2021-07-28T03:24:00.973Z", "dateReleased": "2024-09-03T12:24:31.903Z", "links": {"websiteUrl": "https://www.curseforge.com/minecraft/mc-mods/distant-horizons", "wikiUrl": "https://gitlab.com/distant-horizons-team/distant-horizons/-/wikis/2-frequently-asked-questions/1-general/General", "issuesUrl": "https://gitlab.com/jeseibel/minecraft-lod-mod/-/issues", "sourceUrl": "https://gitlab.com/distant-horizons-team/distant-horizons"}, "logo": {"id": 427238, "description": "", "thumbnailUrl": "https://media.forgecdn.net/avatars/thumbnails/427/238/256/256/637658621275806301.png", "title": "637658621275806301.png", "url": "https://media.forgecdn.net/avatars/427/238/637658621275806301.png", "modId": 508933}, "allowModDistribution": true, "screenshots": [{"id": 488618, "description": "Photo taken from the Pioneer Survival server", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/488/618/310/172/pioneer-survival-floating-island.png", "title": "Client-side Multiplayer Support 1", "url": "https://media.forgecdn.net/attachments/488/618/pioneer-survival-floating-island.png", "modId": 508933}, {"id": 431564, "description": "Vanilla render distance 4. Mod render distance 512.", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/431/564/310/172/cliff-side-2.png", "title": "Cliff side 2", "url": "https://media.forgecdn.net/attachments/431/564/cliff-side-2.png", "modId": 508933}, {"id": 403112, "description": "By default the mod renders at 64, but can go up to 4096!\r\n(although anything above 512 may become difficult due to high RAM or GPU usage)", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/403/112/310/172/256-render-distance.png", "title": "256 render distance", "url": "https://media.forgecdn.net/attachments/403/112/256-render-distance.png", "modId": 508933}, {"id": 488621, "description": "Photo taken from the Pioneer Survival server", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/488/621/310/172/pioneer-survival-spawn.png", "title": "Client-side Multiplayer Support 2", "url": "https://media.forgecdn.net/attachments/488/621/pioneer-survival-spawn.png", "modId": 508933}, {"id": 467383, "description": "The mod will render chunks at different detail levels depending on how far they are from you.", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/467/383/310/172/all-gif.gif", "title": "Detail levels (gif)", "url": "https://media.forgecdn.net/attachments/467/383/all-gif.gif", "modId": 508933}, {"id": 477565, "description": "Finding end cities will be much easier now. \r\nPhoto taken by: <PERSON><PERSON><PERSON>", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/477/565/310/172/2022-07-10_23.png", "title": "The End", "url": "https://media.forgecdn.net/attachments/477/565/2022-07-10_23.png", "modId": 508933}, {"id": 391538, "description": "Fake chunks closer will become larger and less detailed the further they are from the player, increasing performance.", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/391/538/310/172/5.png", "title": "Variable Detail", "url": "https://media.forgecdn.net/attachments/391/538/5.png", "modId": 508933}, {"id": 431567, "description": "Vanilla render distance 4. Mod render distance 512.", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/431/567/310/172/cliff-side-1.png", "title": "Ciff side 1", "url": "https://media.forgecdn.net/attachments/431/567/cliff-side-1.png", "modId": 508933}, {"id": 880253, "description": "Oceans feel a lot smaller now don't they?\n\nCombined with Iris 1.7 shaders.\nPhoto courtesy of <PERSON><PERSON>", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/880/253/310/172/light-house-sarge.jpg", "title": "Ocean Overlook", "url": "https://media.forgecdn.net/attachments/880/253/light-house-sarge.jpg", "modId": 508933}, {"id": 880249, "description": "dh 512 rd.jpg", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/880/249/310/172/dh-512-rd.jpg", "title": "512 Render Distance", "url": "https://media.forgecdn.net/attachments/880/249/dh-512-rd.jpg", "modId": 508933}, {"id": 466466, "description": "", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/466/466/310/172/logonameflathighlight.png", "title": "Logo", "url": "https://media.forgecdn.net/attachments/466/466/logonameflathighlight.png", "modId": 508933}, {"id": 431558, "description": "", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/431/558/310/172/horizon.png", "title": "A Distant Horizon", "url": "https://media.forgecdn.net/attachments/431/558/horizon.png", "modId": 508933}, {"id": 880251, "description": "Combined with Iris 1.7 shaders.\nPhoto courtesy of <PERSON><PERSON>", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/880/251/310/172/misty-mountain-sarge.jpg", "title": "Distant Mountains", "url": "https://media.forgecdn.net/attachments/880/251/misty-mountain-sarge.jpg", "modId": 508933}, {"id": 760783, "description": "Showing MC 1.16", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/760/783/310/172/1024-fog-mc-1.jpg", "title": "1024 render distance", "url": "https://media.forgecdn.net/attachments/760/783/1024-fog-mc-1.jpg", "modId": 508933}, {"id": 475003, "description": "Fog can be customized to change with vertical height along with horizontal distance.", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/475/3/310/172/unknown.png", "title": "Vertical Fog", "url": "https://media.forgecdn.net/attachments/475/3/unknown.png", "modId": 508933}, {"id": 467382, "description": "The mod will render chunks at different detail levels depending on how far they are from you.", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/467/382/310/172/all.png", "title": "Detail levels", "url": "https://media.forgecdn.net/attachments/467/382/all.png", "modId": 508933}, {"id": 431559, "description": "Distant Horizons works with mod terrain generators!\r\n(Terralith 2.0 shown in this image)", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/431/559/310/172/volcano-2.png", "title": "Modded Terrain Generation", "url": "https://media.forgecdn.net/attachments/431/559/volcano-2.png", "modId": 508933}, {"id": 403115, "description": "", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/403/115/310/172/nether-vista.png", "title": "Cave/Nether support", "url": "https://media.forgecdn.net/attachments/403/115/nether-vista.png", "modId": 508933}], "mainFileId": 5689286}, "curseForgeFile": {"id": 5689286, "gameId": 432, "isAvailable": true, "displayName": "1.21.1 Alpha 2.2.1 neo/fabric", "fileName": "DistantHorizons-2.2.1-a-1.21.1-neo-fabric.jar", "releaseType": 2, "fileStatus": 4, "fileDate": "2024-09-03T12:24:31.903Z", "fileLength": 16613008, "dependencies": [], "alternateFileId": 0, "modules": [{"fingerprint": 2974776997, "name": "assets"}, {"fingerprint": 1896229629, "name": "build_info.json"}, {"fingerprint": **********, "name": "com"}, {"fingerprint": **********, "name": "DistantHorizons"}, {"fingerprint": **********, "name": "fabric-1_20_6.distanthorizons.accesswidener"}, {"fingerprint": **********, "name": "fabric-DistantHorizons-fabric-refmap.json"}, {"fingerprint": **********, "name": "fabric-distanthorizons.accesswidener"}, {"fingerprint": **********, "name": "fabric-DistantHorizons.fabric.mixins.json"}, {"fingerprint": **********, "name": "fabric.mod.json"}, {"fingerprint": **********, "name": "LICENSE.LESSER.txt"}, {"fingerprint": **********, "name": "LICENSE.txt"}, {"fingerprint": **********, "name": "loader<PERSON><PERSON><PERSON>"}, {"fingerprint": **********, "name": "log4jConfig.xml"}, {"fingerprint": **********, "name": "META-INF"}, {"fingerprint": **********, "name": "neoforge-1_20_6.distanthorizons.accesswidener"}, {"fingerprint": 324053938, "name": "neoforge-DistantHorizons.neoforge.mixins.json"}, {"fingerprint": 367747903, "name": "not.fabric.mod.json"}, {"fingerprint": 34570901, "name": "org"}, {"fingerprint": **********, "name": "pack.mcmeta"}, {"fingerprint": **********, "name": "shaders"}, {"fingerprint": **********, "name": "sqlite-jdbc.properties"}, {"fingerprint": **********, "name": "sqlScripts"}], "isServerPack": false, "hashes": [{"value": "302ad0271f1b3d9029361c94cd5cf753a60960e5", "algo": 1}, {"value": "6e5c8374e29fdfa1c104f9a1cab0e840", "algo": 2}], "sortableGameVersions": [{"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-09-01T00:00:00Z", "gameVersionName": "<PERSON><PERSON><PERSON>"}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-12-08T00:00:00Z", "gameVersionName": "Client"}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2023-07-25T00:00:00Z", "gameVersionName": "NeoForge"}, {"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.1", "gameVersionReleaseDate": "2024-08-08T15:17:53.787Z", "gameVersionName": "1.21.1"}], "gameVersions": ["<PERSON><PERSON><PERSON>", "Client", "NeoForge", "1.21.1"], "fileFingerprint": 2009432662, "modId": 508933}, "modrinthProject": {"id": "uCdwusMi", "slug": "distanthorizons", "project_type": "mod", "team": "FulsV5Ai", "title": "Distant Horizons", "description": "See farther without turning your game into a slide show", "body": "<center>\n  <img width=\"420\" src=\"https://media.forgecdn.net/attachments/466/466/logonameflathighlight.png\" alt=\"Distant Horizons\">\n</center>\n\n\n\n<br>\n\nWhat does Distant Horizons do? \n<br>\nSimply put, it add simplified terrain past Minecraft's default view distance to improve performance and allow for longer draw distances.\n<br>\nNow you can finally enjoy that lookout tower you built on top of a mountain!\n\n![Cliffside Village](https://media.forgecdn.net/attachments/880/249/dh-512-rd.jpg)\n### <center> This photo was taken with a Minecraft render distance of 12 and a mod distance of 512. </center>\n<br> <br>\n\n<center>\nIf you want to see a quick demo, check out the latest update video\n<br> <br>\n\n<a href=\"https://www.youtube.com/watch?v=zuQDgamAZsU\">\n<img src=\"https://i.ytimg.com/vi/zuQDgamAZsU/hq720.jpg\" width=\"630\" />\n</a> </center> <br> <br>\n\n## Hey, Hey you! This is important!\n\nThe version support numbers are strict! <br>\nIf a version says it supports MC 1.18.1 it will NOT work on 1.18.2! <br>\n\nAnd: the mod is still in alpha. Things may change, break, crash or otherwise go wrong. You have been warned! <br>\nAlthough if you have problems feel free to leave an issue on the project's GitLab.\n<br> \n \n\n \n## FAQ:\n\n**Q: Forge or Fabric?** <br>\nA: MC 1.20.6 and newer - NeoForge and Fabric are both in the same jar. <br>\nMC 1.20.4 and older - Forge and Fabric are both in the same jar. \n\n**Q: Shaders?** <br>\nA: Yes, but only with certain shaders. <br>\n- Optifine: DH partially works with forward rendered shaders.\n- Iris (and equivalent ports): DH 2.1.0+ only works with Iris 1.7+ and only with shaders built with DH support in mind. \n  - DH 2.0 and older don't work with Iris.\n\n**Q: Does this work with player created structures?** <br>\nA: Yes.\n\n**Q: Is there multiplayer support?** <br>\nA: Yes.\nAlthough currently the mod is only client side so you have to explore chunks for the mod to use them.\n\n<br>\n\n### Extended FAQ:\n\n<a href=\"https://gitlab.com/distant-horizons-team/distant-horizons/-/wikis/2-frequently-asked-questions/1-general/General\">\n<img src=\"https://gitlab.com/jeseibel/minecraft-lod-mod/-/wikis/_hidden/shared-images/images/faq-button-general.png\" width=\"200\" />\n</a> <br>\n\n<a href=\"https://gitlab.com/distant-horizons-team/distant-horizons/-/wikis/2-frequently-asked-questions/2-problems-and-solutions/Problems-and-Solutions\">\n<img src=\"https://gitlab.com/distant-horizons-team/distant-horizons/-/wikis/_hidden/shared-images/images/faq-button-mod-support.png\" width=\"200\" />\n</a> <br>\n\n<a href=\"https://gitlab.com/distant-horizons-team/distant-horizons/-/wikis/2-frequently-asked-questions/4-mod-support/Mod-Support\">\n<img src=\"https://gitlab.com/distant-horizons-team/distant-horizons/-/wikis/_hidden/shared-images/images/faq-button-problem.png\" width=\"200\" />\n</a>\n\n<br> <br>\n\n\n## Discord:\nJoin our Discord for news, updates, and help:\ndiscord.com/invite/xAB8G4cENx\n<br> <br>\n\n\n## Support us:\n\nIf you want to help with development, join our discord and let us know!\n\nOtherwise if you can't help with development but still want to support Distant Horizons, check out our donation page:\n\nhttps://ko-fi.com/distanthorizons", "published": "2022-01-23T05:46:55.026356Z", "updated": "2024-09-03T12:39:30.686412Z", "status": "approved", "license": {"id": "LGPL-3.0-only", "name": "GNU Lesser General Public License v3.0 only"}, "client_side": "required", "server_side": "unsupported", "categories": ["optimization", "utility"], "icon_url": "https://cdn.modrinth.com/data/uCdwusMi/0d4a6eaabadd21a7a36c6e7c131b406f70bc933c_96.webp", "issues_url": "https://gitlab.com/distant-horizons-team/distant-horizons/-/issues", "source_url": "https://gitlab.com/distant-horizons-team/distant-horizons", "wiki_url": "https://gitlab.com/distant-horizons-team/distant-horizons/-/wikis/home", "discord_url": "https://discord.gg/xAB8G4cENx", "donation_urls": [{"id": "ko-fi", "platform": "Ko-fi", "url": "https://ko-fi.com/distanthorizons"}], "gallery": [{"url": "https://cdn.modrinth.com/data/uCdwusMi/images/1aa2b086bb9f1e800183feea724b98b90d62cd15_350.webp", "raw_url": "https://cdn.modrinth.com/data/uCdwusMi/images/1b27a855d3b44c0d1b7bc111b7e22d1c6f9a9e5d.png", "featured": false, "title": "Modded Terrain Generation", "description": "Distant Horizons works with mod terrain generators! (Terralith 2.0 shown in this image)", "created": "2022-06-07T02:51:26.416185Z", "ordering": 0.0}, {"url": "https://cdn.modrinth.com/data/uCdwusMi/images/1f23c2eb7d43d67dd35912456205cc1b0b66e905_350.webp", "raw_url": "https://cdn.modrinth.com/data/uCdwusMi/images/5dac2a283e66ed9da0f76ec5a8a6cd243309bff1.png", "featured": false, "title": "Cave/Nether support", "description": " ", "created": "2022-06-07T02:47:51.575910Z", "ordering": 0.0}, {"url": "https://cdn.modrinth.com/data/uCdwusMi/images/29b1542131948d311c933506c240fde3a3114df9_350.webp", "raw_url": "https://cdn.modrinth.com/data/uCdwusMi/images/8a194aa0cdd60a695550eb53110a6f61d880db57.jpeg", "featured": false, "title": "1024 render distance", "description": "Showing MC 1.16", "created": "2023-11-18T14:30:55.851012Z", "ordering": 0.0}, {"url": "https://cdn.modrinth.com/data/uCdwusMi/images/329feb1c2514756735de08f5abe378dcc5597f93_350.webp", "raw_url": "https://cdn.modrinth.com/data/uCdwusMi/images/53bd0a0be0d54480da03175ce1c6901df826f00f.png", "featured": false, "title": "Cliff side 2", "description": "Vanilla render distance 4. Mod render distance 512.", "created": "2022-06-07T02:52:29.740651Z", "ordering": 0.0}, {"url": "https://cdn.modrinth.com/data/uCdwusMi/images/669f60450b322881d01972de2671ff5ab87dab3b.gif", "raw_url": "https://cdn.modrinth.com/data/uCdwusMi/images/669f60450b322881d01972de2671ff5ab87dab3b.gif", "featured": false, "title": "Detail levels (gif)", "description": "The mod will render chunks at different detail levels depending on how far they are from you.", "created": "2022-06-07T03:08:24.100552Z", "ordering": 0.0}, {"url": "https://cdn.modrinth.com/data/uCdwusMi/images/738910aac87e64ca229e62e58d60c672c1c223ff_350.webp", "raw_url": "https://cdn.modrinth.com/data/uCdwusMi/images/f379ba97a83c9ebe18da3ebed2de3f449117f8e8.png", "featured": false, "title": "Client-side Multiplayer Support 2", "description": "Photo taken from the Pioneer Survival server", "created": "2022-08-20T19:56:09.442323Z", "ordering": 0.0}, {"url": "https://cdn.modrinth.com/data/uCdwusMi/images/84b9849fc6a8a97308c551ba95ea9253ee943337_350.webp", "raw_url": "https://cdn.modrinth.com/data/uCdwusMi/images/b743960d0cbdde7f54edc07de127bc59c6b06350.png", "featured": false, "title": "Client-side Multiplayer Support 1", "description": "Photo taken from the Pioneer Survival server", "created": "2022-08-20T19:56:07.314760Z", "ordering": 0.0}, {"url": "https://cdn.modrinth.com/data/uCdwusMi/images/8b80a71825383615354e731402563d434e28e541_350.webp", "raw_url": "https://cdn.modrinth.com/data/uCdwusMi/images/1fc59e3c050f6a06635ccf37aa2b8125bb9856c5.png", "featured": false, "title": "Detail levels", "description": "The mod will render chunks at different detail levels depending on how far they are from you.", "created": "2022-06-07T03:08:02.244410Z", "ordering": 0.0}, {"url": "https://cdn.modrinth.com/data/uCdwusMi/images/9fa71bad4ac5d484221c76690aeccdd74ff55e93_350.webp", "raw_url": "https://cdn.modrinth.com/data/uCdwusMi/images/9b85cf731ecc3100866ccc8e43d60d40c8d189ed.png", "featured": true, "title": "A Distant Horizon", "created": "2022-06-07T02:50:17.573019Z", "ordering": 0.0}, {"url": "https://cdn.modrinth.com/data/uCdwusMi/images/bbc4609a89b2d3882c4bd50d931d9603d290cfcc_350.webp", "raw_url": "https://cdn.modrinth.com/data/uCdwusMi/images/b0981e8472507c468d00b4e466d38c0801c7425e.png", "featured": false, "title": "Custom map support", "description": "Pictured: Super Hostile Iceolation", "created": "2022-06-07T02:48:58.993459Z", "ordering": 0.0}, {"url": "https://cdn.modrinth.com/data/uCdwusMi/images/c5ee81eba95474bcc17b84b17ace49b0f6509277_350.webp", "raw_url": "https://cdn.modrinth.com/data/uCdwusMi/images/65eb075cb80cf3f12cc403aa27a12b9377c40b36.png", "featured": false, "title": "Ciff side 1", "description": "Vanilla render distance 4. Mod render distance 512.", "created": "2022-06-07T02:52:53.882646Z", "ordering": 0.0}, {"url": "https://cdn.modrinth.com/data/uCdwusMi/images/d70f1da2729c79aca38dc0260c5229df5b3ec277_350.webp", "raw_url": "https://cdn.modrinth.com/data/uCdwusMi/images/80be8f6d024e322dc5a7538a4a0b187f2bc87210.png", "featured": false, "title": "256 render distance", "description": "By default the mod renders at 64, but can go up to 4096! (although anything above 512 may become difficult due to high RAM or GPU usage)", "created": "2022-06-07T02:47:26.204976Z", "ordering": 0.0}, {"url": "https://cdn.modrinth.com/data/uCdwusMi/images/fa76d2bd04d77cf1ab1fd4d076f7ac9eb4fe7ffe_350.webp", "raw_url": "https://cdn.modrinth.com/data/uCdwusMi/images/153a12159c2f00a59190623906b79245ddeaec8a.png", "featured": false, "title": "Variable Detail", "description": "Fake chunks closer will become larger and less detailed the further they are from the player, increasing performance.", "created": "2022-06-07T02:46:42.413300Z", "ordering": 0.0}, {"url": "https://cdn.modrinth.com/data/uCdwusMi/images/fdada7d20036fbed71f61f0fd18c959b232af6e2_350.webp", "raw_url": "https://cdn.modrinth.com/data/uCdwusMi/images/5f1a20ee735c1dcd026e62228301f7d10928a299.png", "featured": false, "title": "The End", "description": "Finding end cities will be much easier now.  Photo taken by: <PERSON><PERSON><PERSON>", "created": "2022-07-17T13:33:42.641700Z", "ordering": 0.0}]}, "modrinthVersion": {"id": "pEvLEY5E", "project_id": "uCdwusMi", "author_id": "ksmgF6Gt", "name": "2.2.1-a - 1.21.1 neo/fabric", "version_number": "2.2.1-a-1.21.1", "changelog": "\n## Highlights:\n\n- Added a option to toggle cloud rendering\n- Added nearby beacon culling to prevent overdraw\n- Fixed LOD Lighting\n- Fixed DH Cloud colors\n- Fixed LODs flashing when loading/unloading\n- Fixed Lithium breaking DH world gen for MC 1.20.1 and older\n\n\n\n## Full Changelog\n\n**Additions:**\n\n- Add a top level UI config to toggle cloud rendering\n- Add nearby beacon culling\n\t- Enabled by default, can be disabled under advanced graphics\n\n<br/>\n\n**Changes:**\n\n- Change delayed LOD modified save from 2 seconds -> 500 ms\n\t- This should make LODs appear to update slightly faster\n- Allow users to re-enable the DH renderer if an exception is thrown\n\t- To re-enable the renderer, toggle DH rendering using the UI config menu\n\n<br/>\n\n**Bug Fixes:**\n\n- Fix LODs not updating in the nether or when blocks are changed underground\n\t- Note: DH only listens for some block changes underground (currently 1 in 4 blocks), so changing several blocks or one light emitting may be necessary to trigger an update).\n- Fix Lighting not generating correctly\n- Fix DH clouds colors not matching vanilla MC clouds\n- Fix glass panes not affecting beacon colors\n- Fix several beacon rendering/updating issues when beacons are on chunk borders\n- Disable instanced rendering on Mac when Sodium is present\n\t- This should fix M1 macs crashing when both DH and Sodium are together\n- Fix LODs flashing when they load/unload\n- Fix Lithium breaking DH world gen for MC 1.20.1 and older\n\n<br/>\n\n## API Changes\n\n**Bug Fixes:**\n\n- Fix RenderableBoxGroup not supporting .clear()\n\n\n", "date_published": "2024-09-03T12:39:32.253363Z", "version_type": "alpha", "files": [{"hashes": {"sha512": "915d15ac4bd898ee50c3e451e9c48e8f9aed86c421e56604cdbc1704035b9e5140f73cdecb783a4a6e87b7438ebc76f135eaad6eff2dacc6c8dba6ffb34cc0f8", "sha1": "302ad0271f1b3d9029361c94cd5cf753a60960e5"}, "url": "https://cdn.modrinth.com/data/uCdwusMi/versions/pEvLEY5E/DistantHorizons-2.2.1-a-1.21.1-neo-fabric.jar", "filename": "DistantHorizons-2.2.1-a-1.21.1-neo-fabric.jar", "primary": true, "size": 16613008}], "dependencies": [], "game_versions": ["1.21", "1.21.1"], "loaders": ["fabric", "neoforge"]}}, {"name": "Trinkets", "version": "trinkets-3.10.0.jar", "optional": true, "file": "trinkets-3.10.0.jar", "type": "mods", "description": "A data-driven accessory mod", "disabled": false, "userAdded": true, "wasSelected": true, "skipped": false, "curseForgeProjectId": 341284, "curseForgeFileId": 5534317, "curseForgeProject": {"id": 341284, "name": "Trinkets (Fabric)", "authors": [{"id": 100436320, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://www.curseforge.com/members/emilyploszaj"}], "gameId": 432, "summary": "A data-driven accessory mod", "categories": [{"name": "API and Library", "slug": "library-api", "url": "https://www.curseforge.com/minecraft/mc-mods/library-api", "dateModified": "2014-05-23T03:21:44.06Z", "gameId": 432, "isClass": false, "id": 421, "iconUrl": "https://media.forgecdn.net/avatars/6/36/635351496947765531.png", "parentCategoryId": 6, "classId": 6}], "status": 4, "primaryCategoryId": 421, "classId": 6, "slug": "trinkets", "isFeatured": false, "dateModified": "2024-07-15T11:07:18.877Z", "dateCreated": "2019-09-12T23:26:22.057Z", "dateReleased": "2024-07-15T11:00:45.04Z", "links": {"websiteUrl": "https://www.curseforge.com/minecraft/mc-mods/trinkets", "wikiUrl": "https://github.com/emilyalexandra/trinkets/wiki", "issuesUrl": "https://github.com/emilyalexandra/trinkets/issues", "sourceUrl": "https://github.com/emilyalexandra/trinkets"}, "logo": {"id": 392688, "description": "", "thumbnailUrl": "https://media.forgecdn.net/avatars/thumbnails/392/688/256/256/637587073088891583.png", "title": "637587073088891583.png", "url": "https://media.forgecdn.net/avatars/392/688/637587073088891583.png", "modId": 341284}, "allowModDistribution": true, "screenshots": [{"id": 626565, "description": "", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/626/565/310/172/trinkets.png", "title": "Trinkets UI", "url": "https://media.forgecdn.net/attachments/626/565/trinkets.png", "modId": 341284}], "mainFileId": 5534317}, "curseForgeFile": {"id": 5534317, "gameId": 432, "isAvailable": true, "displayName": "trinkets-3.10.0.jar", "fileName": "trinkets-3.10.0.jar", "releaseType": 1, "fileStatus": 4, "fileDate": "2024-07-15T11:00:45.04Z", "fileLength": 272944, "dependencies": [], "alternateFileId": 0, "modules": [{"fingerprint": **********, "name": "META-INF"}, {"fingerprint": **********, "name": "LICENSE_trinkets"}, {"fingerprint": **********, "name": "assets"}, {"fingerprint": **********, "name": "data"}, {"fingerprint": **********, "name": "dev"}, {"fingerprint": **********, "name": "fabric.mod.json"}, {"fingerprint": **********, "name": "trinkets-refmap.json"}, {"fingerprint": **********, "name": "trinkets.accesswidener"}, {"fingerprint": 320223415, "name": "trinkets.mixins.json"}], "isServerPack": false, "hashes": [{"value": "f54756f938c7c273713b9f97d4a987eec551e892", "algo": 1}, {"value": "4a42e243d6d5c25dea83be3ddc0b28b1", "algo": 2}], "sortableGameVersions": [{"gameVersionPadded": "**********.**********", "gameVersion": "1.21", "gameVersionReleaseDate": "2024-06-14T00:00:00Z", "gameVersionName": "1.21"}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-09-01T00:00:00Z", "gameVersionName": "<PERSON><PERSON><PERSON>"}, {"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.1", "gameVersionReleaseDate": "2024-08-08T15:17:53.787Z", "gameVersionName": "1.21.1"}], "gameVersions": ["1.21", "<PERSON><PERSON><PERSON>", "1.21.1"], "fileFingerprint": 1163983694, "modId": 341284}, "modrinthProject": {"id": "5aaWibi9", "slug": "trinkets", "project_type": "mod", "team": "F0xlmrbH", "title": "Trinkets", "description": "A data-driven accessory mod", "body": "# Trinkets\nA data-driven accessory mod for Minecraft using Fabric.\n\n![Image of the Trinkets UI](https://cdn-raw.modrinth.com/data/5aaWibi9/images/56224f13887cdd914a9624a18eb845dae0bcdfc0.png)\n\n## About\nTrinkets adds a slot group and slot system to Minecraft. Slot groups are collections of slots for a certain body part or more vague area. By default there are 6 slot groups (head, chest, legs, feet, offhand, hand) that can have slots added to them, but more groups can be added if desired. Trinkets' UI is intuitive to use, accessible, and attempts to do away with clutter. Its system means that you'll never have a slot that's not used for anything, as mods request the slots they want.\n\nFor basic tutorials and comprehensive documentation, visit this project's [wiki](https://github.com/emilyploszaj/trinkets/wiki/Home).\n", "published": "2021-06-08T00:05:32.472592Z", "updated": "2024-07-15T11:03:14.206207Z", "status": "approved", "license": {"id": "MIT", "name": "MIT License"}, "client_side": "required", "server_side": "required", "categories": ["equipment", "library", "utility"], "loaders": ["fabric", "quilt"], "icon_url": "https://cdn.modrinth.com/data/5aaWibi9/icon.png", "issues_url": "https://github.com/emilyalexandra/trinkets/issues", "source_url": "https://github.com/emilyalexandra/trinkets", "wiki_url": "https://github.com/emilyalexandra/trinkets/wiki", "discord_url": "https://discord.gg/T2PuD9RTWV", "donation_urls": [], "gallery": [{"url": "https://cdn.modrinth.com/data/5aaWibi9/images/56224f13887cdd914a9624a18eb845dae0bcdfc0.png", "raw_url": "https://cdn.modrinth.com/data/5aaWibi9/images/56224f13887cdd914a9624a18eb845dae0bcdfc0.png", "featured": false, "title": "Trinkets UI", "created": "2023-02-24T23:54:31.401757Z", "ordering": 0.0}]}, "modrinthVersion": {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "project_id": "5aaWibi9", "author_id": "R6WsWKy9", "name": "Trinkets 3.10.0", "version_number": "3.10.0", "changelog": "", "date_published": "2024-07-15T11:03:14.206207Z", "version_type": "release", "files": [{"hashes": {"sha512": "3ea846c945a0559696501ff65b373c8ee8fd9b394604e9910b4ed710c3e07cadc674a615a2c3b385951a42253a418201975df951b3100053ed39afadc70221c9", "sha1": "f54756f938c7c273713b9f97d4a987eec551e892"}, "url": "https://cdn.modrinth.com/data/5aaWibi9/versions/JagCscwi/trinkets-3.10.0.jar", "filename": "trinkets-3.10.0.jar", "primary": true, "size": 272944}], "dependencies": [], "game_versions": ["1.21", "1.21.1"], "loaders": ["fabric"]}}, {"name": "Shulker Box Slot", "version": "shulkerboxslot-fabric-6.0.0+1.21.1.jar", "optional": true, "file": "shulkerboxslot-fabric-6.0.0+1.21.1.jar", "type": "mods", "description": "Adds accessory support for Shulker Boxes for easier access and management. Formerly known as Curious Shulker Boxes and Trinket Shulker Boxes.", "disabled": false, "userAdded": true, "wasSelected": true, "skipped": false, "curseForgeProjectId": 316881, "curseForgeFileId": 5802203, "curseForgeProject": {"id": 316881, "name": "Shulker Box Slot (Fabric/Forge/Quilt)", "authors": [{"id": 34683772, "name": "TheIllusiveC4", "url": "https://www.curseforge.com/members/theillusivec4"}], "gameId": 432, "summary": "Adds curios and trinkets support for Shulker Boxes for easier access and management. Formerly named Curious Shulker Boxes and Trinket Shulker Boxes.", "categories": [{"name": "Cosmetic", "slug": "cosmetic", "url": "https://www.curseforge.com/minecraft/mc-mods/cosmetic", "dateModified": "2014-05-08T17:42:35.597Z", "gameId": 432, "isClass": false, "id": 424, "iconUrl": "https://media.forgecdn.net/avatars/6/39/635351497555976928.png", "parentCategoryId": 6, "classId": 6}, {"name": "Storage", "slug": "storage", "url": "https://www.curseforge.com/minecraft/mc-mods/storage", "dateModified": "2014-05-08T17:41:17.203Z", "gameId": 432, "isClass": false, "id": 420, "iconUrl": "https://media.forgecdn.net/avatars/6/35/635351496772023801.png", "parentCategoryId": 6, "classId": 6}, {"name": "Armor, Tools, and Weapons", "slug": "armor-weapons-tools", "url": "https://www.curseforge.com/minecraft/mc-mods/armor-weapons-tools", "dateModified": "2014-05-08T17:44:39.057Z", "gameId": 432, "isClass": false, "id": 434, "iconUrl": "https://media.forgecdn.net/avatars/6/47/635351498790409758.png", "parentCategoryId": 6, "classId": 6}], "status": 4, "primaryCategoryId": 434, "classId": 6, "slug": "shulker-box-slot", "isFeatured": false, "dateModified": "2024-10-11T07:59:51.953Z", "dateCreated": "2019-03-08T01:29:08.23Z", "dateReleased": "2024-10-11T07:49:50.017Z", "links": {"websiteUrl": "https://www.curseforge.com/minecraft/mc-mods/shulker-box-slot", "wikiUrl": "", "issuesUrl": "https://github.com/TheIllusiveC4/CuriousShulkerBoxes/issues", "sourceUrl": "https://github.com/TheIllusiveC4/CuriousShulkerBoxes"}, "logo": {"id": 195000, "description": "", "thumbnailUrl": "https://media.forgecdn.net/avatars/thumbnails/195/0/256/256/636876053482753569.png", "title": "636876053482753569.png", "url": "https://media.forgecdn.net/avatars/195/0/636876053482753569.png", "modId": 316881}, "allowModDistribution": true, "screenshots": [], "mainFileId": 5802206}, "curseForgeFile": {"id": 5802203, "gameId": 432, "isAvailable": true, "displayName": "shulkerboxslot-fabric-6.0.0+1.21.1.jar", "fileName": "shulkerboxslot-fabric-6.0.0+1.21.1.jar", "releaseType": 1, "fileStatus": 4, "fileDate": "2024-10-11T07:49:41.203Z", "fileLength": 585960, "dependencies": [{"fileId": 0, "modId": 341284, "relationType": 3}, {"fileId": 0, "modId": 306612, "relationType": 3}], "alternateFileId": 5802204, "modules": [{"fingerprint": **********, "name": "META-INF"}, {"fingerprint": **********, "name": "CHANGELOG.md"}, {"fingerprint": **********, "name": "COPYING"}, {"fingerprint": **********, "name": "COPYING.LESSER"}, {"fingerprint": **********, "name": "LICENSE"}, {"fingerprint": **********, "name": "README.md"}, {"fingerprint": **********, "name": "assets"}, {"fingerprint": **********, "name": "com"}, {"fingerprint": 849506222, "name": "data"}, {"fingerprint": **********, "name": "fabric.mod.json"}, {"fingerprint": **********, "name": "pack.mcmeta"}, {"fingerprint": 698132410, "name": "shulkerboxslot_icon.png"}, {"fingerprint": **********, "name": "spectrelib"}], "isServerPack": false, "hashes": [{"value": "0e9873bfe6458d69c847082fb4c04dbfb00f7ec9", "algo": 1}, {"value": "5eec1a321a6e8127063b7f1d30a2d291", "algo": 2}], "sortableGameVersions": [{"gameVersionPadded": "**********.**********", "gameVersion": "1.21", "gameVersionReleaseDate": "2024-06-14T00:00:00Z", "gameVersionName": "1.21"}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-09-01T00:00:00Z", "gameVersionName": "<PERSON><PERSON><PERSON>"}, {"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.1", "gameVersionReleaseDate": "2024-08-08T15:17:53.787Z", "gameVersionName": "1.21.1"}], "gameVersions": ["1.21", "<PERSON><PERSON><PERSON>", "1.21.1"], "fileFingerprint": 1707100203, "modId": 316881}, "modrinthProject": {"id": "R5T1RmWr", "slug": "shulker-box-slot", "project_type": "mod", "team": "YZKnRFuc", "title": "Shulker Box Slot", "description": "Adds accessory support for Shulker Boxes for easier access and management. Formerly known as Curious Shulker Boxes and Trinket Shulker Boxes.", "body": "Shulker Box Slot is a mod that uses Curios API/Trinkets API to add a new slot, the Back slot to the player inventory and allows shulker boxes to be placed into this slot. While a shulker box is in this slot, the player can use a keybinding, default 'X', to directly access its contents. This allows the player to interact with a shulker box's contents more freely and easily without the need to place it down, to sacrifice an inventory slot to keep it around, or to need to search through a player's inventory for the box.\n\n# Affiliates\n\n[![BisectHosting](https://i.ibb.co/1G4QPdc/bh-illusive.png)](https://bisecthosting.com/illusive)\n", "published": "2023-02-10T05:33:53.682938Z", "updated": "2024-10-11T07:49:47.206540Z", "status": "approved", "license": {"id": "LGPL-3.0-or-later", "name": "GNU Lesser General Public License v3.0 only"}, "client_side": "required", "server_side": "required", "categories": ["adventure", "equipment", "storage"], "loaders": ["fabric", "forge", "neoforge", "quilt"], "icon_url": "https://cdn.modrinth.com/data/R5T1RmWr/7494a38aff455e78f6d0ef0208c24c3c06894bd7.png", "issues_url": "https://github.com/TheIllusiveC4/CuriousShulkerBoxes/issues", "source_url": "https://github.com/TheIllusiveC4/CuriousShulkerBoxes", "discord_url": "https://discord.gg/JWgrdwt", "donation_urls": [{"id": "ko-fi", "platform": "Ko-fi", "url": "https://ko-fi.com/theillusivec4"}], "gallery": [{"url": "https://cdn.modrinth.com/data/R5T1RmWr/images/016b3d217f50dc232c8a4abdfc3cc2008cb361ad.png", "raw_url": "https://cdn.modrinth.com/data/R5T1RmWr/images/016b3d217f50dc232c8a4abdfc3cc2008cb361ad.png", "featured": false, "title": "<PERSON><PERSON><PERSON>", "created": "2023-02-10T05:36:14.339451Z", "ordering": 0.0}, {"url": "https://cdn.modrinth.com/data/R5T1RmWr/images/82d75932d9a6b15013fab5674e6a25c2a680fdf7.png", "raw_url": "https://cdn.modrinth.com/data/R5T1RmWr/images/82d75932d9a6b15013fab5674e6a25c2a680fdf7.png", "featured": false, "title": "Trinkets Shulker Box", "created": "2023-02-10T05:36:04.918600Z", "ordering": 0.0}]}, "modrinthVersion": {"id": "M83bv1l4", "project_id": "R5T1RmWr", "author_id": "zbpHS3ry", "name": "shulkerboxslot-fabric-6.0.0+1.21.1", "version_number": "6.0.0+1.21.1", "changelog": "The format is based on [Keep a Changelog](http://keepachangelog.com/en/1.0.0/) and this project adheres to [Semantic Versioning](http://semver.org/spec/v2.0.0.html).\n\nThis is a copy of the changelog for the most recent version. For the full version history, go [here](https://github.com/illusivesoulworks/shulkerboxslot/blob/1.20.x/CHANGELOG.md).\n\n## [6.0.0+1.21.1] - 2024.10.11\n### Changed\n- Updated to Minecraft 1.21.1\n", "date_published": "2024-10-11T07:49:38.612644Z", "version_type": "release", "files": [{"hashes": {"sha1": "0e9873bfe6458d69c847082fb4c04dbfb00f7ec9", "sha512": "8fb9f470b43a403b2fca706fe853324b83812edf51ebe86c8457c365f809b8922b91f9a059477a66db03bdd970b361c6b1078826698b4d637ca15f4afcbb3de4"}, "url": "https://cdn.modrinth.com/data/R5T1RmWr/versions/M83bv1l4/shulkerboxslot-fabric-6.0.0%2B1.21.1.jar", "filename": "shulkerboxslot-fabric-6.0.0+1.21.1.jar", "primary": true, "size": 585960}, {"hashes": {"sha1": "dd6beeec91a8bd688ba17d7cdb02bf570f304451", "sha512": "a3ced7a44a040e5f00b6de5581ead7bfbf59153cd85e79e19d0a8770c4b471abbaaad9825961d00ef4fa72982d075e1a67d3a29a3a691c31c202a9ffc5969e5e"}, "url": "https://cdn.modrinth.com/data/R5T1RmWr/versions/M83bv1l4/shulkerboxslot-fabric-6.0.0%2B1.21.1-sources.jar", "filename": "shulkerboxslot-fabric-6.0.0+1.21.1-sources.jar", "primary": false, "size": 67028}], "dependencies": [{"project_id": "5aaWibi9", "dependency_type": "required"}, {"project_id": "P7dR8mSH", "dependency_type": "required"}], "game_versions": ["1.21", "1.21.1"], "loaders": ["fabric"]}}, {"name": "Konkrete", "version": "[<PERSON><PERSON><PERSON>] v1.9.9 MC 1.21", "optional": true, "file": "konkrete_fabric_1.9.9_MC_1.21.jar", "type": "mods", "description": "Just another boring library mod.", "disabled": false, "userAdded": true, "wasSelected": true, "skipped": false, "curseForgeProjectId": 416797, "curseForgeFileId": 5430434, "curseForgeProject": {"id": 416797, "name": "<PERSON><PERSON><PERSON><PERSON> [<PERSON><PERSON><PERSON>]", "authors": [{"id": 100802238, "name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://www.curseforge.com/members/keksuccino"}], "gameId": 432, "summary": "Just another boring library mod.", "categories": [{"name": "API and Library", "slug": "library-api", "url": "https://www.curseforge.com/minecraft/mc-mods/library-api", "dateModified": "2014-05-23T03:21:44.06Z", "gameId": 432, "isClass": false, "id": 421, "iconUrl": "https://media.forgecdn.net/avatars/6/36/635351496947765531.png", "parentCategoryId": 6, "classId": 6}], "status": 4, "primaryCategoryId": 421, "classId": 6, "slug": "konkrete-fabric", "isFeatured": false, "dateModified": "2025-06-18T23:21:12.07Z", "dateCreated": "2020-10-31T19:32:52.77Z", "dateReleased": "2025-06-18T23:15:21.72Z", "links": {"websiteUrl": "https://www.curseforge.com/minecraft/mc-mods/konkrete-fabric", "wikiUrl": "", "sourceUrl": "https://github.com/Keksuccino/Konkrete"}, "logo": {"id": 310138, "description": "", "thumbnailUrl": "https://media.forgecdn.net/avatars/thumbnails/310/138/256/256/637397695728123168.png", "title": "637397695728123168.png", "url": "https://media.forgecdn.net/avatars/310/138/637397695728123168.png", "modId": 416797}, "allowModDistribution": true, "screenshots": [], "mainFileId": 6668778}, "curseForgeFile": {"id": 5430434, "gameId": 432, "isAvailable": true, "displayName": "[<PERSON><PERSON><PERSON>] v1.9.9 MC 1.21", "fileName": "konkrete_fabric_1.9.9_MC_1.21.jar", "releaseType": 1, "fileStatus": 4, "fileDate": "2024-06-15T08:08:46.427Z", "fileLength": 786695, "dependencies": [{"fileId": 0, "modId": 306612, "relationType": 3}], "alternateFileId": 5430435, "modules": [{"fingerprint": 3289763903, "name": "META-INF"}, {"fingerprint": 2077150322, "name": "assets"}, {"fingerprint": 2211845012, "name": "de"}, {"fingerprint": 3753108714, "name": "fabric.mod.json"}, {"fingerprint": 1855543675, "name": "konkrete.accesswidener"}, {"fingerprint": 3578584023, "name": "konkrete.fabric.mixins.json"}, {"fingerprint": 1712148904, "name": "konkrete.mixins.json"}, {"fingerprint": 50462880, "name": "konkrete.refmap.json"}, {"fingerprint": 1541347227, "name": "konkrete_logo_128x128.png"}, {"fingerprint": 4026528315, "name": "pack.mcmeta"}], "isServerPack": false, "hashes": [{"value": "c521e23a8be17a941f4f95a76feba9df5e3de5d0", "algo": 1}, {"value": "287ee9cfc6015f706672fd8c20f5ef9c", "algo": 2}], "sortableGameVersions": [{"gameVersionPadded": "**********.**********", "gameVersion": "1.21", "gameVersionReleaseDate": "2024-06-14T00:00:00Z", "gameVersionName": "1.21"}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-09-01T00:00:00Z", "gameVersionName": "<PERSON><PERSON><PERSON>"}, {"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.1", "gameVersionReleaseDate": "2024-08-08T15:17:53.787Z", "gameVersionName": "1.21.1"}], "gameVersions": ["1.21", "<PERSON><PERSON><PERSON>", "1.21.1"], "fileFingerprint": 3517116216, "modId": 416797}, "modrinthProject": {"id": "J81TRJWm", "slug": "konkrete", "project_type": "mod", "team": "o00O3vS9", "title": "Konkrete", "description": "Just another boring library mod.", "body": "<p style=\"text-align: center;\"><a href=\"https://discord.gg/rhayah27GC\"><img src=\"https://discordapp.com/api/guilds/704163135787106365/widget.png?style=banner2\" /></a> <a href=\"https://twitter.com/keksuccino\"><img src=\"https://user-images.githubusercontent.com/35544624/132924153-df28357d-6816-48a2-96a8-594333d3b075.png\" /></a> <a href=\"https://www.patreon.com/keksuccino\"><img src=\"https://user-images.githubusercontent.com/35544624/132924155-25fe4269-5936-4cac-88cf-5d6069e0443a.png\" /></a> <a href=\"https://paypal.me/TimSchroeter\"><img src=\"https://user-images.githubusercontent.com/35544624/132924156-ec4300ea-7e10-40de-a271-8effb8fbf5cf.png\" /></a></p>\n\n\n## Dependencies\n\n### Fabric\n- [Fabric API](https://modrinth.com/mod/fabric-api) (Only for Fabric)\n\n### Forge 1.12\n- [MixinBootstrap](https://modrinth.com/mod/mixinbootstrap)\n\n\n## About\n\nKonkrete is a library mod used as base for my other mods.<br>\nThis mod does nothing on its own and its only purpose is to be the base of other mods.\n\nTechnically, you are free to use this mod as base mod for your own mods, but there's **currently no documentation** for it, so it would probably be a very annoying experience.\n\n\n## Bug Reports and Requests\n\nYou can report bugs and request features on [GitHub](https://github.com/Keksuccino/Konkrete/issues)!\n\n\n## Copyright\n\nKonkrete Copyright © 2020-2023 Keksuccino.<br>\nKonkrete is licensed under Apache-2.0.\n\nOpen Imaging Copyright © 2014 Dhyan Blum.<br>\nOpen Imaging is licensed under Apache-2.0.\n\n\nJsonPath Copyright © 2017 Jayway.<br>\nJsonPath is licensed under Apache-2.0.\n\nJson-smart Copyright © netplex.<br>\nJson-smart is licensed under Apache-2.0.\n\n\n## Server Needed?\n\nYou need a Minecraft server to play with your friends but it's just too much work to setup one on your own?<br>\nNo problem, just rent an ready-to-use server and start playing in a snap!\n\nJust click on the image below and use the code **keksuccino** to get **25% off** your first month!\n\n<a href=\"https://bisecthosting.com/keksuccino\" target=\"_blank\" rel=\"noopener noreferrer\"><img src=\"https://user-images.githubusercontent.com/35544624/100003401-505d5300-2dc6-11eb-9444-3dd117a8aef9.png\" width=\"241\" height=\"244\" /></a><span style=\"color: #808080;\"><br /></span></p>", "published": "2022-11-13T22:15:04.878426Z", "updated": "2025-06-18T23:16:21.961865Z", "status": "approved", "license": {"id": "Apache-2.0", "name": "Apache License 2.0"}, "client_side": "optional", "server_side": "optional", "categories": ["library"], "loaders": ["fabric", "forge", "neoforge"], "icon_url": "https://cdn.modrinth.com/data/J81TRJWm/4c569bccc9c3b2806060b3d48f7d20447818c941_96.webp", "issues_url": "https://github.com/Keksuccino/Konkrete/issues", "source_url": "https://github.com/Keksuccino/Konkrete", "discord_url": "https://discord.gg/UzmeWkD", "donation_urls": [], "gallery": []}, "modrinthVersion": {"id": "Qvrc7DuG", "project_id": "J81TRJWm", "author_id": "k0LcYHWE", "name": "[<PERSON><PERSON><PERSON>] v1.9.9 MC 1.21", "version_number": "1.9.9-1.21-fabric", "changelog": "\n<br>CHANGELOG: https://github.com/Keksuccino/Konkrete/blob/main/changelog.txt\n\n<br>", "date_published": "2024-06-15T08:08:52.591973Z", "version_type": "release", "files": [{"hashes": {"sha1": "c521e23a8be17a941f4f95a76feba9df5e3de5d0", "sha512": "6d4c65a0a9d51b2b956ba61f926f642e901573c9621289cf7a387996370fcab23c69ee6e698596965e9bd68b86b1e96e44295172a880a979105347719228ecf8"}, "url": "https://cdn.modrinth.com/data/J81TRJWm/versions/Qvrc7DuG/konkrete_fabric_1.9.9_MC_1.21.jar", "filename": "konkrete_fabric_1.9.9_MC_1.21.jar", "primary": true, "size": 786695}, {"hashes": {"sha1": "19f788ca11a9a8f160e5ce5eea91393eaad34081", "sha512": "82420f3fbb2f8f1e4532eecc961e42fc541b00db0c73fd7bdf3aa15186bc57dd4a2b3f52bab48de82adf8a849f6ede68ad53e0768f3ffdadd9e434e1cb57f87e"}, "url": "https://cdn.modrinth.com/data/J81TRJWm/versions/Qvrc7DuG/sources_konkrete_fabric_1.9.9_MC_1.21.jar", "filename": "sources_konkrete_fabric_1.9.9_MC_1.21.jar", "primary": false, "size": 355117}], "dependencies": [{"project_id": "P7dR8mSH", "dependency_type": "required"}], "game_versions": ["1.21", "1.21.1"], "loaders": ["fabric"]}}, {"name": "<PERSON>", "version": "5.2.4+mc1.21", "optional": true, "file": "bobby-5.2.4+mc1.21.jar", "type": "mods", "description": "Allows for render distances greater than the server's view-distance", "disabled": false, "userAdded": true, "wasSelected": true, "skipped": false, "curseForgeProjectId": 409301, "curseForgeFileId": 5661558, "curseForgeProject": {"id": 409301, "name": "<PERSON>", "authors": [{"id": 100050958, "name": "Johni0702", "url": "https://www.curseforge.com/members/johni0702"}], "gameId": 432, "summary": "Allows for render distances greater than the server's view-distance setting.", "categories": [{"name": "Cosmetic", "slug": "cosmetic", "url": "https://www.curseforge.com/minecraft/mc-mods/cosmetic", "dateModified": "2014-05-08T17:42:35.597Z", "gameId": 432, "isClass": false, "id": 424, "iconUrl": "https://media.forgecdn.net/avatars/6/39/635351497555976928.png", "parentCategoryId": 6, "classId": 6}], "status": 4, "primaryCategoryId": 424, "classId": 6, "slug": "bobby", "isFeatured": false, "dateModified": "2025-06-23T19:13:39.847Z", "dateCreated": "2020-09-21T19:44:48.267Z", "dateReleased": "2025-06-23T19:11:01.21Z", "links": {"websiteUrl": "https://www.curseforge.com/minecraft/mc-mods/bobby", "wikiUrl": "", "issuesUrl": "https://github.com/Johni0702/bobby/issues", "sourceUrl": "https://github.com/Johni0702/bobby"}, "logo": {"id": 301608, "description": "", "thumbnailUrl": "https://media.forgecdn.net/avatars/thumbnails/301/608/256/256/637363142883032981.png", "title": "637363142883032981.png", "url": "https://media.forgecdn.net/avatars/301/608/637363142883032981.png", "modId": 409301}, "allowModDistribution": true, "screenshots": [], "mainFileId": 6688155}, "curseForgeFile": {"id": 5661558, "gameId": 432, "isAvailable": true, "displayName": "bobby-5.2.4+mc1.21.jar", "fileName": "bobby-5.2.4+mc1.21.jar", "releaseType": 1, "fileStatus": 4, "fileDate": "2024-08-25T09:33:48.147Z", "fileLength": 863192, "dependencies": [{"fileId": 0, "modId": 348521, "relationType": 2}, {"fileId": 0, "modId": 383301, "relationType": 1}, {"fileId": 0, "modId": 308702, "relationType": 2}, {"fileId": 0, "modId": 394468, "relationType": 2}], "alternateFileId": 0, "modules": [{"fingerprint": **********, "name": "META-INF"}, {"fingerprint": **********, "name": "LICENSE.md"}, {"fingerprint": **********, "name": "assets"}, {"fingerprint": **********, "name": "bobby-sodium05-compat.mixins.json"}, {"fingerprint": **********, "name": "bobby-sodium05-compat.refmap.json"}, {"fingerprint": **********, "name": "bobby-sodium06-compat.mixins.json"}, {"fingerprint": **********, "name": "bobby-sodium06-compat.refmap.json"}, {"fingerprint": **********, "name": "bobby.mixins.json"}, {"fingerprint": 928537237, "name": "bobby.refmap.json"}, {"fingerprint": 721793473, "name": "de"}, {"fingerprint": **********, "name": "fabric.mod.json"}], "isServerPack": false, "hashes": [{"value": "dfa517fdb6e3fd2893f5d40f2c06cf01fb2a5db1", "algo": 1}, {"value": "2f842ec35ec49b869047786de5551420", "algo": 2}], "sortableGameVersions": [{"gameVersionPadded": "**********.**********", "gameVersion": "1.21", "gameVersionReleaseDate": "2024-06-14T00:00:00Z", "gameVersionName": "1.21"}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-09-01T00:00:00Z", "gameVersionName": "<PERSON><PERSON><PERSON>"}], "gameVersions": ["1.21", "<PERSON><PERSON><PERSON>"], "fileFingerprint": 1436604534, "modId": 409301}, "modrinthProject": {"id": "M08ruV16", "slug": "bobby", "project_type": "mod", "team": "Gk6pR1Ua", "title": "<PERSON>", "description": "Allows for render distances greater than the server's view-distance", "body": "<PERSON> is a Minecraft mod which allows for render distances greater than the server's view-distance setting.\n\n![The Province of Wynn at 96 chunks render distance](https://i.johni0702.de/7FlGK.png \"The Province of Wynn at 96 chunks render distance\")\n\nIt accomplishes this goal by recording and storing (in `.minecraft/.bobby`) all chunks sent by the server which it\nthen can load and display at a later point when the chunk is outside the server's view-distance.\n\n\nOptionally, it can also use an existing single player world to load in chunks which the server has never sent before.\nTo make use of this feature, simply rename the world folder (not the name of the world! the name of its folder!) to \"bobby-fallback\".\n\n\n<PERSON> automatically reloads its config file when it is changed.\nFor an in-game config screen, install [ModMenu] and [ClothConfig].\n\n[ModMenu]: https://modrinth.com/mod/modmenu\n[ClothConfig]: https://modrinth.com/mod/cloth-config", "published": "2021-01-01T16:31:38.745984Z", "updated": "2025-06-23T19:11:08.854024Z", "status": "approved", "license": {"id": "LGPL-3.0-only", "name": "GNU Lesser General Public License v3.0 only"}, "client_side": "required", "server_side": "unsupported", "categories": [], "loaders": ["fabric"], "icon_url": "https://cdn.modrinth.com/data/M08ruV16/icon.png", "issues_url": "https://github.com/Johni0702/bobby/issues", "source_url": "https://github.com/Johni0702/bobby", "donation_urls": [], "gallery": []}, "modrinthVersion": {"id": "oeSOphtG", "project_id": "M08ruV16", "author_id": "ZExHEtu6", "name": "5.2.4+mc1.21", "version_number": "5.2.4+mc1.21", "changelog": "- Add support for Sodium 0.6.0 (#308)", "date_published": "2024-08-25T09:33:58.594353Z", "version_type": "release", "files": [{"hashes": {"sha1": "dfa517fdb6e3fd2893f5d40f2c06cf01fb2a5db1", "sha512": "414436bf14ee9ad76ff2f849ffb5a2acda122a7588231d14dfbd27253924d08c22368c73ebd4b0048209d14682579be58743993265daaface4fdec6ec8e6e7ee"}, "url": "https://cdn.modrinth.com/data/M08ruV16/versions/oeSOphtG/bobby-5.2.4%2Bmc1.21.jar", "filename": "bobby-5.2.4+mc1.21.jar", "primary": true, "size": 863192}], "dependencies": [{"project_id": "mOgUt4GM", "dependency_type": "optional"}, {"project_id": "AANobbMI", "dependency_type": "optional"}, {"project_id": "9s6osm5g", "dependency_type": "optional"}], "game_versions": ["1.21", "1.21.1"], "loaders": ["fabric"]}}, {"name": "Text Placeholder API", "version": "placeholder-api-2.4.2+1.21.jar", "optional": true, "file": "placeholder-api-2.4.2+1.21.jar", "type": "mods", "description": "Placeholder and Text manipulation library for your Minecraft mods.", "disabled": false, "userAdded": true, "wasSelected": true, "skipped": false, "curseForgeProjectId": 1037459, "curseForgeFileId": 6131327, "curseForgeProject": {"id": 1037459, "name": "Text Placeholder API", "authors": [{"id": 61679721, "name": "Patbox", "url": "https://www.curseforge.com/members/patbox"}], "gameId": 432, "summary": "Placeholder and Text manipulation library for your Minecraft mods.", "categories": [{"name": "API and Library", "slug": "library-api", "url": "https://www.curseforge.com/minecraft/mc-mods/library-api", "dateModified": "2014-05-23T03:21:44.06Z", "gameId": 432, "isClass": false, "id": 421, "iconUrl": "https://media.forgecdn.net/avatars/6/36/635351496947765531.png", "parentCategoryId": 6, "classId": 6}], "status": 4, "primaryCategoryId": 421, "classId": 6, "slug": "text-placeholder-api", "isFeatured": false, "dateModified": "2025-06-22T19:58:43.653Z", "dateCreated": "2024-06-15T14:58:19.497Z", "dateReleased": "2025-06-22T19:54:48.797Z", "links": {"websiteUrl": "https://www.curseforge.com/minecraft/mc-mods/text-placeholder-api"}, "logo": {"id": 1015016, "description": "", "thumbnailUrl": "https://media.forgecdn.net/avatars/thumbnails/1015/16/256/256/638540602996666426.png", "title": "638540602996666426.png", "url": "https://media.forgecdn.net/avatars/1015/16/638540602996666426.png", "modId": 1037459}, "allowModDistribution": true, "screenshots": [], "mainFileId": 6684499}, "curseForgeFile": {"id": 6131327, "gameId": 432, "isAvailable": true, "displayName": "placeholder-api-2.4.2+1.21.jar", "fileName": "placeholder-api-2.4.2+1.21.jar", "releaseType": 1, "fileStatus": 4, "fileDate": "2025-01-27T19:08:23.927Z", "fileLength": 232325, "dependencies": [], "alternateFileId": 0, "modules": [{"fingerprint": **********, "name": "META-INF"}, {"fingerprint": **********, "name": "LICENSE_placeholder-api"}, {"fingerprint": 466124403, "name": "assets"}, {"fingerprint": 453075339, "name": "eu"}, {"fingerprint": **********, "name": "fabric.mod.json"}], "isServerPack": false, "hashes": [{"value": "aa1b9a49f9f190dfe4825c502b150817dd2998c3", "algo": 1}, {"value": "4de8568a088a276c94168514d625413d", "algo": 2}], "sortableGameVersions": [{"gameVersionPadded": "**********.**********", "gameVersion": "1.21", "gameVersionReleaseDate": "2024-06-14T00:00:00Z", "gameVersionName": "1.21"}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-09-01T00:00:00Z", "gameVersionName": "<PERSON><PERSON><PERSON>"}, {"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.1", "gameVersionReleaseDate": "2024-08-08T15:17:53.787Z", "gameVersionName": "1.21.1"}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-05-26T00:00:00Z", "gameVersionName": "Quilt"}], "gameVersions": ["1.21", "<PERSON><PERSON><PERSON>", "1.21.1", "Quilt"], "fileFingerprint": 2589927179, "modId": 1037459}, "modrinthProject": {"id": "eXts2L7r", "slug": "placeholder-api", "project_type": "mod", "team": "tA4rMuIL", "title": "Text Placeholder API", "description": "Placeholder and Text manipulation library for your Minecraft mods.", "body": "# About Placeholder API\n\nIt's a small, JIJ-able API that allows creation and parsing placeholders within strings and Minecraft Text Components.\nPlaceholder API uses a simple format of `%modid:type%` or `%modid:type data%` (`%modid:type/data%` prior to 1.19).\nIt also includes simple, general usage text format indented for simplifying user input in configs/chats/etc.\n\n## For users\n\nIt allows users to configure multiple mods in similar way without losing compatibility between mods.\nPlaceholders allow changing what and where any information is present within compatible mods.\n\nAdditionally, Simplified Text Format allows to style them in readable way without the requirement of writing JSON manually or using\ngenerators.\n- [Using placeholders](https://placeholders.pb4.eu/user/general)\n- [Default placeholder list](https://placeholders.pb4.eu/user/default-placeholders)\n- [Mod placeholder list](https://placeholders.pb4.eu/user/mod-placeholders)\n- [QuickText](https://placeholders.pb4.eu/user/quicktext)\n- [Simplified Text Format](https://placeholders.pb4.eu/user/text-format)\n\nMods generally using this api should just include it directly, but you can downloaded updated version from here if needed!\n\n## For developers\n\nUsage of Placeholder API is a simple way to achieve good mod compatibility without having to implement\nmultiple mod specific apis. Additionally, the placeholder parsing system can be used for replacing\nown static (or dynamic placeholders) in Text created by player or read from config. This with combination\nof Simplified Text Format allows creating great user/admin experience.\n\n- [Getting Started](https://placeholders.pb4.eu/dev/getting-started)\n- [Adding placeholders](https://placeholders.pb4.eu/dev/adding-placeholders)\n- [Parsing placeholders](https://placeholders.pb4.eu/dev/parsing-placeholders)\n- [TextNodes and NodeParsers](https://placeholders.pb4.eu/dev/text-nodes)\n- [Using Simplified Text Format (TextParserV1)](https://placeholders.pb4.eu/dev/text-format)\n\n*[JIJ]: Jar-in-Jar", "published": "2023-05-07T07:16:09.821727Z", "updated": "2025-06-22T19:54:46.773732Z", "status": "approved", "license": {"id": "LGPL-3.0-only", "name": "GNU Lesser General Public License v3.0 only"}, "client_side": "optional", "server_side": "optional", "categories": ["library"], "loaders": ["fabric", "quilt"], "icon_url": "https://cdn.modrinth.com/data/eXts2L7r/e9c9990896e6422bffc5f73d2c41b8f077348f83.png", "issues_url": "https://github.com/Patbox/TextPlaceholderAPI/issues", "source_url": "https://github.com/Patbox/TextPlaceholderAPI", "wiki_url": "https://placeholders.pb4.eu/", "discord_url": "https://pb4.eu/discord", "donation_urls": [{"id": "ko-fi", "platform": "Ko-fi", "url": "https://ko-fi.com/patbox"}], "gallery": []}, "modrinthVersion": {"id": "U5bhVym2", "project_id": "eXts2L7r", "author_id": "L8RLwrF2", "name": "2.4.2+1.21", "version_number": "2.4.2+1.21", "changelog": "**Full Changelog**: https://github.com/Patbox/TextPlaceholderAPI/compare/2.4.1+1.21...2.4.2+1.21", "date_published": "2025-01-27T19:08:23.196696Z", "version_type": "release", "files": [{"hashes": {"sha1": "aa1b9a49f9f190dfe4825c502b150817dd2998c3", "sha512": "fc13d3a5c048dbaab86318edaf8b6c6b46ef9f1d367e8f063d19f5a9b0da66c5ae419d92c8c4608edc89a01eb44d91ffcf017fea73f39b222cbd85e82f70a233"}, "url": "https://cdn.modrinth.com/data/eXts2L7r/versions/U5bhVym2/placeholder-api-2.4.2%2B1.21.jar", "filename": "placeholder-api-2.4.2+1.21.jar", "primary": true, "size": 232325}], "dependencies": [], "game_versions": ["1.21", "1.21.1"], "loaders": ["fabric", "quilt"]}}, {"name": "<PERSON><PERSON>", "version": "v11.0.3 for 1.21", "optional": true, "file": "modmenu-11.0.3.jar", "type": "mods", "description": "Adds a mod menu to view the list of mods you have installed.", "disabled": false, "userAdded": true, "wasSelected": true, "skipped": false, "curseForgeProjectId": 308702, "curseForgeFileId": 5810603, "curseForgeProject": {"id": 308702, "name": "<PERSON><PERSON>", "authors": [{"id": 18470197, "name": "Prospector", "url": "https://www.curseforge.com/members/prospector"}], "gameId": 432, "summary": "Adds a mod menu to view the list of mods you have installed.", "categories": [{"name": "Miscellaneous", "slug": "mc-miscellaneous", "url": "https://www.curseforge.com/minecraft/mc-mods/mc-miscellaneous", "dateModified": "2014-06-15T04:27:14.543Z", "gameId": 432, "isClass": false, "id": 425, "iconUrl": "https://media.forgecdn.net/avatars/6/40/635351497693711265.png", "parentCategoryId": 6, "classId": 6}], "status": 4, "primaryCategoryId": 425, "classId": 6, "slug": "modmenu", "isFeatured": false, "dateModified": "2025-06-20T23:39:37.11Z", "dateCreated": "2018-12-09T23:28:50.327Z", "dateReleased": "2025-06-20T23:35:24.09Z", "links": {"websiteUrl": "https://www.curseforge.com/minecraft/mc-mods/modmenu", "wikiUrl": "https://github.com/terraformersmc/modmenu?tab=readme-ov-file#developers", "issuesUrl": "https://github.com/Prospector/ModMenu/issues", "sourceUrl": "https://github.com/Prospector/ModMenu"}, "logo": {"id": 1163963, "description": "", "thumbnailUrl": "https://media.forgecdn.net/avatars/thumbnails/1163/963/256/256/638733598889810840.jpg", "title": "638733598889810840.jpg", "url": "https://media.forgecdn.net/avatars/1163/963/638733598889810840.jpg", "modId": 308702}, "allowModDistribution": true, "screenshots": [], "mainFileId": 6293895}, "curseForgeFile": {"id": 5810603, "gameId": 432, "isAvailable": true, "displayName": "v11.0.3 for 1.21", "fileName": "modmenu-11.0.3.jar", "releaseType": 1, "fileStatus": 4, "fileDate": "2024-10-14T04:11:10.747Z", "fileLength": 973066, "dependencies": [{"fileId": 0, "modId": 1037459, "relationType": 3}, {"fileId": 0, "modId": 306612, "relationType": 3}], "alternateFileId": 0, "modules": [{"fingerprint": **********, "name": "META-INF"}, {"fingerprint": **********, "name": "LICENSE"}, {"fingerprint": **********, "name": "assets"}, {"fingerprint": **********, "name": "com"}, {"fingerprint": 106944541, "name": "fabric.mod.json"}, {"fingerprint": **********, "name": "high_contrast"}, {"fingerprint": 444592166, "name": "mixins.modmenu.json"}, {"fingerprint": **********, "name": "modmenu-refmap.json"}, {"fingerprint": **********, "name": "programmer_art"}], "isServerPack": false, "hashes": [{"value": "6213120772192040a0aeb5c7cfe330c068605b1f", "algo": 1}, {"value": "d9a3c1f26c571e3eb07ef2ed3e89d1b0", "algo": 2}], "sortableGameVersions": [{"gameVersionPadded": "**********.**********", "gameVersion": "1.21", "gameVersionReleaseDate": "2024-06-14T00:00:00Z", "gameVersionName": "1.21"}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-09-01T00:00:00Z", "gameVersionName": "<PERSON><PERSON><PERSON>"}, {"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.1", "gameVersionReleaseDate": "2024-08-08T15:17:53.787Z", "gameVersionName": "1.21.1"}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-05-26T00:00:00Z", "gameVersionName": "Quilt"}], "gameVersions": ["1.21", "<PERSON><PERSON><PERSON>", "1.21.1", "Quilt"], "fileFingerprint": 2497936418, "modId": 308702}, "modrinthProject": {"id": "mOgUt4GM", "slug": "modmenu", "project_type": "mod", "team": "VMz4FpgB", "title": "<PERSON><PERSON>", "description": "Adds a mod menu to view the list of mods you have installed.", "body": "# Mod Menu\n![Screenshot of the Mods screen, showing a list of a few mods on the left side below a search bar and filters button, where <PERSON><PERSON>u is selected. On the right side of the screen, it shows more details about the mod, such as authors, a description, links, credits, and a button to configure the mod.](https://cdn.modrinth.com/data/mOgUt4GM/images/155dd2b006883b168b1279ec0ff21e753946518b.png)\n\nMod Menu lets you view the mods you have installed and, if supported by the mod, enables quick and easy access to the mod's config screens.\n\nMod Menu also supports some more advanced features, such as translatable mod names and descriptions, support for [QuickText formatting](https://placeholders.pb4.eu/user/quicktext/) in mod descriptions thanks to [Patbox](https://ko-fi.com/patbox)'s [Text Placeholder API](https://modrinth.com/mod/placeholder-api), filters library mods out from regular mods, a mod update checker for mods hosted on Modrinth or that provide their own update sources, and deep configuration for all the features we provide.\n\n### Supported Platforms\nMod Menu is currently available for Fabric or Quilt on Minecraft: Java Edition 1.14 or newer.\n\n## Developers\nMod Menu includes a number of APIs for developers to improve how their mod appears in Mod Menu. These come in the form of language keys, JSON metadata, and even a Java API.\n\n### Translation API\nYou can translate your mod's name, summary, and description all without touching any Java code. Simply add translation keys in the supported format to any language you'd like.\n\n<details>\n<summary>Translation API Documentation</summary>\n\nHere's an example of Mod Menu's translations into Pirate Speak. To create your own, simply replace `modmenu` at the end (***NOT*** the one in the beginning) of the translation key with your own mod ID, for example `modmenu.descriptionTranslation.traverse`.\n\n`en_pt.json`\n```json\n\"modmenu.nameTranslation.modmenu\": \"Menu o' mods!\",\n\"modmenu.descriptionTranslation.modmenu\": \"Menu o' mods ye installed matey!\",\n\"modmenu.summaryTranslation.modmenu\": \"Menu o' mods ye installed matey!\"\n```\n\n> The summary translation is redundant here and does not need to be included because it's the same as the description, but it was included to show that you may translate the summary (a short, one-sentence description of the mod) separately from the description, even in English!\n\n</details>\n\n\n\n### Fabric Metadata API\nThere's a number of things you can add just with metadata in your `fabric.mod.json`.\n\nAll of these are added to a custom block in your `fabric.mod.json` for Mod Menu's metadata. Here's an example usage of many of the features this API provides:\n\n`fabric.mod.json`\n```json5\n{\n  ...\n  \"custom\": {\n    \"modmenu\": {\n      \"links\": {\n        \"modmenu.discord\": \"https://discord.gg/jEGF5fb\"\n      },\n      \"badges\": [ \"library\", \"deprecated\" ],\n      \"parent\": {\n        \"id\": \"example-api\",\n        \"name\": \"Example API\",\n        \"description\": \"Modular example library\",\n        \"icon\": \"assets/example-api-module-v1/parent_icon.png\",\n        \"badges\": [ \"library\" ]\n      },\n      \"update_checker\": true\n    }\n  }\n}\n```\n\n<details>\n<summary>Fabric Metadata API Documentation</summary>\n\n#### Badges (`\"badges\": [ ]`)\nWhile the `Client` badge is added automatically to mods set as client-side only (set `\"environment\": \"client\"` in `fabric.mod.json` to do this.), other badges such as the `Library` and `Deprecated` badges require definition here.\n\nSupported values:\n- `library` - should be assigned to mods that are purely dependencies for other mods that should not be shown to the user by default unless they toggle them on.\n- `deprecated` - should be assigned to mods that exist purely for legacy reasons, such as an old API module or such.\n\nAny others will be ignored, and Mod Menu does not support adding your own badges. You may open an issue [here](https://github.com/TerraformersMC/ModMenu/issues) if you have a compelling use case for a new badge.\n\n#### Links (`\"links\": { }`)\nThe `links` object allows mod authors to add custom hyperlinks to the end of their description. If you specify a `sources` contact in the official `fabric.mod.json` metadata, it will also be included in the links section.\n\nAny key in the `links` object will be included in the links section, with the key being used as a translation key. For example, this:\n\n`fabric.mod.json`\n```json\n\"custom\": {\n    \"modmenu\": {\n        \"links\": {\n          \"modmenu.discord\": \"https://discord.gg/jEGF5fb\"\n        }\n    }\n}\n```\nwill show as a link with the text \"Discord\", since \"Discord\" is the English translation of \"modmenu.discord\" provided by Mod Menu.\n\nMod Menu provides several default translations that can be used for links. A full list can be seen in Mod Menu's language file [here](https://github.com/TerraformersMC/ModMenu/blob/-/src/main/resources/assets/modmenu/lang/en_us.json). All default link translation keys take the form `modmenu.<type>`.\n\nYou can also provide your own translations if you would like to add custom links. Make sure to use ***your own namespace*** (as opposed to `modmenu`) for any custom keys.\n\n#### Parents (`\"parent\": \"mod_id\" or { }`)\n<img align=\"right\" width=\"400\" src=\"https://i.imgur.com/ZutCprf.png\">\n\nParents are used to display a mod as a child of another one. This is meant to be used for mods divided into different modules. The following element in a `fabric.mod.json` will define the mod as a child of the mod 'flamingo': \n\n`fabric.mod.json`\n```json\n\"custom\": {\n    \"modmenu\": {\n        \"parent\": \"flamingo\"\n    }\n}\n```\n\nHowever, if you want to group mods under a parent, but the parent isn't an actual mod, you can do that too. In the example below, a mod is defining metadata for a parent. Make sure that this metadata is included in all of the children that use the fake/dummy parent. This can also be used as a fallback for an optional parent, it will be replace by the mod's real metadata if present.\n\n\n`fabric.mod.json`\n```json\n\"custom\": {\n    \"modmenu\": {\n        \"parent\": {\n            \"id\": \"this-mod-isnt-real\",\n            \"name\": \"Fake Mod\",\n            \"description\": \"Do cool stuff with this fake mod\",\n            \"icon\": \"assets/real-mod/fake-mod-icon.png\",\n            \"badges\": [ \"library\" ]\n        }\n    }\n}\n```\n\nDummy parent mods only support the following metadata:\n- `id` (String)\n- `name` (String)\n- `description` (String)\n- `icon` (String)\n- `badges` (Array of Strings)\n\n\n#### Disable update checker (`\"update_checker\": false`)\nBy default, Mod Menu's update checker will use the hash of your mod's jar to lookup the latest version on Modrinth. If it finds a matching project, it will check for the latest version that supports your mod loader and Minecraft version, and if it has a different hash from your existing file, it will prompt the user that there is an update available.\n\nYou can disable the update checker by setting `update_checker` to false in your Mod Menu metadata like so:\n\n`fabric.mod.json`\n```json\n\"custom\": {\n    \"modmenu\": {\n        \"update_checker\": false\n    }\n}\n```\n\n</details>\n\n### Quilt Metadata API\nSince Mod Menu supports Quilt as well, the same APIs in the Fabric Metadata API section are also available for Quilt mods, but the format for custom metadata is slightly different. \n\nInstead of a `\"modmenu\"` block inside of a `\"custom\"` block, you put the `\"modmenu\"` block as an element in the root object. So it should look like:\n\n`quilt.mod.json`\n```json5\n{\n  ...\n  \"modmenu\": {\n    // Here's where your links, badges, etc. stuff goes\n  }\n}\n```\n\n### Java API\nTo use the Java API, you'll need to add Mod Menu as a compile-time dependency in your gradle project. This won't make your mod require Mod Menu, but it'll be present in your environment for you to test with.\n\n`build.gradle`\n```gradle\n// Add the Terraformers maven repo to your repositories block\nrepositories {\n  maven {\n    name = \"Terraformers\"\n    url = \"https://maven.terraformersmc.com/\"\n  }\n}\n\n// Add Mod Menu as a dependency in your environment\ndependencies {\n  modImplementation(\"com.terraformersmc:modmenu:${project.modmenu_version}\")\n}\n```\nThen, define the version of Mod Menu you're using in your `gradle.properties`. You can get the latest version number [here](https://modrinth.com/mod/modmenu/version/latest), but you may need a different version if you're not using the latest Minecraft version. See the [versions page](https://modrinth.com/mod/modmenu/versions) for a full list of versions.\n\n`gradle.properties`\n```properties\nmodmenu_version=VERSION_NUMBER_HERE\n```\n> If you don't want it in your environment for testing but still want to compile against Mod Menu for using the Java API, you can use `modCompileOnly` instead of `modImplementation` (this will work even if Mod Menu is not updated to the version of Minecraft you're running).\n\n<details>\n<summary>Java API Documentation</summary>\n\n### Getting Started\nTo use the API, implement the ModMenuApi interface on a class and add that as an entry point of type \"modmenu\" in your `fabric.mod.json` like this:\n\n`fabric.mod.json`\n```json\n\"entrypoints\": {\n  \"modmenu\": [ \"com.example.mod.ExampleModMenuApiImpl\" ]\n}\n```\n\n### Mod Config Screens\nMods can provide a Screen factory to provide a custom config screen to open with the config button. Implement the `getModConfigScreenFactory` method in your API implementation to do this.\n\nThe intended use case for this is for mods to provide their own config screens. The mod id of the config screen is automagically determined by the source mod container that the entrypoint originated from.\n\n### Provided Config Screens\nMods can provide Screen factories to provide a custom config screens to open with the config buttons for other mods as well. Implement the `getProvidedConfigScreenFactories` method in your API implementation for this.\n\nThe intended use case for this is for a mod like Cloth Config to provide config screens for mods that use its API.\n\n### Modpack Badges\nMods can give other mods the `Modpack` badge by implementing the `attachModpackBadges` method, such as through the following:\n\n```java\n@Override\npublic void attachModpackBadges(Consumer<String> consumer) {\n\tconsumer.accept(\"modmenu\"); // Indicates that 'modmenu' is part of the modpack\n}\n```\n\nNote that 'internal' mods such as Minecraft itself and the mod loader cannot be given the modpack badge, as they are not distributed within a typical modpack.\n\n### Static Helper Methods\n`ModMenuApi` also offers a few helper methods for mods that want to work with Mod Menu better, like making their own Mods buttons.\n\n#### Creating a Mods screen instance\nYou can call this method to get an instance of the Mods screen:\n```java\nScreen createModsScreen(Screen previous)\n```\n\n#### Creating a Mods button `Text`\nYou can call this method to get the Text that would be displayed on a Mod Menu Mods button:\n```java\nText createModsButtonText()\n```\n\n</details>", "published": "2020-11-06T05:11:04.725058Z", "updated": "2025-06-20T23:35:23.212679Z", "status": "approved", "license": {"id": "MIT", "name": "MIT License"}, "client_side": "required", "server_side": "unsupported", "categories": ["utility"], "loaders": ["fabric", "quilt"], "icon_url": "https://cdn.modrinth.com/data/mOgUt4GM/1bfe2006b38340e9d064700e41adf84a8abb1bd4_96.webp", "issues_url": "https://github.com/TerraformersMC/ModMenu/issues", "source_url": "https://github.com/TerraformersMC/ModMenu", "wiki_url": "https://github.com/TerraformersMC/ModMenu/wiki", "discord_url": "https://discord.gg/jEGF5fb", "donation_urls": [], "gallery": [{"url": "https://cdn.modrinth.com/data/mOgUt4GM/images/185911191819382e1e4b1f2c54ea8a510a92a064.jpeg", "raw_url": "https://cdn.modrinth.com/data/mOgUt4GM/images/185911191819382e1e4b1f2c54ea8a510a92a064.jpeg", "featured": true, "title": "modmen ubanner", "created": "2025-01-03T19:47:07.316088Z", "ordering": 0.0}, {"url": "https://cdn.modrinth.com/data/mOgUt4GM/images/70fd54b0579f2de3ac45fa9650aca2ec8ff0882b_350.webp", "raw_url": "https://cdn.modrinth.com/data/mOgUt4GM/images/ec97a9f09bc750dd7895414eac14f6252ca47621.png", "featured": false, "title": "<PERSON><PERSON>", "created": "2023-11-03T21:09:55.559800Z", "ordering": 0.0}, {"url": "https://cdn.modrinth.com/data/mOgUt4GM/images/92397b510671ef89974665cfa6309c5da081b8f8_350.webp", "raw_url": "https://cdn.modrinth.com/data/mOgUt4GM/images/155dd2b006883b168b1279ec0ff21e753946518b.png", "featured": false, "title": "Mod Menu v11 Screenshot", "created": "2024-06-17T18:16:51.936311Z", "ordering": 0.0}, {"url": "https://cdn.modrinth.com/data/mOgUt4GM/images/5726bbaf7d0ea53e55b6e666af5d3e1861758334.png", "raw_url": "https://cdn.modrinth.com/data/mOgUt4GM/images/5726bbaf7d0ea53e55b6e666af5d3e1861758334.png", "featured": false, "title": "<PERSON><PERSON>", "created": "2022-02-28T02:33:37.984225Z", "ordering": 1.0}]}, "modrinthVersion": {"id": "YIfqIJ8q", "project_id": "mOgUt4GM", "author_id": "uF4fzWRt", "name": "v11.0.3 for 1.21", "version_number": "11.0.3", "changelog": "- Fix Mods screen being slow to open the first time\n- Config screen errors no longer show until after a user has tried to open them\n- Fix drag-n-drop not supporting Quilt mods\n- Updated Polish translation\n- Fixed issue with drag-n-drop toast having the same line displayed twice\n- Updated Korean translations\n- Fixed Belarusian translation\n- Fixed issues with parent mods", "date_published": "2024-10-14T04:11:09.901609Z", "version_type": "release", "files": [{"hashes": {"sha512": "4c6387a059c7ac9028acc3d78124af02a4495bef2c16783bbffe5bf449067daf2620708fd57f8725e46f0c34d0f571adf60f0869742bfe7f6101ddf13a2a87da", "sha1": "6213120772192040a0aeb5c7cfe330c068605b1f"}, "url": "https://cdn.modrinth.com/data/mOgUt4GM/versions/YIfqIJ8q/modmenu-11.0.3.jar", "filename": "modmenu-11.0.3.jar", "primary": true, "size": 973066}], "dependencies": [{"project_id": "P7dR8mSH", "dependency_type": "required"}, {"project_id": "eXts2L7r", "dependency_type": "required"}], "game_versions": ["1.21", "1.21.1"], "loaders": ["fabric", "quilt"]}}, {"name": "Just Zoom", "version": "[<PERSON><PERSON><PERSON>] v2.1.0 MC 1.21.1", "optional": true, "file": "justzoom_fabric_2.1.0_MC_1.21.1.jar", "type": "mods", "description": "Zoom by pressing a hotkey and adjust the zoom factor with your mouse wheel!", "disabled": false, "userAdded": true, "wasSelected": true, "skipped": false, "curseForgeProjectId": 561885, "curseForgeFileId": 6290225, "curseForgeProject": {"id": 561885, "name": "Just Zoom", "authors": [{"id": 100802238, "name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://www.curseforge.com/members/keksuccino"}], "gameId": 432, "summary": "Zoom by pressing a hotkey and adjust the zoom factor with your mouse wheel!", "categories": [{"name": "Miscellaneous", "slug": "mc-miscellaneous", "url": "https://www.curseforge.com/minecraft/mc-mods/mc-miscellaneous", "dateModified": "2014-06-15T04:27:14.543Z", "gameId": 432, "isClass": false, "id": 425, "iconUrl": "https://media.forgecdn.net/avatars/6/40/635351497693711265.png", "parentCategoryId": 6, "classId": 6}], "status": 4, "primaryCategoryId": 425, "classId": 6, "slug": "just-zoom", "isFeatured": false, "dateModified": "2025-06-21T17:34:53.94Z", "dateCreated": "2021-12-31T01:50:54.497Z", "dateReleased": "2025-06-21T17:30:24.817Z", "links": {"websiteUrl": "https://www.curseforge.com/minecraft/mc-mods/just-zoom", "wikiUrl": "", "issuesUrl": "https://github.com/Keksuccino/JustZoom/issues", "sourceUrl": "https://github.com/Keksuccino/JustZoom"}, "logo": {"id": 1324771, "description": "", "thumbnailUrl": "https://media.forgecdn.net/avatars/thumbnails/1324/771/256/256/638861240221497743.png", "title": "638861240221497743.png", "url": "https://media.forgecdn.net/avatars/1324/771/638861240221497743.png", "modId": 561885}, "allowModDistribution": true, "screenshots": [], "mainFileId": 6679684}, "curseForgeFile": {"id": 6290225, "gameId": 432, "isAvailable": true, "displayName": "[<PERSON><PERSON><PERSON>] v2.1.0 MC 1.21.1", "fileName": "justzoom_fabric_2.1.0_MC_1.21.1.jar", "releaseType": 1, "fileStatus": 4, "fileDate": "2025-03-10T22:38:08.227Z", "fileLength": 196476, "dependencies": [{"fileId": 0, "modId": 416797, "relationType": 3}, {"fileId": 0, "modId": 306612, "relationType": 3}], "alternateFileId": 6290226, "modules": [{"fingerprint": 2832247782, "name": "META-INF"}, {"fingerprint": 2103086544, "name": "assets"}, {"fingerprint": 4294816009, "name": "de"}, {"fingerprint": 1896675393, "name": "fabric.mod.json"}, {"fingerprint": 1673185071, "name": "justzoom.accesswidener"}, {"fingerprint": 3341926228, "name": "justzoom.fabric.mixins.json"}, {"fingerprint": 3814367890, "name": "justzoom.mixins.json"}, {"fingerprint": 4109054582, "name": "justzoom.refmap.json"}, {"fingerprint": 59460657, "name": "justzoom_logo_128x128.png"}, {"fingerprint": 2506564194, "name": "pack.mcmeta"}], "isServerPack": false, "hashes": [{"value": "5ee4e825eb9fb2b7795ade1a99b5f0626a5a3d9a", "algo": 1}, {"value": "ef6a7cf9d987e0a5adecc749bdea76a3", "algo": 2}], "sortableGameVersions": [{"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-09-01T00:00:00Z", "gameVersionName": "<PERSON><PERSON><PERSON>"}, {"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.1", "gameVersionReleaseDate": "2024-08-08T15:17:53.787Z", "gameVersionName": "1.21.1"}], "gameVersions": ["<PERSON><PERSON><PERSON>", "1.21.1"], "fileFingerprint": 2157538043, "modId": 561885}, "modrinthProject": {"id": "iAiqcykM", "slug": "just-zoom", "project_type": "mod", "team": "brmoyUbE", "title": "Just Zoom", "description": "Zoom by pressing a hotkey and adjust the zoom factor with your mouse wheel!", "body": "<a href=\"https://discord.gg/rhayah27GC\"><img src=\"https://img.shields.io/discord/704163135787106365?style=flat&label=Discord&labelColor=%234260f5&color=%2382aeff\" /></a> <a href=\"https://paypal.me/TimSchroeter\"><img src=\"https://img.shields.io/badge/Donate%20via%20PayPal-%233d91ff?style=flat\" /></a> <a href=\"https://www.patreon.com/keksuccino\"><img src=\"https://img.shields.io/badge/Support%20me%20on%20Patreon-%23ff9b3d?style=flat\" /></a>\n\n<br>\n<img width=\"600\" src=\"https://github.com/Keksuccino/JustZoom/blob/main/justzoom_logo_shadow.png?raw=true\" /><br><br><br>\n\nJust Zoom allows you to zoom by pressing a hotkey (**Z or Y by default, depending on your keyboard**) and adjust the zoom with your mouse wheel!\nIt feels similar to zooming with the spyglass, but you can zoom in further and don't have the black bars and spyglass overlay blocking your view!\n\nFor support, tips, and to connect with other users, join the official [Discord server](https://discord.gg/rhayah27GC).\n\nIf you experience crashes or other issues, please write a bug report on [GitHub](https://github.com/Keksuccino/JustZoom/issues)!\n\n# Features\n\nJust Zoom has some great features to improve the zoom experience!\n\n## Zoom\n\nJust Zoom lets you zoom by pressing a hotkey and adjust the zoom factor with your **mouse wheel**.<br>\nBy default, Just Zoom will also zoom in/out smootly, just like the spyglass, but you can disable that in its options.\n\n<img width=\"500\" src=\"https://i.postimg.cc/15JfmnYW/mouse-wheel-small.gif\" /><br>\n\n## Normalized Mouse Sensitivity\n\nJust Zoom normalizes your mouse sensitivity when zooming, so it doesn't feel like the mouse is moving too fast.\n\n<img width=\"500\" src=\"https://i.postimg.cc/bw5rbXPk/sensitivity-small-short.gif\" /><br>\n\n## _Maximum_ Zoom\n\nJust Zoom can zoom in further than the spyglass.\n\n<img width=\"500\" src=\"https://i.postimg.cc/8zcsjp2T/spyglass-small.gif\" /><br>\n\n# Configuration\n\nJust Zoom has in-game options, so you can easily configure it by pressing the little **spyglass button** in the **bottom-left** side of the **Pause Screen**!\n\n<img width=\"400\" alt=\"spyglass_button\" src=\"https://gist.github.com/assets/35544624/ed3cb1a3-d34a-4fe8-a591-d49260ebded7\"><br>\n\n<img width=\"500\" alt=\"just_zoom_options\" src=\"https://gist.github.com/assets/35544624/48b2951c-b4cf-4c36-9542-119bcfbfce33\"><br>\n\n# Copyright\n\nJust Zoom Copyright © 2021-2024 Keksuccino.<br>\nJust Zoom is licensed under DSMSLv2 (DON'T SNATCH MA STUFF LICENSE V2.0).<br>\nSee [LICENSE.md](https://github.com/Keksuccino/JustZoom/blob/main/LICENSE.md) for more information about the license.\n\n# Server Needed?\n\nLooking to play Minecraft with friends but setting up a server is just too time-consuming?<br>\nNo worries! Simply rent a pre-configured server and start playing _in a snap_.\n\nJust click the image below and use code **keksuccino** to enjoy a **25% discount** on your first month!\n\n<br>\n\n<a href=\"https://tinyurl.com/bisectkeks\"><img src=\"https://www.bisecthosting.com/partners/custom-banners/f5dd9194-01d8-4ce3-9b6a-a85327d975b1.png\" /></a>", "published": "2023-06-09T23:36:39.409185Z", "updated": "2025-06-21T17:30:27.433965Z", "status": "approved", "license": {"id": "LicenseRef-DSMSLv2", "name": "", "url": "https://github.com/Keksuccino/JustZoom/blob/main/LICENSE.md"}, "client_side": "required", "server_side": "unsupported", "categories": ["utility"], "loaders": ["fabric", "forge", "neoforge"], "icon_url": "https://cdn.modrinth.com/data/iAiqcykM/0425611318ddc95184845eaa3d78899ddba0620e_96.webp", "issues_url": "https://github.com/Keksuccino/JustZoom/issues", "source_url": "https://github.com/Keksuccino/JustZoom", "discord_url": "https://discord.gg/UzmeWkD", "donation_urls": [{"id": "patreon", "platform": "Patreon", "url": "https://www.patreon.com/keksuccino"}, {"id": "paypal", "platform": "<PERSON><PERSON>", "url": "https://www.paypal.com/paypalme/TimSchroeter"}], "gallery": []}, "modrinthVersion": {"id": "jCSPF9Ft", "project_id": "iAiqcykM", "author_id": "k0LcYHWE", "name": "[<PERSON><PERSON><PERSON>] v2.1.0 MC 1.21.1", "version_number": "2.1.0-1.21.1-fabric", "changelog": "Some changelog text.<br>Supports multiline input.", "date_published": "2025-03-10T22:38:12.307253Z", "version_type": "release", "files": [{"hashes": {"sha1": "5ee4e825eb9fb2b7795ade1a99b5f0626a5a3d9a", "sha512": "f470188df43d2e7d750bb9dc0d037d6e2637e29b85c9bf409c3bb997330a4464010c3c3bb8ebf0b8e36a8ad3022caad8526e33aece92c3ae9daa5c407a3c3df5"}, "url": "https://cdn.modrinth.com/data/iAiqcykM/versions/jCSPF9Ft/justzoom_fabric_2.1.0_MC_1.21.1.jar", "filename": "justzoom_fabric_2.1.0_MC_1.21.1.jar", "primary": true, "size": 196476}, {"hashes": {"sha1": "142a35f22211b116109694ab3b26cae2fbf488ca", "sha512": "64ebb240624eef6dbab9ac03e448e9dba55054a6fe227de775e939ab2ebc7fbeb691784c3e6e6f45bf6b23f1592d0c1f26bd140d601f112eed4cbea879c4f986"}, "url": "https://cdn.modrinth.com/data/iAiqcykM/versions/jCSPF9Ft/sources_justzoom_fabric_2.1.0_MC_1.21.1.jar", "filename": "sources_justzoom_fabric_2.1.0_MC_1.21.1.jar", "primary": false, "size": 32121}], "dependencies": [{"project_id": "P7dR8mSH", "dependency_type": "required"}, {"project_id": "J81TRJWm", "dependency_type": "required"}], "game_versions": ["1.21.1"], "loaders": ["fabric"]}}, {"name": "Lithium", "version": "Lithium mc1.21.1-0.15.0 for <PERSON><PERSON><PERSON>", "optional": true, "file": "lithium-fabric-0.15.0+mc1.21.1.jar", "type": "mods", "description": "No-compromises game logic optimization mod. Well suited for clients and servers of all kinds. Now available for Fabric and NeoForge!", "disabled": false, "userAdded": true, "wasSelected": true, "skipped": false, "curseForgeProjectId": 360438, "curseForgeFileId": 6290412, "curseForgeProject": {"id": 360438, "name": "Lithium (Fabric/NeoForge)", "authors": [{"id": 28746583, "name": "JellySquid", "url": "https://www.curseforge.com/members/jellysquid"}], "gameId": 432, "summary": "An optimization mod for Minecraft which improves server performance significantly.", "categories": [{"name": "Performance", "slug": "performance", "url": "https://www.curseforge.com/minecraft/mc-mods/performance", "dateModified": "2024-01-16T06:56:01.057Z", "gameId": 432, "isClass": false, "id": 6814, "iconUrl": "https://media.forgecdn.net/avatars/933/987/638409849610531091.png", "parentCategoryId": 6, "classId": 6}], "status": 4, "primaryCategoryId": 6814, "classId": 6, "slug": "lithium", "isFeatured": false, "dateModified": "2025-06-17T21:16:06.373Z", "dateCreated": "2020-01-28T21:36:14.247Z", "dateReleased": "2025-06-17T21:08:13.9Z", "links": {"websiteUrl": "https://www.curseforge.com/minecraft/mc-mods/lithium", "wikiUrl": "", "issuesUrl": "https://github.com/jellysquid3/lithium-fabric/issues", "sourceUrl": "https://github.com/jellysquid3/lithium"}, "logo": {"id": 246646, "description": "", "thumbnailUrl": "https://media.forgecdn.net/avatars/thumbnails/246/646/256/256/637158441743060329.png", "title": "637158441743060329.png", "url": "https://media.forgecdn.net/avatars/246/646/637158441743060329.png", "modId": 360438}, "allowModDistribution": true, "screenshots": [], "mainFileId": 6663179}, "curseForgeFile": {"id": 6290412, "gameId": 432, "isAvailable": true, "displayName": "Lithium mc1.21.1-0.15.0 for <PERSON><PERSON><PERSON>", "fileName": "lithium-fabric-0.15.0+mc1.21.1.jar", "releaseType": 1, "fileStatus": 4, "fileDate": "2025-03-10T23:26:11.287Z", "fileLength": 771768, "dependencies": [], "alternateFileId": 0, "modules": [{"fingerprint": 888919809, "name": "META-INF"}, {"fingerprint": **********, "name": "LICENSE.md"}, {"fingerprint": **********, "name": "assets"}, {"fingerprint": **********, "name": "fabric.mod.json"}, {"fingerprint": 685323655, "name": "lithium-fabric-mixin-config.md"}, {"fingerprint": **********, "name": "lithium-fabric.mixins.json"}, {"fingerprint": **********, "name": "lithium.accesswidener"}, {"fingerprint": **********, "name": "lithium.mixins.json"}, {"fingerprint": 96547872, "name": "net"}], "isServerPack": false, "hashes": [{"value": "b7d77602807f2c7cd7132741244bf8c0b8dec6c1", "algo": 1}, {"value": "319d83e396c1a1227bcb4451d79e3336", "algo": 2}], "sortableGameVersions": [{"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-09-01T00:00:00Z", "gameVersionName": "<PERSON><PERSON><PERSON>"}, {"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.1", "gameVersionReleaseDate": "2024-08-08T15:17:53.787Z", "gameVersionName": "1.21.1"}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-05-26T00:00:00Z", "gameVersionName": "Quilt"}], "gameVersions": ["<PERSON><PERSON><PERSON>", "1.21.1", "Quilt"], "fileFingerprint": 2430859929, "modId": 360438}, "modrinthProject": {"id": "gvQqBUqZ", "slug": "lithium", "project_type": "mod", "team": "peSx5UYg", "title": "Lithium", "description": "No-compromises game logic optimization mod. Well suited for clients and servers of all kinds. Now available for Fabric and NeoForge!", "body": "Lithium is a modern, general-purpose optimization mod for Minecraft which works to improve a number of systems (game physics, mob AI, block ticking, etc) with the goal of **not changing any vanilla mechanics**. \n\n<br>\n\nThe mod works **on both the client and server**, and can be installed on servers **without requiring clients to also have the mod** (and vice versa.)\n\n<br>\n\nFor multiplayer servers, administrators can expect a sizeable improvement to tick times, allowing their hardware to support more loaded entities, chunks, and players. Even in single-player, <PERSON>thium helps to improve performance by optimizing the internal game server, which is used for \"ticking\" the world. This can free up your computer's processor to focus on other tasks, resulting in **improved frame rates and increased responsiveness**. \n\n\n<br>\n\n\nThe strict goal of maintaining exact behavior with vanilla makes Lithium suitable for almost any Minecraft server running Fabric, even for highly complex gameplay scenarios and creations. In fact, our continued insistence towards not changing vanilla gameplay has even led to the mod being officially allowed in [Minecraft speedrunning](https://www.speedrun.com/mc).\n\n<br>\n\n## Installation\n\nMake sure you have the latest version of Fabric Loader present and then simply drop the mod into your mods folder. No other mods or additional setup (not even Fabric API!) is required. You do not need to create new worlds in order to take advantage of the mod.\n\n<br>\n\n\nWhile you're at it, you may want to check out [Sodium](https://modrinth.com/mod/sodium), which improves rendering performance.\n\n<br>\n\n## Configuration\n\nLithium makes use of an semi-unusual configuration system which allows you to enable or completely disable patches the mod applies. This system allows fine-grained control over what code in Minecraft is modified by Lithium, and as such, can be used to completely eliminate bugs or mod incompatibilities introduced by the mod.\n\n<br>\n\nFor more information, please read [the wiki entry](https://github.com/CaffeineMC/lithium-fabric/wiki/Configuration-File) on modifying your configuration file.\n\n<br>\n\nPlease note: An empty configuration file is *perfectly normal* and just means you want to use the default options, which are already set up out of the box for the best performance the mod can offer.\n\n<br>\n\n## Reporting Issues\n\nPlease use the issue tracker linked at the top of the page to report bugs, crashes, and other issues.\n\n", "published": "2021-01-03T00:56:52.292581Z", "updated": "2025-06-17T17:01:15.490551Z", "status": "approved", "license": {"id": "LGPL-3.0-only", "name": "GNU Lesser General Public License v3.0 only"}, "client_side": "optional", "server_side": "optional", "categories": ["optimization"], "loaders": ["fabric", "neoforge", "quilt"], "icon_url": "https://cdn.modrinth.com/data/gvQqBUqZ/bcc8686c13af0143adf4285d741256af824f70b7_96.webp", "issues_url": "https://github.com/caffeinemc/lithium-fabric/issues", "source_url": "https://github.com/caffeinemc/lithium-fabric", "discord_url": "https://caffeinemc.net/discord", "donation_urls": [{"id": "patreon", "platform": "Patreon", "url": "https://www.patreon.com/2No2Name"}], "gallery": []}, "modrinthVersion": {"id": "MM5BBBOK", "project_id": "gvQqBUqZ", "author_id": "uhPSqlnd", "name": "Lithium 0.15.0 for <PERSON><PERSON><PERSON>", "version_number": "mc1.21.1-0.15.0-fabric", "changelog": "Lithium 0.15.0 for Minecraft 1.21.1 includes new optimizations and several bug fixes, including better compatibility with neoforge inventories.\n\nMake sure to take a backup of your world before using the mod and please report any bugs and mod compatibility issues at the [issue tracker](https://github.com/CaffeineMC/lithium-fabric/issues). You can check the [description of each optimization](https://github.com/CaffeineMC/lithium/blob/mc1.21.1-0.15.0/lithium-mixin-config.md) and how to disable it when encountering a problem.\n\n## Additions\n- explosion entity raycast optimizations (Thanks to RacoonD<PERSON>)\n- Sleeping crafter block entity\n\n## Fixes\n- Fix crash with C<PERSON> Pondering when entities are pushed by fluids\n- Fix crash when placing a block at build limit in the end and attempting to spawn a dragon\n- Fix hoppers sleeping even though interaction with neoforge API block inventories is possible\n- Fix sleeping hoppers woken up by moving item entities even if blocked with a full block\n- Fix broken state of ChunkAwareBlockCollisionSweeper in entity nether portal positioning optimization and with TIS-Carpet", "date_published": "2025-03-10T23:26:12.088687Z", "version_type": "release", "files": [{"hashes": {"sha512": "fd3215501ebe8f6590cdf1ccc58182873cad8d74ebc4b0b3a2f5724748b9cb04c2b967503c072311dfbca9ba856195dc4662f3395a071b294fa0acfdd2a86bf6", "sha1": "b7d77602807f2c7cd7132741244bf8c0b8dec6c1"}, "url": "https://cdn.modrinth.com/data/gvQqBUqZ/versions/MM5BBBOK/lithium-fabric-0.15.0%2Bmc1.21.1.jar", "filename": "lithium-fabric-0.15.0+mc1.21.1.jar", "primary": true, "size": 771768}], "dependencies": [], "game_versions": ["1.21.1"], "loaders": ["fabric", "quilt"]}}, {"name": "Shulker Box Tooltip [Fabric/Forge/NeoForge]", "version": "[<PERSON><PERSON><PERSON> 1.21.1] v5.1.6+1.21.1", "optional": true, "file": "shulkerboxtooltip-fabric-5.1.6+1.21.1.jar", "type": "mods", "description": "What's in my shulker box?", "disabled": false, "userAdded": true, "wasSelected": true, "skipped": false, "curseForgeProjectId": 315811, "curseForgeFileId": 6530831, "curseForgeProject": {"id": 315811, "name": "Shulker Box Tooltip [Fabric/Forge/NeoForge]", "authors": [{"id": 13923283, "name": "Mister<PERSON>e<PERSON>od<PERSON>", "url": "https://www.curseforge.com/members/misterpemodder"}], "gameId": 432, "summary": "What's in my shulker box?", "categories": [{"name": "Cosmetic", "slug": "cosmetic", "url": "https://www.curseforge.com/minecraft/mc-mods/cosmetic", "dateModified": "2014-05-08T17:42:35.597Z", "gameId": 432, "isClass": false, "id": 424, "iconUrl": "https://media.forgecdn.net/avatars/6/39/635351497555976928.png", "parentCategoryId": 6, "classId": 6}, {"name": "Map and Information", "slug": "map-information", "url": "https://www.curseforge.com/minecraft/mc-mods/map-information", "dateModified": "2014-05-08T17:42:23.74Z", "gameId": 432, "isClass": false, "id": 423, "iconUrl": "https://media.forgecdn.net/avatars/6/38/635351497437388438.png", "parentCategoryId": 6, "classId": 6}], "status": 4, "primaryCategoryId": 424, "classId": 6, "slug": "shulkerboxtooltip", "isFeatured": false, "dateModified": "2025-06-21T11:21:41.607Z", "dateCreated": "2019-02-25T03:10:08.423Z", "dateReleased": "2025-06-21T11:18:55.867Z", "links": {"websiteUrl": "https://www.curseforge.com/minecraft/mc-mods/shulkerboxtooltip", "wikiUrl": "https://github.com/MisterPeModder/ShulkerBoxTooltip/wiki", "issuesUrl": "https://github.com/MisterPeModder/ShulkerBoxTooltip/issues", "sourceUrl": "https://github.com/MisterPeModder/ShulkerBoxTooltip"}, "logo": {"id": 850113, "description": "", "thumbnailUrl": "https://media.forgecdn.net/avatars/thumbnails/850/113/256/256/638251427406536090.png", "title": "638251427406536090.png", "url": "https://media.forgecdn.net/avatars/850/113/638251427406536090.png", "modId": 315811}, "allowModDistribution": true, "screenshots": [{"id": 900095, "description": "Configuration Screen", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/900/95/310/172/config-mk2.png", "title": "config-mk2.png", "url": "https://media.forgecdn.net/attachments/900/95/config-mk2.png", "modId": 315811}, {"id": 695948, "description": "", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/695/948/310/172/sbt_banner.png", "title": "Logo", "url": "https://media.forgecdn.net/attachments/695/948/sbt_banner.png", "modId": 315811}, {"id": 285173, "description": "", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/285/173/310/172/config_screen.png", "title": "Config Screen", "url": "https://media.forgecdn.net/attachments/285/173/config_screen.png", "modId": 315811}, {"id": 285169, "description": "", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/285/169/310/172/loot_table_tooltip_info.png", "title": "Loot Table Tooltip info", "url": "https://media.forgecdn.net/attachments/285/169/loot_table_tooltip_info.png", "modId": 315811}, {"id": 285163, "description": "", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/285/163/310/172/full_preview_1.png", "title": "Full Preview (Minecraft 1.16)", "url": "https://media.forgecdn.net/attachments/285/163/full_preview_1.png", "modId": 315811}, {"id": 285160, "description": "", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/285/160/310/172/other_containers_preview.png", "title": "Other Containers Preview", "url": "https://media.forgecdn.net/attachments/285/160/other_containers_preview.png", "modId": 315811}, {"id": 285159, "description": "", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/285/159/310/172/compact_preview_2.png", "title": "Compact Preview", "url": "https://media.forgecdn.net/attachments/285/159/compact_preview_2.png", "modId": 315811}, {"id": 269447, "description": "", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/269/447/310/172/2019-11-19_10.png", "title": "Full Preview", "url": "https://media.forgecdn.net/attachments/269/447/2019-11-19_10.png", "modId": 315811}], "mainFileId": 6678392}, "curseForgeFile": {"id": 6530831, "gameId": 432, "isAvailable": true, "displayName": "[<PERSON><PERSON><PERSON> 1.21.1] v5.1.6+1.21.1", "fileName": "shulkerboxtooltip-fabric-5.1.6+1.21.1.jar", "releaseType": 1, "fileStatus": 4, "fileDate": "2025-05-14T23:39:54.097Z", "fileLength": 377858, "dependencies": [{"fileId": 0, "modId": 308702, "relationType": 2}, {"fileId": 0, "modId": 306612, "relationType": 3}], "alternateFileId": 0, "modules": [{"fingerprint": 656626058, "name": "META-INF"}, {"fingerprint": 3687740183, "name": "architectury_inject_ShulkerBoxTooltip_common_f14b8f40fb2c448a87e6e6ab704f1070_0b433980fabcff6a5532bcb20aac21303ee81653648207a45abf617535f2d9efshulkerboxtooltipcommon5161211devjar"}, {"fingerprint": 2694178073, "name": "assets"}, {"fingerprint": 1949628524, "name": "com"}, {"fingerprint": 927526079, "name": "fabric.mod.json"}, {"fingerprint": 31061239, "name": "shulkerboxtooltip-common-common-refmap.json"}, {"fingerprint": 3862657908, "name": "shulkerboxtooltip-common.mixins.json"}, {"fingerprint": 68961871, "name": "shulkerboxtooltip-fabric-fabric-refmap.json"}, {"fingerprint": 2394869243, "name": "shulkerboxtooltip-fabric.mixins.json"}, {"fingerprint": 2410858661, "name": "shulkerboxtooltip.accesswidener"}], "isServerPack": false, "hashes": [{"value": "e6bf91c784e773494f43d53e46bb09011cc22435", "algo": 1}, {"value": "e3c137339aa96c4ff554102a4e6c6c28", "algo": 2}], "sortableGameVersions": [{"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-09-01T00:00:00Z", "gameVersionName": "<PERSON><PERSON><PERSON>"}, {"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.1", "gameVersionReleaseDate": "2024-08-08T15:17:53.787Z", "gameVersionName": "1.21.1"}], "gameVersions": ["<PERSON><PERSON><PERSON>", "1.21.1"], "fileFingerprint": 3210419200, "modId": 315811}}, {"name": "Xaero's World Map", "version": "1.39.9 for Fabric 1.21.1", "optional": true, "file": "XaerosWorldMap_1.39.9_Fabric_1.21.jar", "type": "mods", "description": "Adds a full screen world map which shows you what you have explored in the world. Works great together with Xaero's Minimap.", "disabled": false, "userAdded": true, "wasSelected": true, "skipped": false, "curseForgeProjectId": 317780, "curseForgeFileId": 6538360, "curseForgeProject": {"id": 317780, "name": "Xaero's World Map", "authors": [{"id": 12362980, "name": "xaero96", "url": "https://www.curseforge.com/members/xaero96"}], "gameId": 432, "summary": "Adds a fullscreen worldmap which shows you what you have explored in the world. Can work together with <PERSON><PERSON><PERSON>'s Minimap.", "categories": [{"name": "Map and Information", "slug": "map-information", "url": "https://www.curseforge.com/minecraft/mc-mods/map-information", "dateModified": "2014-05-08T17:42:23.74Z", "gameId": 432, "isClass": false, "id": 423, "iconUrl": "https://media.forgecdn.net/avatars/6/38/635351497437388438.png", "parentCategoryId": 6, "classId": 6}, {"name": "Player Transport", "slug": "technology-player-transport", "url": "https://www.curseforge.com/minecraft/mc-mods/technology/technology-player-transport", "dateModified": "2014-05-08T17:38:58.357Z", "gameId": 432, "isClass": false, "id": 414, "iconUrl": "https://media.forgecdn.net/avatars/6/29/635351495383551178.png", "parentCategoryId": 412, "classId": 6}], "status": 4, "primaryCategoryId": 423, "classId": 6, "slug": "xaeros-world-map", "isFeatured": false, "dateModified": "2025-06-18T10:42:30.39Z", "dateCreated": "2019-03-20T13:35:11.837Z", "dateReleased": "2025-06-18T10:12:11.387Z", "links": {"websiteUrl": "https://www.curseforge.com/minecraft/mc-mods/xaeros-world-map", "wikiUrl": "", "issuesUrl": "https://legacy.curseforge.com/minecraft/mc-mods/xaeros-world-map/issues"}, "logo": {"id": 196654, "description": "", "thumbnailUrl": "https://media.forgecdn.net/avatars/thumbnails/196/654/256/256/636886857118683130.png", "title": "636886857118683130.png", "url": "https://media.forgecdn.net/avatars/196/654/636886857118683130.png", "modId": 317780}, "allowModDistribution": true, "screenshots": [{"id": 248987, "description": "", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/248/987/310/172/10xwx6t.png", "title": "Night", "url": "https://media.forgecdn.net/attachments/248/987/10xwx6t.png", "modId": 317780}, {"id": 248986, "description": "", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/248/986/310/172/357h91j.png", "title": "Nether", "url": "https://media.forgecdn.net/attachments/248/986/357h91j.png", "modId": 317780}, {"id": 248985, "description": "", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/248/985/310/172/2czdn3k.jpg", "title": "Overworld", "url": "https://media.forgecdn.net/attachments/248/985/2czdn3k.jpg", "modId": 317780}], "mainFileId": 6665448}, "curseForgeFile": {"id": 6538360, "gameId": 432, "isAvailable": true, "displayName": "1.39.9 for Fabric 1.21.1", "fileName": "XaerosWorldMap_1.39.9_Fabric_1.21.jar", "releaseType": 2, "fileStatus": 4, "fileDate": "2025-05-17T08:27:54.217Z", "fileLength": 969206, "dependencies": [{"fileId": 0, "modId": 263420, "relationType": 2}, {"fileId": 0, "modId": 636608, "relationType": 2}, {"fileId": 0, "modId": 306612, "relationType": 3}], "alternateFileId": 0, "modules": [{"fingerprint": 3084379320, "name": "META-INF"}, {"fingerprint": 1430489591, "name": "assets"}, {"fingerprint": 894010528, "name": "fabric.mod.json"}, {"fingerprint": 1565779974, "name": "pack.mcmeta"}, {"fingerprint": 1323866653, "name": "xae<PERSON>"}, {"fingerprint": 946837686, "name": "xaeroworldmap.mixins.json"}, {"fingerprint": 3926920045, "name": "xaeroworldmap.refmap.json"}], "isServerPack": false, "hashes": [{"value": "2d23d1604f7e03dd82a140cc8ee2b6881ab48b48", "algo": 1}, {"value": "78bb49742396fe9839df3c44de97b087", "algo": 2}], "sortableGameVersions": [{"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-09-01T00:00:00Z", "gameVersionName": "<PERSON><PERSON><PERSON>"}, {"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.1", "gameVersionReleaseDate": "2024-08-08T15:17:53.787Z", "gameVersionName": "1.21.1"}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-05-26T00:00:00Z", "gameVersionName": "Quilt"}], "gameVersions": ["<PERSON><PERSON><PERSON>", "1.21.1", "Quilt"], "fileFingerprint": 1831762431, "modId": 317780}, "modrinthProject": {"id": "NcUtCpym", "slug": "xaeros-world-map", "project_type": "mod", "team": "QWyiT27S", "title": "Xaero's World Map", "description": "Adds a full screen world map which shows you what you have explored in the world. Works great together with Xaero's Minimap.", "body": "<p><img src=\"https://chocolateminecraft.com/images/worldmapbanner.png\" width=\"965\" height=\"643\" /></p>\n\n### Do not use XaeroPlus with this mod! It is not affiliated with me and causes critical untrackable bugs/crashes. It is also allegedly affiliated with a server griefing group, which can put your server builds at risk.\n\n<p>Xaero's World Map mod adds a self-writing fullscreen map to your Minecraft client. Works as a separate mod but is a lot better with Xaero's Minimap. The reason why it's available separately is to keep Xaero's Minimap as light-weight as possible. <strong>This mod is mostly client-sided. Installing it on the server side is optional for some additional features like world identification.</strong></p>\n<p>The mod is still in development! Please report any bugs that you encounter or otherwise they probably won't get fixed.&nbsp;</p>\n<p><strong>Make sure your world is optimized (<em>Singleplayer - Edit - Optimize World</em>) for the Minecraft version you're playing (repeat after every Minecraft update). It takes A LOT longer to generate singleplayer maps from unoptimized world saves.</strong></p>\n\n### Follow me on Mastodon (Twitter alternative)?&nbsp;<a href=\"https://mas.to/@xaero96\" target=\"_blank\" rel=\"nofollow noopener noreferrer\">mas.to/@xaero96</a>\n\n### Support me on Patreon?&nbsp;<a href=\"https://bit.ly/PatreonXaero96\" target=\"_blank\" rel=\"nofollow noopener noreferrer\"><span style=\"text-decoration: underline;\">patreon.com/xaero96</span></a>\n\n<p>Thank you!</p>\n\n### Features\n\n<ul>\n<li>A self-writing world map (as opposed to manual paper maps). You can create multiple maps on the same server address and switch between them either manually or automatically.</li>\n<li>Works in Singleplayer, Multiplayer and on Realms.</li>\n<li>Intuitive interface. Open the map screen by&nbsp;<strong class=\"bbc\">pressing M</strong>. Move the map around with your mouse and zoom in/out using the&nbsp;<strong class=\"bbc\">mouse wheel</strong>, similar to Google Maps. All relevant controls are listed in a tooltip on the map screen and you can customize a lot of them.</li>\n<li>Optional automatic multiple world detection on servers.</li>\n<li>Displays chunk claims and ally players from the <a href=\"https://modrinth.com/mod/open-parties-and-claims\" target=\"_blank\" rel=\"noopener noreferrer\">Open Parties and Claims</a> mod. Lets you claim or forceload chunks directly from the map.</li>\n<li>Resource pack support. Changing the resource packs causes the whole world map to regenerate.</li>\n<li>Cave dimensions support (for example the Nether).</li>\n<li>Optional vanilla maps mode, which makes the world map look like the vanilla paper maps.</li>\n<li>Terrain shading customization.</li>\n<li>\"Footsteps\" that show you your most recent path taken.</li>\n<li>Settings that can help improve the performance of the mod when necessary. Although in most cases the mod should perform great by default.</li>\n<li><a href=\"https://modrinth.com/mod/xaeros-minimap\" target=\"_blank\" rel=\"noopener noreferrer\">Xaero's Minimap</a>&nbsp;integration. The minimap mod will display chunks provided by the world map. The world map mod will display waypoints from the Xaero's Minimap mod. Also works with the&nbsp;<a href=\"https://www.planetminecraft.com/mod/164-better-pvp-v10/\" target=\"_blank\" rel=\"noopener noreferrer\">Better PVP mod</a>. You can create, edit and&nbsp;<strong class=\"bbc\">teleport to the waypoints</strong>&nbsp;without leaving the map interface.</li>\n<li>Potion effects to control the usage of the world map for the players on your server/map. The following potion effects exist as of writing this:&nbsp;<em class=\"bbc\">xaeroworldmap:no_world_map.&nbsp;</em>The effects are of the neutral type by default, but you can also specify a harmful type by appending&nbsp;<em class=\"bbc\">_harmful</em>&nbsp;to the end of the effect ID, for example&nbsp;<em class=\"bbc\">xaeroworldmap:no_world_map_harmful</em>.</li>\n<li><strong class=\"bbc\">Since version 1.14.0</strong>&nbsp;of the mod, you can open an on-map waypoint menu by clicking the icon in the bottom right corner of the map screen. In the waypoint menu, you can see a list of all your map waypoints from the rendered sets, interact with them with the mouse cursor, filter them by name in a text field. The menu also lets you toggle the visibility of disabled waypoints, toggle rendering all sets and change the current waypoint set. Hovering over the waypoints in the menu also acts as hovering over them on the map: they are highlighted on the map and you can use the right-click or hover controls to interact with them.</li>\n<li>An option to export a map as a PNG image file. The mod tries to export the whole map, so it might not work, if the map is millions of blocks in length. I'm planning to give more options for exporting the map in the future!</li>\n<li>Option to limit your map access by binding it to an item required in your hotbar or to be equipped. For example, add&nbsp;<em>mapItemId:minecraft:compass&nbsp;</em>anywhere in the config file to bind it to the compass item. This way the map will not work unless you have the compass in your hotbar or equipped.&nbsp;</li>\n</ul>\n<p><a href=\"https://modrinth.com/mod/xaeros-minimap\" target=\"_blank\" rel=\"noopener noreferrer\"><span style=\"font-size: 24px; text-decoration: underline;\">Get Xaero's Minimap</span></a></p>\n\n## FAQ\n\n### How do I restore pre 1.30 update Nether maps in multiplayer to be displayed in the \"full\" cave mode type?\n\n<p>First, exit the server and locate the Minecraft game directory that you're using to play on the server. In the game directory, go into&nbsp;<em>xaero/world-map/Multiplayer_[server_address]/DIM-1/[map_folder]</em>. In older versions, <em>xaero/world-map</em> is simply <em>XaeroWorldMap</em>. If such doesn't exist already, create a folder called \"<strong>caves</strong>\", without the quotation marks, and go into it. Inside the caves folder, create a folder called \"<strong>-2147483648</strong>\", without the quotation marks, if such doesn't exist already. Go back into the folder that contains the \"<strong>caves</strong>\" folder and move all \"<strong>.zip</strong>\" files to the&nbsp;<strong><em>caves/-2147483648</em></strong>&nbsp;folder. Feel free to delete the cache folders or also move them with the \"<strong>.zip</strong>\" files.</p>\n\n### How do I set up cave mode to behave like it used to pre 1.30 before cave mode was introduced?\n\n<p>You can fully achieve this only when using the normal version of the minimap or no minimap at all. The fair-play version of the minimap now disables world map cave mode in the Nether and you can't toggle that, so it won't be exactly like it used to be.</p>\n<p>If you have Nether map data from before 1.30, please start by moving it in the correct folder as explained under \"<em>How do I restore pre 1.30 update Nether maps in multiplayer to be displayed in the \"full\" cave mode type?</em>\" on this page.<br />After you're done with that, join a world/server and open the world map settings. Locate \"Default Cave Mode Type\" and set it to OFF and then set \"Legible Cave Maps\" to ON. Next, open the world map screen and click the cave icon in the bottom left of the screen. In the menu that opens, set the \"Cave Mode Top Y\" to absolutely any number. If you are in the overworld, set the \"Cave Mode Type\" to OFF. When you are in the Nether later, set&nbsp;\"Cave Mode Type\" to Full. The cave mode type is stored per dimension, so you can set it to Full for any nether-like dimension and keep it on OFF for anything else. You can also switch between displaying below and above bedrock roof by toggling it.</p>\n\n### Why does right-click teleportation not work for me?\n\nThe mod sends a chat command to teleport you to specific coordinates, which is _/tp @s x y z_ by default, so appropriate permission is necessary. The vanilla /tp command requires OP status (cheats enabled) but it's possible to have separate permissions for commands on a non-vanilla server with certain plugins installed. The used command can be changed in the world map settings with \"Map Teleport Command\". Non-vanilla teleportation commands often don't support the selectors such as @s, so you might have to remove it from the configured command. There is also an option called \"Player Teleport Command\" which is used when you right click tracked players (e.g. your party from Open Parties and Claims). Teleportation to waypoints uses its own setting in the minimap mod. You can read about it on the minimap description page under FAQ.\n\n### How do I discover the map without manually walking?\n\n<p>In singleplayer, the map is loaded directly from the world save. This means that you can pregenerate the world using whatever tools you prefer, relog and use the \"Reload Regions\" option in the world map settings to fix any conflicts in the map cache. In multiplayer, it isn't as simple. However, if you can download the server world save and enter the world in singleplayer, then you can pregenerate the world using a method of your choice, relog and then use the \"Convert All Regions\" in the world map settings to convert the whole world save to a multiplayer-compatible map instance saved to <i>xaero->world-map->World->null->cm$converted</i>. In older versions, <em>xaero/world-map</em> is simply <em>XaeroWorldMap</em>. You can copy/move that map instance folder to your server map instances in the xaero/world-map folder.</p>\n\n### Why are multiple worlds on the same server address/IP using and writing to the same map?\n\n<p>This mod lets you have multiple maps on the same server address. You can freely switch between maps and create new ones at any time. Map overwriting can be prevented by choosing the right \"Map Selection\" mode for the type of server that you are playing on. Map selection is done in a special menu by clicking the bottom left icon on the world map screen (if the menu isn't open by default). In the menu you can switch between <em>Manual</em>, <em>World Spawn</em> and <em>Single</em> modes and select/create maps by using the top-center dropdown menu. If you hover over the <em>Map Selection</em> option, you can read everything you need to know about each map selection mode, including <em>Server</em> mode. <em>World Spawn </em>mode is the equivalent of what was used by the world map pre 1.6. So you don't have to downgrade the mod to still use it. <em>Server&nbsp;</em>mode is the best option for most cases because the server automatically controls your map selection based on server-side level IDs. It requires the mod to be installed on the server side. If changing the map selection mode switches your map to an empty one, you can always rebind the map that you want to be used by selecting it from the dropdown menu and confirming.&nbsp;</p>\n\n### Why does the world map ask for confirmation?\n\n<p>A single server address can have multiple overworlds, nethers etc. It can be due to multiple sub-servers or just multiple world saves, for example a \"Lobby\" world and a \"Game\" world. Different worlds don't have any unique identifiers on the client side, which makes it impossible to reliably differentiate between them. Because of this, the world map mod lets you have multiple world maps for each dimension on the same server address. In older mod versions, by default, map selection is <em>Manual</em>, which means that you are asked to confirm which map to use every time you switch dimensions/worlds/sub-servers. You can also choose one of the 2 automatic modes. Map selection is done in a special menu by clicking the bottom left icon on the world map screen (if the menu isn't open by default). In the menu you can switch between <em>Manual</em>, <em>World Spawn</em> and <em>Single</em> modes and select/create maps by using the top-center dropdown menu. If you hover over the <em>Map Selection</em> option, you can read everything you need to know about each map selection mode, including <em>Server</em> mode. <em>World Spawn </em>mode is the equivalent of what was used by the world map pre 1.6. So you <span style=\"text-decoration: underline;\">don't</span> have to downgrade the mod to still use it. <em>Server&nbsp;</em>mode is the best option for most cases because the server automatically controls your map selection based on server-side level IDs. It requires the mod to be installed on the server side. If changing the map selection mode switches your map to an empty one, you can always rebind the map that you want to be used by selecting it from the dropdown menu and confirming.&nbsp;</p>\n\n### Why can't I see my minimap waypoints on a server after the installation of the world map?\n\n<p>Your currently confirmed map affects which waypoints sub-world in the minimap mod is considered automatic. This allows each created map to have separate waypoints data. At any point in time, you can select and view any non-automatic waypoints sub-world by going to the waypoints menu (press U) and selecting it from top-right dropdown menu. You can make any sub-world automatic for your currently confirmed map by selecting <em>Options -&gt; Make Sub-World Auto</em>.</p>\n\n### Why has my world map \"reset\" for a specific server?\n\n<p>Do not panic. It's almost certainly not actually deleted. Old world map versions use a world spawn-based system to separate a server into multiple maps. This system often breaks because of servers adding custom functionality to the vanilla compass item by sending fake world spawn coordinates to your game client.&nbsp;<strong class=\"bbc\">Make sure you update the mod to the latest version.</strong>&nbsp;There are now multiple map selection modes you can choose between,&nbsp;<em class=\"bbc\">Manual</em>&nbsp;being the default one. You can now view any world map you have on the current server address. Click the bottom left icon on the world map to open the map selection menu, if it's not open by default. <strong>The top center dropdown menu allows you to select existing or create new world maps.</strong> You can hover over the map selection mode setting (bottom left) to read about how each mode works. <em>Server </em>mode is the best option for most cases because the server controls your map selection based on server-side level IDs. It requires the mod to be installed on the server side.&nbsp;<em class=\"bbc\">Manual</em>&nbsp;mode is the safest alternative and should work on every other server. There are also 2 additional automatic map selection modes when <em>Server</em> mode isn't an option. Read more about them in the tooltip of the setting.</p>\n<p><u><strong class=\"bbc\"><em class=\"bbc\">Old map is not in the map menu?&nbsp;</em></strong></u>This can mean 2 things: the server address has changed or the dimension IDs have changed.</p>\n<p>Let's start with the&nbsp;<u><strong class=\"bbc\">dimension ID change</strong></u>. Each dimension on the server has a separate directory in the&nbsp;<em class=\"bbc\">game directory -&gt; xaero -&gt; world-map </em>&nbsp;directory. In older versions, <em>xaero/world-map</em> is simply <em>XaeroWorldMap</em>.&nbsp;<strong class=\"bbc\">Please backup the&nbsp;<em class=\"bbc\">xaero/world-map</em>&nbsp;directory before you begin doing anything with it.</strong>&nbsp;In older mod versions, the dimension directory name contains the server address and the dimension key, for example&nbsp;<em>XaeroWorldMap/</em><em class=\"bbc\">Multiplayer_127.0.0.1_DIM1</em>. In newer versions the dimension directory is a sub-directory, so something like&nbsp;<em>xaero/world-map/</em><em class=\"bbc\">Multiplayer_127.0.0.1/DIM1.</em> For compatibility reasons, default&nbsp;<em class=\"bbc\">overworld, nether and the end</em>&nbsp;dimensions are named&nbsp;<em class=\"bbc\">null, DIM-1 and DIM1</em>. Other dimensions should have their name be based on the original dimension ID, for example&nbsp;<em class=\"bbc\">Multiplayer_127.0.0.1/minecraft$overworld_creative</em>. In 1.16+, you can see your current dimension ID on the&nbsp;<em class=\"bbc\">F3 screen ingame</em>&nbsp;to figure out which directory is meant for which dimension (for example \"minecraft:overworld\").&nbsp;<em class=\"bbc\">Please exit to the game title screen or close the game before editing files/directories.</em><br />Each dimension directory contains sub-directories, one for each map that you have in the dimension (for example&nbsp;<em class=\"bbc\">mw$default</em>&nbsp;or&nbsp;<em class=\"bbc\">mw-4,0,-4</em>) as well as a file named&nbsp;<em class=\"bbc\">dimension_config.txt&nbsp;</em>that contains the actual display names of the maps. You can use the file to help you find a specific map directory that you want. Moving/copying a map directory between dimension directories should move/copy it between dimensions. You can give it a name later ingame.&nbsp;<strong class=\"bbc\"><em class=\"bbc\">The most common case would be&nbsp;</em></strong>something like moving a map from <strong class=\"bbc\"><em class=\"bbc\">/null</em></strong>&nbsp;to <strong class=\"bbc\"><em class=\"bbc\">/minecraft$overworld_survival</em></strong>. It is needed when a 1.16+ server starts using a unique dimension ID for each sub-world.</p>\n<p><u><strong class=\"bbc\">For server address changes</strong></u>, you would first have to remove all directories inside&nbsp;<em class=\"bbc\">game directory -&gt; xaero -&gt; world-map</em>&nbsp;that contain the new server address. There is usually only 1 such directory if you are using the latest mod version.&nbsp;<strong class=\"bbc\">Please backup the whole&nbsp;<em class=\"bbc\">xaero/world-map</em>&nbsp;directory before you do that. Also, exit the game at least to the title screen.&nbsp;</strong>Then you would have to rename each old server address directory (in xaero/world-map) to the new address, replacing ONLY the address part.</p>\n<p>Message me or leave a comment if you have questions!</p>\n<p><em class=\"bbc\">Here's the old explanation if you can't update the mod to 1.6.1 or newer:</em></p>\n<div class=\"spoiler\">\n<p>Do not panic. It's almost certainly not actually deleted (this has never happened as far as I know). It's likely caused by a server plugin changing the behavior of the vanilla compass item and interfering with the mod's \"multiworld\" detection. In most such cases the compass item points to your bed instead of the global spawn point. Sleeping in the right bed again should bring everything back for you if sleeping in another one was the cause of the map \"resetting\".<br />If the map \"reset\" because of a new plugin being added (and if removing it is not an option for you), then here's how you can restore your map (and hope the plugin doesn't do it again):</p>\n<p>Make sure Minecraft is&nbsp;<strong class=\"bbc\"><em class=\"bbc\">NOT RUNNING</em></strong>.</p>\n<p>1. Go to the game directory</p>\n<p>2. Go to the&nbsp;<em class=\"bbc\">XaeroWorldMap</em>&nbsp;directory</p>\n<p>3. Find directories that represent the dimensions you have visited on the server. These directories contain the address of the server in their name. The one that ends with&nbsp;<em class=\"bbc\">_null</em>&nbsp;is the overworld,&nbsp;<em class=\"bbc\">_dim-1</em>&nbsp;is Nether etc.</p>\n<p>4. Open the overworld directory (<em class=\"bbc\">_null</em>).</p>\n<p>5. You should see multiple directories that start with&nbsp;<em class=\"bbc\">mw</em>.</p>\n<p>6. Find the one that is the biggest in file size. Copy the name of it somewhere so you don't lose it.</p>\n<p>7. Now find the newest&nbsp;<em class=\"bbc\">mw</em>&nbsp;directory (likely the emptiest or just check the creation date). Move this directory somewhere else.&nbsp;<strong class=\"bbc\">Don't delete it just yet.</strong></p>\n<p>8. Rename the directory from step 6 to the name of the backed up directory from step 7.</p>\n<p>9. Open Minecraft and test if the overworld world map is back.</p>\n<p>10. If it's not, exit Minecraft and restore the old name of the directory that you have renamed + restore the backed up folder. Reread all the steps to make sure you're doing everything right.<br />If overworld is now fixed, proceed to step 11.</p>\n<p>11. Rename and backup the directories with the same names as overworld for every other dimension that you'd like to restore.</p>\n<p><em class=\"bbc\">Contact me if you have questions!</em></p>\n</div>\n\n### Why have my waypoints not restored after restoring the map?\n\n<p>Please read the&nbsp;<em class=\"bbc\">\"Why are my waypoints no longer shown for a specific server?\"</em>&nbsp;section on the&nbsp;<a href=\"https://modrinth.com/mod/xaeros-minimap\" target=\"_blank\" rel=\"noopener noreferrer\">minimap description</a>&nbsp;page. It should cover most cases. Contact me, if it doesn't help.</p>\n\n### How do I prohibit the use of cave mode on my server?\n\n<p>To do that, please install the mod on the server side (requires a Forge/Fabric/Quilt server as of writing this). When you start your server, a new \"common\" config file will be created in the server directory (not the world folder), usually in the \"config\" folder. In the config file, you get multiple options that let you disable certain mod features for your players. Changes require a server restart.</p>\n<p>Alternatively, if you have the minimap installed, you can include the following strings in a server/system message sent to players (on login), which do the following:</p>\n<p>(requires minimap to work)&nbsp;<strong>&sect;f&sect;a&sect;i&sect;r&sect;x&sect;a&sect;e&sect;r&sect;o</strong> - causes the minimap to disable cave mode (including world map) and entity radar (fair-play mode)</p>\n<p><strong>&sect;x&sect;a&sect;e&sect;r&sect;o&sect;w&sect;m&sect;n&sect;e&sect;t&sect;h&sect;e&sect;r&sect;i&sect;s&sect;f&sect;a&sect;i&sect;r</strong> - causes world map cave mode to work in the nether despite the minimap being fair-play</p>\n<p><strong>&sect;r&sect;e&sect;s&sect;e&sect;t&sect;x&sect;a&sect;e&sect;r&sect;o</strong> - reset all the system message flags</p>\n\n### How do I let vanilla client players join my server that has the mod installed?\n\n<p>Look for a \"common\" config file directly in your server directory (not the world folder). In the config file, you get multiple options that let you disable certain mod features for your players. One of the options lets you disable the registration of status effects, which is what prevents players from joining. Changes require a server restart. Do the same for the minimap mod.</p>\n<p><span style=\"color: #800080;\"><em><strong>You are allowed to make videos using this mod.</strong></em></span></p>\n<p><span style=\"color: #800080;\"><em><strong>Using it in your modpacks is allowed with the following conditions: </strong></em></span></p>\n<ul>\n<li><span style=\"color: #800080;\"><em><strong><b><i>Only monetization of the modpack through CurseForge or Modrinth is allowed (which includes sponsored links/banners in your modpack description), unless I have given you written permission to monetize it elsewhere. Feel free to private message me about it. I'm more likely to give you permission than not.</i></b></strong></em></span></li>\n<li><span style=\"color: #800080;\"><em><strong>If the modpack is distributed outside of CurseForge and Modrinth, then you must credit me by providing an easily accessible link to one of my official pages for the mod, to the users outside of CurseForge and Modrinth.&nbsp;</strong></em></span></li>\n<li><span style=\"color: #800080;\"><em><strong>The name and/or description of the modpack must not be easily confused with the names/descriptions of my mods.</strong></em></span></li>\n<li><span style=\"color: #800080;\"><em><strong>If I have given you written permission to monetize the modpack outside of CurseForge and Modrinth, then, if I request you to, you must remove monetization outside of CurseForge and Modrinth.&nbsp;</strong></em></span></li>\n</ul>", "published": "2023-04-22T09:32:10.659737Z", "updated": "2025-06-18T10:27:06.170688Z", "status": "approved", "license": {"id": "LicenseRef-All-Rights-Reserved", "name": ""}, "client_side": "required", "server_side": "unsupported", "categories": ["adventure", "transportation", "utility"], "loaders": ["fabric", "forge", "neoforge", "quilt"], "icon_url": "https://cdn.modrinth.com/data/NcUtCpym/354080f65407e49f486fcf9c4580e82c45ae63b8_96.webp", "issues_url": "https://curseforge.com/minecraft/mc-mods/xaeros-world-map/issues", "donation_urls": [{"id": "patreon", "platform": "Patreon", "url": "https://www.patreon.com/xaero96"}], "gallery": []}, "modrinthVersion": {"id": "3XtBvLkH", "project_id": "NcUtCpym", "author_id": "ANNqL3WC", "name": "1.39.9", "version_number": "1.39.9_Fabric_1.21", "changelog": "[Read changelogs](https://chocolateminecraft.com/update.php?mod_id=2)", "date_published": "2025-05-17T08:41:34.887159Z", "version_type": "release", "files": [{"hashes": {"sha512": "0780efbcf18e4174e15ae540387f16cac5e68b39c728bb523805af4e7485de72cdf0587d488116743fffdd12546adc8e4b37b27a85a70406ff5527abe85a3267", "sha1": "2d23d1604f7e03dd82a140cc8ee2b6881ab48b48"}, "url": "https://cdn.modrinth.com/data/NcUtCpym/versions/3XtBvLkH/XaerosWorldMap_1.39.9_Fabric_1.21.jar", "filename": "XaerosWorldMap_1.39.9_Fabric_1.21.jar", "primary": true, "size": 969206}], "dependencies": [{"project_id": "P7dR8mSH", "dependency_type": "required"}, {"project_id": "gF3BGWvG", "dependency_type": "optional"}], "game_versions": ["1.21", "1.21.1"], "loaders": ["fabric", "quilt"]}}, {"name": "Clumps", "version": "Clumps-fabric-1.21.1-********.jar", "optional": true, "file": "Clumps-fabric-1.21.1-********.jar", "type": "mods", "description": "Clumps XP orbs together to reduce lag", "disabled": false, "userAdded": true, "wasSelected": true, "skipped": false, "curseForgeProjectId": 256717, "curseForgeFileId": 5623729, "curseForgeProject": {"id": 256717, "name": "Clumps", "authors": [{"id": 10618648, "name": "Jaredlll08", "url": "https://www.curseforge.com/members/jaredlll08"}], "gameId": 432, "summary": "Clumps XP orbs together to reduce lag", "categories": [{"name": "Server Utility", "slug": "server-utility", "url": "https://www.curseforge.com/minecraft/mc-mods/server-utility", "dateModified": "2014-05-08T17:44:55.057Z", "gameId": 432, "isClass": false, "id": 435, "iconUrl": "https://media.forgecdn.net/avatars/6/48/635351498950580836.png", "parentCategoryId": 6, "classId": 6}, {"name": "Utility & QoL", "slug": "utility-qol", "url": "https://www.curseforge.com/minecraft/mc-mods/utility-qol", "dateModified": "2021-11-17T11:45:09.143Z", "gameId": 432, "isClass": false, "id": 5191, "iconUrl": "https://media.forgecdn.net/avatars/456/558/637727379086405233.png", "parentCategoryId": 6, "classId": 6}, {"name": "Storage", "slug": "storage", "url": "https://www.curseforge.com/minecraft/mc-mods/storage", "dateModified": "2014-05-08T17:41:17.203Z", "gameId": 432, "isClass": false, "id": 420, "iconUrl": "https://media.forgecdn.net/avatars/6/35/635351496772023801.png", "parentCategoryId": 6, "classId": 6}], "status": 4, "primaryCategoryId": 435, "classId": 6, "slug": "clumps", "isFeatured": false, "dateModified": "2025-03-30T21:51:57.963Z", "dateCreated": "2017-01-07T11:46:23.243Z", "dateReleased": "2025-03-30T21:40:38.11Z", "links": {"websiteUrl": "https://www.curseforge.com/minecraft/mc-mods/clumps", "wikiUrl": "", "issuesUrl": "https://github.com/jaredlll08/Clumps/issues", "sourceUrl": "https://github.com/jaredlll08/Clumps"}, "logo": {"id": 1289817, "description": "", "thumbnailUrl": "https://media.forgecdn.net/avatars/thumbnails/1289/817/256/256/638838549850518521_animated.gif", "title": "638838549850518521.gif", "url": "https://media.forgecdn.net/avatars/1289/817/638838549850518521.gif", "modId": 256717}, "allowModDistribution": true, "screenshots": [], "mainFileId": 6368577}, "curseForgeFile": {"id": 5623729, "gameId": 432, "isAvailable": true, "displayName": "Clumps-fabric-1.21.1-********.jar", "fileName": "Clumps-fabric-1.21.1-********.jar", "releaseType": 1, "fileStatus": 4, "fileDate": "2024-08-13T07:49:05.263Z", "fileLength": 20723, "dependencies": [{"fileId": 0, "modId": 306612, "relationType": 3}], "alternateFileId": 0, "modules": [{"fingerprint": 30592064, "name": "META-INF"}, {"fingerprint": 2145951388, "name": "clumps.mixins.json"}, {"fingerprint": 573669456, "name": "clumps.refmap.json"}, {"fingerprint": 38162433, "name": "com"}, {"fingerprint": 4104758643, "name": "fabric.mod.json"}, {"fingerprint": 1788784743, "name": "icon.png"}, {"fingerprint": 2425768564, "name": "pack.mcmeta"}], "isServerPack": false, "hashes": [{"value": "14628ba309d876cfefc4cef864a66ef12812066c", "algo": 1}, {"value": "8715713c6b63565a5eafb4c925275035", "algo": 2}], "sortableGameVersions": [{"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-09-01T00:00:00Z", "gameVersionName": "<PERSON><PERSON><PERSON>"}, {"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.1", "gameVersionReleaseDate": "2024-08-08T15:17:53.787Z", "gameVersionName": "1.21.1"}], "gameVersions": ["<PERSON><PERSON><PERSON>", "1.21.1"], "fileFingerprint": 1960002860, "modId": 256717}, "modrinthProject": {"id": "Wnxd13zP", "slug": "clumps", "project_type": "mod", "team": "twbRaNO5", "title": "Clumps", "description": "Clumps XP orbs together to reduce lag", "body": "Clumps groups XP orbs together into a single entity to reduce lag when there are many in a small area. On top of this, it also makes the player immediately collect the orbs once they touch the player, so you are not stuck with a bunch of orbs in your face.\n\n### Do I have to install it client or server side?\n* For **1.17 or newer versions** it is only required on **server side**.\n* For **older Minecraft versions** you have to install it on **both sides**.\n\nIf you would like to support me in my modding endeavors, you can become a patron via **[![Patreon logo](https://i.imgur.com/CAJuExT.png) Patreon](https://www.patreon.com/jaredlll08)**.\n\n[![Nodecraft sponsor banner](https://assets.blamejared.com/nodecraft/jared.jpg)](https://nodecraft.com/r/jared)\n\n**This project is sponsored by Nodecraft. Use code [JARED](https://nodecraft.com/r/jared) for 30% off your first month of service!**", "published": "2023-06-04T08:54:48.176874Z", "updated": "2025-03-30T21:40:48.982075Z", "status": "approved", "license": {"id": "MIT", "name": "MIT License"}, "client_side": "required", "server_side": "required", "categories": ["storage", "utility"], "loaders": ["fabric", "forge", "neoforge"], "icon_url": "https://cdn.modrinth.com/data/Wnxd13zP/6a965bb7974c3e759a53a1c89c35de4acd4cf86a_96.webp", "issues_url": "https://github.com/jaredlll08/Clumps/issues", "source_url": "https://github.com/jaredlll08/Clumps", "donation_urls": [{"id": "patreon", "platform": "Patreon", "url": "https://www.patreon.com/jaredlll08"}], "gallery": []}, "modrinthVersion": {"id": "3ene3W1l", "project_id": "Wnxd13zP", "author_id": "l45nT5ov", "name": "Fabric-1.21.1-********", "version_number": "********", "changelog": "- [Port to 1.21.1. Fix Forge version not working. Close #147](https://github.com/jaredlll08/Clumps/commit/9dc57a1d3bd3cab65a34394718bc5813c46c9915) - Jared \n- [Restore Forge support (#143)](https://github.com/jaredlll08/Clumps/commit/515f24247c72ae77b5b1c043389e9b4a86679328) - <PERSON><PERSON>_Ninja \n- [Port to 1.21](https://github.com/jaredlll08/Clumps/commit/327ca88f573f0a7f46b1c1701aa9e4a5a3b096f5) - Jared \n- [Port to 1.20.6. Close #139](https://github.com/jaredlll08/Clumps/commit/0bc727c4345894322540c709889af89ba2492c69) - Jared \n- [port to 1.20.5](https://github.com/jaredlll08/Clumps/commit/959e3961e6b077e286a845c9ca6051959f192f58) - Jared \n- [Update mod version](https://github.com/jaredlll08/Clumps/commit/6f56ba314adf695e0ef34f1a4d4084b9c43c1ac6) - Jared \n- [Port to 1.20.4. Close #130](https://github.com/jaredlll08/Clumps/commit/77b2aaf1d70d174b126f9cd200955f2521513ae6) - Jared \n- [port to 1.20.3](https://github.com/jaredlll08/Clumps/commit/f90851d213cace19045829654981354f7b0b6da3) - Jared \n- [Fix refmap not being used. Close #131](https://github.com/jaredlll08/Clumps/commit/f04c393a7f60be57491ab35bb9d97b007fab36d1) - Jared \n- [Add neoforge support](https://github.com/jaredlll08/Clumps/commit/9bb8fc0bcbb40d3c0f4ff29faefcc5dc154b06e6) - Jared", "date_published": "2024-08-13T07:49:13.355334Z", "version_type": "release", "files": [{"hashes": {"sha512": "0aa8e3508d0a40ef814d4064c0b6cadba6326128dd878fe69f30677c889cec4ccb8f639c22bdd7083a73ae8fa76e1c115b5e4b1885904dc1244b02ab2f728e78", "sha1": "14628ba309d876cfefc4cef864a66ef12812066c"}, "url": "https://cdn.modrinth.com/data/Wnxd13zP/versions/3ene3W1l/Clumps-fabric-1.21.1-********.jar", "filename": "Clumps-fabric-1.21.1-********.jar", "primary": true, "size": 20723}], "dependencies": [{"project_id": "P7dR8mSH", "dependency_type": "required"}], "game_versions": ["1.21.1"], "loaders": ["fabric"]}}, {"name": "ViaFabricPlus", "version": "ViaFabricPlus 1.21.1 Backport", "optional": true, "file": "ViaFabricPlus-3.4.9.jar", "type": "mods", "description": "Minecraft Fabric mod which allows you to join EVERY Minecraft server version (Classic, Alpha, Beta, Release, April Fools, Bedrock)", "disabled": false, "userAdded": true, "wasSelected": true, "skipped": false, "modrinthProject": {"id": "rIC2XJV4", "slug": "viafabricplus", "project_type": "mod", "team": "rQ5MtNxi", "title": "ViaFabricPlus", "description": "Minecraft Fabric mod which allows you to join EVERY Minecraft server version (Classic, Alpha, Beta, Release, April Fools, Bedrock)", "body": "# ViaFabricPlus\nMinecraft Fabric mod which allows you to join EVERY Minecraft server version (Classic, Alpha, Beta, Release, April Fools, Bedrock)\n\n![https://rocketnode.com/enza](https://cdn.modrinth.com/data/cached_images/fc98262c77afba9bdfa0e74487c1a5269f060331_0.webp)\n\n# Why another protocol translator?\nViaFabricPlus implements the [ViaVersion projects](https://github.com/ViaVersion) into Fabric and provides tons of fixes to the existing protocol translation which can't be implemented in the original ViaVersion project.\nThese fixes consist of movment changes, block/entity collisions, rendering changes, and many more.\n\nAt the time of writing, ViaFabricPlus is the only mod that supports joining all Minecraft server versions down to the first multiplayer version while implementing\nlegacy combat mechanics, movement, and rendering changes to make the gameplay more feel like the old days.\n\n**On the other hand, ViaFabricPlus supports only the latest Minecraft client version, and only Fabric.**\n\n## Supported Server versions\n- Release (1.0.0 - 1.21.5)\n- Beta (b1.0 - b1.8.1)\n- Alpha (a1.0.15 - a1.2.6)\n- Classic (c0.0.15 - c0.30 including [CPE](https://wiki.vg/Classic_Protocol_Extension))\n- April Fools (3D Shareware, 20w14infinite, 25w14craftmine)\n- Combat Snapshots (Combat Test 8c)\n- Bedrock Edition 1.21.60 ([Some features are missing](https://github.com/RaphiMC/ViaBedrock#features))\n\n## How to (Users)\n- [A detailed guide on how to install and use the mod](https://github.com/ViaVersion/ViaFabricPlus/tree/main/docs/USAGE.md)\n- If you encounter any issues, please report them on either:\n  - [the issue tracker](https://github.com/ViaVersion/ViaFabricPlus/issues) \n  - [the ViaVersion Discord](https://discord.gg/viaversion)\n\n## How to (Developers)\n- [Detailed guidelines for contributions as well as setting up a dev environment](https://github.com/ViaVersion/ViaFabricPlus/tree/main/docs/UPDATE_INSTRUCTIONS.md)\n- [API and integration examples for developers](https://github.com/ViaVersion/ViaFabricPlus/tree/main/docs/DEVELOPER_API.md)\n\n## ViaFabric\n[ViaFabric](https://github.com/ViaVersion/ViaFabric) can be used for server-side purposes or when using older versions of the game.\n\n### Does it work with ViaFabricPlus:\n\n- No, ViaFabricPlus cannot be used with ViaFabric.\n\n### Differences with ViaFabric:\nhttps://github.com/ViaVersion/ViaFabric?tab=readme-ov-file#differences-with-viafabricplus\n\n## Credits\nSpecial thanks to all our [Contributors](https://github.com/ViaVersion/ViaFabricPllus/graphs/contributors).\n\n## Disclaimer\nIt cannot be guaranteed that this mod is allowed on specific servers as it can possibly cause problems with anti-cheat plugins.\\\n***(USE ONLY WITH CAUTION!)***\n\n## Partnership with RocketNode Hosting\nRocketNode provides top-tier server hosting worldwide, allowing you to set up your server with just a few clicks! Their intuitive dashboard makes server management a breeze, and you can even schedule automatic restarts without needing any coding knowledge.\n\nUse the code ENZA to get 25% off your first month on any of their gaming servers. Get started here: https://rocketnode.com/enza\n", "published": "2023-03-01T15:53:22.364374Z", "updated": "2025-06-21T20:26:05.331007Z", "status": "approved", "license": {"id": "GPL-3.0-only", "name": "GNU General Public License v3.0 only"}, "client_side": "required", "server_side": "unsupported", "categories": ["cursed", "library", "utility"], "loaders": ["fabric", "quilt"], "icon_url": "https://cdn.modrinth.com/data/rIC2XJV4/34c6c433ac1cbfc1401dbe749214641fce8f1ebf_96.webp", "issues_url": "https://github.com/ViaVersion/ViaFabricPlus/issues", "source_url": "https://github.com/ViaVersion/ViaFabricPlus", "discord_url": "https://discord.gg/viaversion", "donation_urls": [{"id": "ko-fi", "platform": "Ko-fi", "url": "https://ko-fi.com/florian<PERSON>hael"}, {"id": "paypal", "platform": "<PERSON><PERSON>", "url": "https://www.paypal.com/donate/?hosted_button_id=9RJRFKZQ2JTZS"}, {"id": "github", "platform": "<PERSON><PERSON><PERSON>", "url": "https://github.com/sponsors/FlorianMichael"}], "gallery": [{"url": "https://cdn.modrinth.com/data/rIC2XJV4/images/536c64fdf36a55d892117d75c96ab87b7b9e7a80_350.webp", "raw_url": "https://cdn.modrinth.com/data/rIC2XJV4/images/536c64fdf36a55d892117d75c96ab87b7b9e7a80.png", "featured": false, "title": "Protocol Selection", "description": "The main window where you can select any Minecraft version to join to.", "created": "2025-04-23T22:55:01.716784Z", "ordering": 0.0}, {"url": "https://cdn.modrinth.com/data/rIC2XJV4/images/7f2e2f0cf90cc2c96b5cd49efa8529ad49e31fbf.png", "raw_url": "https://cdn.modrinth.com/data/rIC2XJV4/images/7f2e2f0cf90cc2c96b5cd49efa8529ad49e31fbf.png", "featured": false, "title": "Debug Hud", "description": "Visible when you select a different version than the native version in the protocol selection menu", "created": "2025-04-23T22:55:34.838435Z", "ordering": 0.0}, {"url": "https://cdn.modrinth.com/data/rIC2XJV4/images/d6f01443a9e0d6d1fc13ff412070440f1e627c68_350.webp", "raw_url": "https://cdn.modrinth.com/data/rIC2XJV4/images/d6f01443a9e0d6d1fc13ff412070440f1e627c68.png", "featured": true, "title": "<PERSON><PERSON><PERSON>", "description": "Allows you to access some debug/visual as well as general protocol translation settings.", "created": "2025-04-23T22:55:20.511528Z", "ordering": 0.0}]}, "modrinthVersion": {"id": "9aI7gGPn", "project_id": "rIC2XJV4", "author_id": "rwEkdtka", "name": "ViaFabricPlus 1.21.1 Backport", "version_number": "3.4.9", "changelog": "ViaFabricPlus 3.4.9 with updated ViaVersion (Backport)\n", "date_published": "2024-11-16T14:08:08.751599Z", "version_type": "alpha", "files": [{"hashes": {"sha1": "33edb94ab4ca5e20bafabaead0cedc5c3c09abf0", "sha512": "60ca891a915af4345c3b982bf2e3a3914e4b5dd1d6c175918845a39a49748cf97c1d315608a398cb58e91e813c035095e4b763626c99b5e64d94f3052bbe2f56"}, "url": "https://cdn.modrinth.com/data/rIC2XJV4/versions/9aI7gGPn/ViaFabricPlus-3.4.9.jar", "filename": "ViaFabricPlus-3.4.9.jar", "primary": true, "size": 11559457}], "dependencies": [], "game_versions": ["1.21", "1.21.1"], "loaders": ["fabric"]}}, {"name": "Xaero's Minimap", "version": "v25.2.6 for Fabric 1.21.1", "optional": true, "file": "Xaeros_Minimap_25.2.6_Fabric_1.21.jar", "type": "mods", "description": "Displays the nearby world terrain, players, mobs, entities in the corner of your screen. Lets you create waypoints which help you find the locations you've marked.", "disabled": false, "userAdded": true, "wasSelected": true, "skipped": false, "curseForgeProjectId": 263420, "curseForgeFileId": 6515046, "curseForgeProject": {"id": 263420, "name": "Xaero's Minimap", "authors": [{"id": 12362980, "name": "xaero96", "url": "https://www.curseforge.com/members/xaero96"}], "gameId": 432, "summary": "Displays the nearby world terrain, players, mobs, entities in the corner of your screen. Lets you create waypoints which help you find the locations you've marked.", "categories": [{"name": "Player Transport", "slug": "technology-player-transport", "url": "https://www.curseforge.com/minecraft/mc-mods/technology/technology-player-transport", "dateModified": "2014-05-08T17:38:58.357Z", "gameId": 432, "isClass": false, "id": 414, "iconUrl": "https://media.forgecdn.net/avatars/6/29/635351495383551178.png", "parentCategoryId": 412, "classId": 6}, {"name": "Map and Information", "slug": "map-information", "url": "https://www.curseforge.com/minecraft/mc-mods/map-information", "dateModified": "2014-05-08T17:42:23.74Z", "gameId": 432, "isClass": false, "id": 423, "iconUrl": "https://media.forgecdn.net/avatars/6/38/635351497437388438.png", "parentCategoryId": 6, "classId": 6}], "status": 4, "primaryCategoryId": 423, "classId": 6, "slug": "xaeros-minimap", "isFeatured": false, "dateModified": "2025-06-18T10:44:28.45Z", "dateCreated": "2017-03-22T15:56:16.757Z", "dateReleased": "2025-06-18T10:17:45.463Z", "links": {"websiteUrl": "https://www.curseforge.com/minecraft/mc-mods/xaeros-minimap", "wikiUrl": "", "issuesUrl": "https://legacy.curseforge.com/minecraft/mc-mods/xaeros-minimap/issues"}, "logo": {"id": 92854, "description": "", "thumbnailUrl": "https://media.forgecdn.net/avatars/thumbnails/92/854/256/256/636258666554688823.png", "title": "636258666554688823.png", "url": "https://media.forgecdn.net/avatars/92/854/636258666554688823.png", "modId": 263420}, "allowModDistribution": true, "screenshots": [], "mainFileId": 6665478}, "curseForgeFile": {"id": 6515046, "gameId": 432, "isAvailable": true, "displayName": "v25.2.6 for Fabric 1.21.1", "fileName": "Xaeros_Minimap_25.2.6_Fabric_1.21.jar", "releaseType": 2, "fileStatus": 4, "fileDate": "2025-05-10T07:34:37.193Z", "fileLength": 1741254, "dependencies": [{"fileId": 0, "modId": 317780, "relationType": 2}, {"fileId": 0, "modId": 636608, "relationType": 2}, {"fileId": 0, "modId": 306612, "relationType": 3}], "alternateFileId": 0, "modules": [{"fingerprint": **********, "name": "META-INF"}, {"fingerprint": **********, "name": "LICENSE_xaerohud"}, {"fingerprint": **********, "name": "assets"}, {"fingerprint": **********, "name": "fabric.mod.json"}, {"fingerprint": **********, "name": "pack.mcmeta"}, {"fingerprint": **********, "name": "xae<PERSON>"}, {"fingerprint": **********, "name": "xaerohud.mixins.json"}, {"fingerprint": **********, "name": "xaerominimap.mixins.json"}, {"fingerprint": 21106561, "name": "xaerominimap.refmap.json"}], "isServerPack": false, "hashes": [{"value": "36ec2f79b89539e82b430f550fd12731c5bef4b2", "algo": 1}, {"value": "b5af8349131a1a04e2da09ad678822f3", "algo": 2}], "sortableGameVersions": [{"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-09-01T00:00:00Z", "gameVersionName": "<PERSON><PERSON><PERSON>"}, {"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.1", "gameVersionReleaseDate": "2024-08-08T15:17:53.787Z", "gameVersionName": "1.21.1"}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-05-26T00:00:00Z", "gameVersionName": "Quilt"}], "gameVersions": ["<PERSON><PERSON><PERSON>", "1.21.1", "Quilt"], "fileFingerprint": 3541049784, "modId": 263420}, "modrinthProject": {"id": "1bokaNcj", "slug": "xaeros-minimap", "project_type": "mod", "team": "9lteWJca", "title": "Xaero's Minimap", "description": "Displays a map of the nearby world terrain, players, mobs, entities in the corner of your screen. Lets you create waypoints which help you find the locations you've marked.", "body": "<p><img src=\"https://chocolateminecraft.com/images/minimap_2020.png\" width=\"1125\" height=\"655\" /></p>\n\n### Do not use XaeroPlus with this mod! It is not affiliated with me and causes critical untrackable bugs/crashes. It is also allegedly affiliated with a server griefing group, which can put your server builds at risk.\n\n<p>Unlike many other minimap mods, Xaero's minimap keeps the <strong>aesthetic of vanilla Minecraft</strong>, which helps it be a more seamless addition to the game. It is also <strong>the first rotating square minimap for Minecraft. </strong>Among a wide variety of customization settings, there is an option for a <strong>circle-shaped minimap</strong> and a separate setting for <strong>locking the minimap's rotation</strong> (using a direction arrow instead). The minimap can display the surrounding entities, including <strong>players, mobs and items</strong>, as <strong>dots with a custom color</strong> or as <strong>icons (usually heads)</strong>. In addition to the default <strong>compass directions</strong>, you can place your own&nbsp;<strong>waypoints </strong>that are visible both on the minimap and in the game world.&nbsp;Waypoints help you find previously visited locations or reach specific coordinates, with <strong>optional teleportation</strong>. The minimap can display the surrounding blocks above ground and below ground (<strong>cave mode</strong>). These are just some of the features of the mod while there are many more. Please read the \"Minimap features\" section below for a more complete list. For the purposes of following multiplayer server rules, there are 2 mod editions, full and fair-play, the latter being designed for fair PVP.&nbsp;<strong>This mod is mostly client-sided. Installing it on the server side is optional for some additional features like world identification.</strong></p>\n\n### Follow me on Mastodon (Twitter alternative)?&nbsp;<a href=\"https://mas.to/@xaero96\" target=\"_blank\" rel=\"nofollow noopener noreferrer\">mas.to/@xaero96</a>\n\n### Support me on Patreon?</strong>&nbsp;<span style=\"text-decoration: underline;\"><a href=\"https://bit.ly/PatreonXaero96\">patreon.com/xaero96</a></span>\n\n<p>Thank you!</p>\n\n### Features\n\n<ul>\n<li><strong class=\"bbc\">Look of vanilla Minecraft</strong>.</li>\n<li>Multiple minimap size options including automatic.&nbsp;</li>\n<li>Multiple zoom options.</li>\n<li>Two minimap shape options: <strong>square or circle</strong>.</li>\n<li>Runs&nbsp;<strong class=\"bbc\">smoother than a lot of minimap mods</strong>.</li>\n<li>Can be placed anywhere on the screen using the \"Change Position\" setting/screen. You can even shift it by 1 pixel if you so wish. Can be useful when playing with other UI mods.</li>\n<li>2 block color modes:&nbsp;<strong class=\"bbc\">Vanilla</strong>, which uses the colors of vanilla Minecraft maps and&nbsp;<strong class=\"bbc\">Accurate</strong>, which uses the colors of block textures and biomes. You can also enable biome colors for the Vanilla mode using a separate setting.</li>\n<li>A custom key binding (Z by default) to temporarily enlarge the minimap to take a better look around.&nbsp;<strong>All key bindings are in the vanilla controls menu.&nbsp;</strong>There are also settings for controlling the behavior of the minimap when it is enlarged, for example to center the minimap on the screen or to fully zoom out the minimap.</li>\n<li>Map shading customization with the \"Terrain Depth\" and \"Terrain Slopes\" settings.</li>\n<li>Compatible with&nbsp;<a href=\"https://modrinth.com/mod/xaeros-world-map\" target=\"_blank\" rel=\"noopener noreferrer\">Xaero's World Map</a>. By default uses the map textures generated by the world map mod instead of generating its own. This improves performance when using both mods.</li>\n<li>Option to limit your minimap access by binding it to an item required in your hotbar or to be equipped. For example, add <em>minimapItemId:minecraft:compass</em> anywhere in the config file to bind it to the compass item. This way the minimap will not be displayed unless you have the compass in your hotbar or equipped.</li>\n<li>Potion effects to control the usage of the minimap or some of its features for the players on your server/map. The following potion effects exist as of writing this: <em>xaerominimap:no_minimap, xaerominimap:no_entity_radar, xaerominimap:no_waypoints, xaerominimap:no_cave_maps. </em>The effects are of the neutral type by default, but you can also specify a harmful type by appending <em>_harmful</em>&nbsp;to the end of the effect ID, for example&nbsp;<em>xaerominimap:no_entity_radar_harmful</em>.</li>\n<li>Translated to a lot of languages.</li>\n<li>Option to&nbsp;<strong class=\"bbc\">lock north</strong>&nbsp;to stop the minimap from rotating and instead display the direction of your character with an arrow.</li>\n<li>Works both above and under ground thanks to the&nbsp;<strong>automatic cave mode</strong>. Cave mode should also activate inside buildings. You can choose the size of the \"solid roof\" above you that the mod searches for to activate cave mode. Roof size 3x3 and above should prevent cave mode activation when standing under log blocks while cutting wood.</li>\n<li><strong class=\"bbc\">Waypoints</strong>. Practically an infinite amount. Can be teleported to (permission for teleportation chat command needed). Waypoints are rendered in the game world and displayed on the minimap. You can hide all or specific waypoints. Press&nbsp;<strong class=\"bbc\">B</strong>&nbsp;to create a new waypoint. Press&nbsp;<strong class=\"bbc\">U</strong>&nbsp;to list all the waypoints. In the waypoints list, you can add/edit a single or multiple waypoints, sort them, view waypoints from other sub-worlds/dimensions and much more.</li>\n<li><strong>Local and global waypoints. </strong>To clear the screen from unnecessary waypoints you can create local waypoints that, unlike global ones, are affected by the \"Max WP Draw Distance\" option. This enables you to set a single global waypoint for a large area/region and multiple local ones in the area/region. Only the global waypoints will be visible when you are beyond the maximum draw distance.</li>\n<li><strong>Waypoint sets.</strong>&nbsp;You can organize your waypoints by sorting them into sets. Your current set can be quickly switched with a key binding. <strong>All key bindings are in the vanilla controls menu.</strong></li>\n<li><strong>Waypoint sharing</strong>. You can share your waypoints in the game chat with players on the server. This is done from the waypoints list (press U).</li>\n<li><strong class=\"bbc\">Deathpoints</strong>. An&nbsp;<strong class=\"bbc\">automatic skull-shaped waypoint</strong>&nbsp;is created&nbsp;<strong class=\"bbc\">on each death</strong>. Old deathpoints are converted to regular waypoints, which you can disable using the \"Keep Old Deathpoints\" setting.</li>\n<li>Displays chunk claims and ally players from the <a href=\"https://modrinth.com/mod/open-parties-and-claims\" target=\"_blank\" rel=\"noopener noreferrer\">Open Parties and Claims</a> mod.</li>\n<li>Togglable&nbsp;<strong class=\"bbc\">chunk grid</strong>. The chunk grid shows you where block chunks begin and end.&nbsp;</li>\n<li>Togglable&nbsp;<strong class=\"bbc\">slime chunks</strong>&nbsp;mode. The chunks where slimes can spawn are marked with a green overlay.</li>\n<li><strong>Custom light overlay</strong>. You can setup a light overlay that will mark blocks with a custom color if they are within a custom light value range, which is block light from 0 to 7 by default. The default settings are meant for finding mob-spawnable blocks. You can use it for any purpose.</li>\n<li>Multi-world detection in multiplayer. If you install the minimap mod on the server side in addition to the client side, each server world should get a separate waypoints \"sub-world\". Otherwise a much less reliable world separation is used, which is based on the world spawnpoint. If you can't install the mod on the server side, it is recommended to turn off multi-world detection for servers that only have a single \"world\". You can do it through the <em>Waypoints Menu (press U) -&gt; Options</em> screen.</li>\n<li>Displays&nbsp;<strong class=\"bbc\">mobs</strong>&nbsp;on the map as&nbsp;<strong class=\"bbc\">yellow dots.</strong>&nbsp;<strong class=\"bbc\">Hostile and friendly</strong>&nbsp;mobs can be colored&nbsp;<strong class=\"bbc\">differently. </strong>Can also be displayed as <strong>icons/heads</strong>.&nbsp;Check the <strong>\"Entity Radar\"</strong> settings.</li>\n<li>Displays&nbsp;<strong class=\"bbc\">players</strong>&nbsp;on the map as&nbsp;<strong class=\"bbc\">white dots or player heads.&nbsp;</strong>Can also be displayed as <strong>icons/heads</strong>.&nbsp;Check the <strong>\"Entity Radar\"</strong> settings.</li>\n<li>Displays&nbsp;<strong class=\"bbc\">items</strong>&nbsp;on the map as&nbsp;<strong class=\"bbc\">red dots. </strong>Check the <strong>\"Entity Radar\"</strong> settings.</li>\n<li>Displays all the&nbsp;<strong class=\"bbc\">other entities</strong>&nbsp;such as arrows and item frames as&nbsp;<strong class=\"bbc\">purple dots</strong><span class=\"bbc\">.</span><strong class=\"bbc\">&nbsp;</strong>Different types of entities can be disabled in the settings.&nbsp;Check the <strong>\"Entity Radar\"</strong> settings.</li>\n<li>Options for <strong>displaying entity names </strong>on the minimap<strong>&nbsp;</strong>in the \"Entity Radar\" settings.</li>\n<li>Zoom in and out key bindings&nbsp;<strong class=\"bbc\">(I and O by default) </strong>to efficiently change the zoom level to fit your preference or use case.</li>\n<li><strong class=\"bbc\">Coordinates&nbsp;</strong>under the minimap.</li>\n<li><strong class=\"bbc\">Current biome name</strong>&nbsp;under the minimap. Disabled by default and needs to be enabled in the mod settings (press Y).</li>\n<li><strong class=\"bbc\">Light level</strong>&nbsp;under the minimap. Also disabled by default and needs to be enabled in the mod settings (press Y).</li>\n<li><strong>Current game time</strong> under the minimap. Disabled by default. You can choose between the 12 hour and the 24 hour formats.</li>\n<li><strong>Current camera angles</strong> under the minimap. Disabled by default.</li>\n<li>Setting to hide/unhide all waypoint coordinates from all UI screens. Can be useful for streams/videos.</li>\n<li>Setting to fully hide/unhide all mentions of server addresses in dropdown menus. The setting is set to \"Partial\" by default, which is probably good enough in most cases.&nbsp;</li>\n<li>A LOT MORE SETTINGS. Too many to list them all here. Press&nbsp;<strong class=\"bbc\">Y</strong>&nbsp;in game to open the settings and take a look.</li>\n<li>2 mod editions: full and fair-play.&nbsp;<strong class=\"bbc\">Full version</strong>&nbsp;does not have any limits.&nbsp;<a href=\"https://modrinth.com/mod/xaeros-minimap-fair\" target=\"_blank\" rel=\"noopener noreferrer\"><strong class=\"bbc\">Fair-play</strong></a>&nbsp;version does not display any entities or cave maps which is more suitable for PVP against people who don't use a minimap.</li>\n</ul>\n<p><a href=\"https://modrinth.com/mod/xaeros-minimap-fair\"><span style=\"text-decoration: underline; font-size: 18px;\">Get fair-play edition</span></a></p>\n<p><a href=\"https://www.modrinth.com/mod/xaeros-world-map\" target=\"_blank\" rel=\"noopener noreferrer\"><span style=\"text-decoration: underline; font-size: 18px;\">Get Xaero's World Map</span></a></p>\n\n## FAQ\n\n### Why does waypoint teleportation not work for me?\n\n<p>There can be many reasons for this. The mod sends a chat command&nbsp;to teleport you to the waypoints, which is <em>/tp @s x y z </em>by default, so appropriate permission is necessary. The vanilla <em>/tp</em> command requires OP status (cheats enabled) but it's possible to have separate permissions for commands on a non-vanilla server with certain plugins installed. The used command can be changed in the minimap settings with the \"Default Teleport Command\" option or per server in the waypoints menu with the \"Teleport Chat Command\" option. Non-vanilla teleportation commands often don't support the selectors such as @s, so you might have to remove it from the configured command. Right-click teleportation on the world map uses its own setting for the command. You can read about it on the world map description page under FAQ.</p>\n<p>If the \"Teleport\" button is disabled, then it can also mean multiple things. The most common reason for this is manually using waypoints from a sub-world that isn't the \"auto\" one but still from the same dimension. If the \"auto\" sub-world isn't what is supposed to be automatically picked on a certain world, then select the correct sub-world and use \"Make Sub-world Auto\" in the waypoints menu Options. This will swap all waypoints data between the 2 sub-worlds and you'll be able to teleport to the waypoints, which are now in your \"auto\" sub-world.</p>\n<p>If the \"auto\" sub-world IS selected but the teleport button is still disabled, then it probably means that teleportation was manually disabled for the server/world. Check the teleportation option in the waypoints menu Options. If it's disabled, you can only enable it through the server-specific config file in the game directory -&gt; xaero/minimap (XaeroWaypoints in older versions). I recommend exiting Minecraft before editing any config files. And make backups from time to time.</p>\n\n### Why are my waypoints no longer shown for a specific server?\n\n<p>Do not panic. It's almost certainly not actually deleted. If you use the world map mod and it has also reset, then please start by restoring it first before dealing with the waypoints. Read the&nbsp;<em class=\"bbc\">\"Why has my world map \"reset\" for a specific server?\"</em>&nbsp;section on the world map&nbsp;<a href=\"https://www.modrinth.com/mod/xaeros-world-map\" target=\"_blank\" rel=\"noopener noreferrer\">description page</a>. Done with that? Please continue reading.</p>\n<p>Start by opening the full waypoint menu. Press U ingame to do so. Check the vanilla controls menu, if the U key doesn't work. Go through the sub-worlds listed in the top-right dropdown menu. All waypoint sub-worlds for the current server address should be there. If none of them have the right waypoints for the world/dimension that you are in,&nbsp;then the server address was likely changed. Find the old server address in the top-<strong class=\"bbc\">LEFT</strong>&nbsp;dropdown menu, select it and do&nbsp;<em class=\"bbc\">Options -&gt; Make World/Server Auto</em>. You should now be able to find the correct sub-world in the top-right dropdown menu. If the right sub-world is already marked with (auto), which means that the sub-world is automatically selected for your current world/dimension, then you should be good to go. If the wrong sub-world is marked with (auto), then please continue reading.</p>\n<p><u><em class=\"bbc\">Using the world map mod?</em></u><br />One of the most common reasons for the wrong sub-world being automatically selected is the incorrect usage of the world map mod in multiplayer (since version 1.6).&nbsp;Make sure you select the correct map for your current world before confirming it. It affects which waypoints are being shown to you. The menu for map selection is on the world map screen, if you click the bottom left icon (unless it's open by default). Consider changing the map selection mode to one of the automatic ones, if it fits the server you're playing on.&nbsp;</p>\n<p>Your currently confirmed map affects which waypoints sub-world in the minimap mod is considered automatic (auto). This allows each created map to have separate waypoints data. At any point in time, you can select and view any non-automatic waypoints sub-world by going to the waypoints menu (press U) and selecting it from top-right dropdown menu. You can make any sub-world automatic for your currently confirmed map by selecting&nbsp;<em class=\"bbc\">Options -&gt; Make Sub-World Auto</em>.</p>\n<p><u><em class=\"bbc\">Not using the world map mod?</u></em>&nbsp;<br />The wrong sub-world being considered (auto) and therefore being automatically selected can be caused by a server plugin changing the behavior of the vanilla compass item and interfering with the mod's \"multiworld\" detection, if you have it enabled (disabled by default). In most such cases the compass item points to your bed instead of the global spawn point. Sleeping in the right bed again should bring everything back for you if sleeping in another one was the cause of the waypoints disappearing.<br />If this happened because of a new plugin being added (and if removing it is not an option for you), then here's how you can restore your waypoints:</p>\n<p>1. Find the right waypoints \"sub-world\" from the top-right dropdown menu in the waypoints screen, it's the one with the waypoints that you need in your current dimension. Perhaps start with overworld?</p>\n<p>2. Use&nbsp;<em>Options -&gt; Make sub-world auto.&nbsp;</em></p>\n<p>3. Do the same for every dimension (enter the dimensions before doing it!).</p>\n<p>4. If it's a simple server with 1 world (no hub/lobby worlds, no minigame worlds, creative plot worlds etc), then also turn off \"Multiworld Detection\" in the same&nbsp;<em>Options&nbsp;</em>menu. You can turn it back on later if you ever need it.<br /><br /><em style=\"font-size: 1.2rem;\">Contact me if you have questions!</em></p>\n\n### How do I add my own entity icons or override the default ones?\n\n<p><strong>First of all, make sure that you actually have the entity icons/heads enabled in the minimap settings -&gt; Entity Radar Categories, whether you put it on \"Always\" or \"Player List\". Just make sure that you know how to display them. Those are the icons that this section is about.</strong></p>\n<p>Starting from version 21.6.0 of the minimap mod you are able to override the default icons displayed for entities with your own sprites/images by making a resource pack (or including the required resources in your own mod). In addition to that, you are also able to add or improve entity type variants support (different variants of the same entity type having different icons) by creating a simple mod of your own or including some code in your existing mod. Although this is usually unnecessary for simpler multi-variant entity types (most entity types).<br />By default, the minimap mod renders all entity icons using the entity renderers/models bound to the entities. This supports all vanilla mobs and some simple bipedal/quadrupedal mobs whose models have easily identifiable head model parts (head, ears, nose etc). If the mod fails to do so, the colored dot is displayed instead.</p>\n<p>The relevant assets directory to the purpose of overriding the icons is&nbsp;<em class=\"bbc\">/assets/xaerominimap/entity/icon/</em>. You can find it inside the minimap mod's jar file and extract the contained files/folders for your reference. The jar contains an example file for overriding icons of a mod entity type&nbsp;<em class=\"bbc\">/assets/xaerominimap/entity/icon/definition/example_mod/example_entity.json</em>. The example file would corresponds to the entity type&nbsp;<em class=\"bbc\">example_mod:example_entity</em>, if such existed. Please read the comments in the file&nbsp;<em class=\"bbc\">example_entity.json</em>&nbsp;to learn about its structure, so that you are able to create your own icon definitions similar to it.<br />In addition to that,&nbsp;<em class=\"bbc\">/assets/xaerominimap/entity/icon/definition/minecraft/</em>&nbsp;contains several example files for vanilla entity types that might require additional comments regarding the default entity type variants support. By default, entities have very simple variants support where the entity type variant ID is simply the entity's main texture resource location.<br />If you set the \"<em>debugEntityVariantIds</em>\" option in the minimap config file to true, then the variant IDs of newly encountered entities (variants) will be printed in the chat and the log/console.</p>\n<p>Starting from version 21.8.0 of the minimap mod you are also able to configure your own model-based rendering. Please read the comments in the example file&nbsp;<em>/assets/xaerominimap/entity/icon/definition/example_mod/example_entity.json</em>&nbsp;for all the information you need to know.</p>\n<p>Check out this awesome project by <a href=\"https://www.curseforge.com/members/babybluetit\">babybluetit</a>, if you want to have a look at a real implementation of custom icons or actually use it:&nbsp;<a href=\"https://www.curseforge.com/minecraft/texture-packs/xaeros-minimap-modded-support\">https://www.curseforge.com/minecraft/texture-packs/xaeros-minimap-modded-support</a></p>\n\n### How do I prohibit the use of cave mode and/or entity radar on my server?\n\n<p>To do that, please install the mod on the server side (requires a Forge/Fabric/Quilt server as of writing this). When you start your server, a new \"common\" config file will be created in the server directory (not the world folder), usually in the \"config\" folder. In the config file, you get multiple options that let you disable certain mod features for your players. Changes require a server restart.</p>\n<p>Alternatively, you can include the following strings in a server/system message sent to players (on login), which do the following:</p>\n<p><strong>&sect;f&sect;a&sect;i&sect;r&sect;x&sect;a&sect;e&sect;r&sect;o</strong> - causes the minimap to disable cave mode (including world map) and entity radar (fair-play mode)</p>\n<p><strong>&sect;x&sect;a&sect;e&sect;r&sect;o&sect;w&sect;m&sect;n&sect;e&sect;t&sect;h&sect;e&sect;r&sect;i&sect;s&sect;f&sect;a&sect;i&sect;r</strong> - causes world map cave mode to work in the nether despite the minimap being fair-play</p>\n<p><strong>&sect;n&sect;o&sect;m&sect;i&sect;n&sect;i&sect;m&sect;a&sect;p</strong> - disables the minimap completely</p>\n<p><strong>&sect;r&sect;e&sect;s&sect;e&sect;t&sect;x&sect;a&sect;e&sect;r&sect;o</strong> - reset all the system message flags</p>\n\n### How do I let vanilla client players join my server that has the mod installed?\n\n<p>Look for a \"common\" config file directly in your server directory (not the world folder). In the config file, you get multiple options that let you disable certain mod features for your players. One of the options lets you disable the registration of status effects, which is what prevents players from joining. Changes require a server restart.</p>\n<p><span style=\"color: #800080;\"><em><strong>You are allowed to make videos using this mod.</strong></em></span></p>\n<p><span style=\"color: #800080;\"><em><strong>Using it in your modpacks is allowed with the following conditions: </strong></em></span></p>\n<ul>\n<li><span style=\"color: #800080;\"><em><strong><b><i>Only monetization of the modpack through CurseForge or Modrinth is allowed (which includes sponsored links/banners in your modpack description), unless I have given you written permission to monetize it elsewhere. Feel free to private message me about it. I'm more likely to give you permission than not.</i></b></strong></em></span></li>\n<li><span style=\"color: #800080;\"><em><strong>If the modpack is distributed outside of CurseForge and Modrinth, then you must credit me by providing an easily accessible link to one of my official pages for the mod, to the users outside of CurseForge and Modrinth.&nbsp;</strong></em></span></li>\n<li><span style=\"color: #800080;\"><em><strong>The name and/or description of the modpack must not be easily confused with the names/descriptions of my mods.</strong></em></span></li>\n<li><span style=\"color: #800080;\"><em><strong>If I have given you written permission to monetize the modpack outside of CurseForge and Modrinth, then, if I request you to, you must remove monetization outside of CurseForge and Modrinth.&nbsp;</strong></em></span></li>\n</ul>", "published": "2023-04-21T18:24:45.476148Z", "updated": "2025-06-18T10:21:15.855611Z", "status": "approved", "license": {"id": "LicenseRef-All-Rights-Reserved", "name": ""}, "client_side": "required", "server_side": "unsupported", "categories": ["adventure", "transportation", "utility"], "loaders": ["fabric", "forge", "neoforge", "quilt"], "icon_url": "https://cdn.modrinth.com/data/1bokaNcj/354080f65407e49f486fcf9c4580e82c45ae63b8_96.webp", "issues_url": "https://curseforge.com/minecraft/mc-mods/xaeros-minimap/issues", "donation_urls": [{"id": "patreon", "platform": "Patreon", "url": "https://www.patreon.com/xaero96"}], "gallery": []}, "modrinthVersion": {"id": "BKURGnp1", "project_id": "1bokaNcj", "author_id": "ANNqL3WC", "name": "25.2.6", "version_number": "25.2.6_Fabric_1.21", "changelog": "[Read changelogs](https://chocolateminecraft.com/update.php?mod_id=0)", "date_published": "2025-05-10T07:49:21.900363Z", "version_type": "release", "files": [{"hashes": {"sha1": "36ec2f79b89539e82b430f550fd12731c5bef4b2", "sha512": "e5bd4dfde9746c9a233a7da923d88f8cf37c9c42eae181d82b9df1756e2d4a5fa0d4a64b7ccbc22d9431e4d924a10b23f522e01aaf915c1c36310b64903573b5"}, "url": "https://cdn.modrinth.com/data/1bokaNcj/versions/BKURGnp1/Xaeros_Minimap_25.2.6_Fabric_1.21.jar", "filename": "Xaeros_Minimap_25.2.6_Fabric_1.21.jar", "primary": true, "size": 1741254}], "dependencies": [{"project_id": "gF3BGWvG", "dependency_type": "optional"}, {"project_id": "P7dR8mSH", "dependency_type": "required"}], "game_versions": ["1.21", "1.21.1"], "loaders": ["fabric", "quilt"]}}, {"name": "Fabric API", "version": "[1.21.1] Fabric API 0.116.4+1.21.1", "optional": true, "file": "fabric-api-0.116.4+1.21.1.jar", "type": "mods", "description": "Core API library for the Fabric toolchain", "disabled": false, "userAdded": true, "wasSelected": true, "skipped": false, "curseForgeProjectId": 306612, "curseForgeFileId": 6701041, "curseForgeProject": {"id": 306612, "name": "Fabric API", "authors": [{"id": 9548223, "name": "modmuss50", "url": "https://www.curseforge.com/members/modmuss50"}, {"id": 7025176, "name": "sfPlayer1", "url": "https://www.curseforge.com/members/sfplayer1"}], "gameId": 432, "summary": "Core API library for the Fabric toolchain", "categories": [{"name": "API and Library", "slug": "library-api", "url": "https://www.curseforge.com/minecraft/mc-mods/library-api", "dateModified": "2014-05-23T03:21:44.06Z", "gameId": 432, "isClass": false, "id": 421, "iconUrl": "https://media.forgecdn.net/avatars/6/36/635351496947765531.png", "parentCategoryId": 6, "classId": 6}], "status": 4, "primaryCategoryId": 421, "classId": 6, "slug": "fabric-api", "isFeatured": false, "dateModified": "2025-07-09T17:24:16.867Z", "dateCreated": "2018-11-08T17:59:56.667Z", "dateReleased": "2025-07-09T17:16:42.093Z", "links": {"websiteUrl": "https://www.curseforge.com/minecraft/mc-mods/fabric-api", "wikiUrl": "https://fabricmc.net/wiki/", "issuesUrl": "https://github.com/FabricMC/fabric/issues", "sourceUrl": "https://github.com/FabricMC/fabric"}, "logo": {"id": 185822, "description": "", "thumbnailUrl": "https://media.forgecdn.net/avatars/thumbnails/185/822/256/256/636829723898798601.png", "title": "636829723898798601.png", "url": "https://media.forgecdn.net/avatars/185/822/636829723898798601.png", "modId": 306612}, "allowModDistribution": true, "screenshots": [], "mainFileId": 6749190}, "curseForgeFile": {"id": 6701041, "gameId": 432, "isAvailable": true, "displayName": "[1.21.1] Fabric API 0.116.4+1.21.1", "fileName": "fabric-api-0.116.4+1.21.1.jar", "releaseType": 1, "fileStatus": 4, "fileDate": "2025-06-27T12:09:11.13Z", "fileLength": 2423497, "dependencies": [], "alternateFileId": 0, "modules": [{"fingerprint": **********, "name": "META-INF"}, {"fingerprint": **********, "name": "LICENSE-fabric-api"}, {"fingerprint": 860504683, "name": "assets"}, {"fingerprint": **********, "name": "fabric.mod.json"}], "isServerPack": false, "hashes": [{"value": "39d1a6b6213d012649821ecc516ed5b5142478ef", "algo": 1}, {"value": "aa1660d1af25eb18790182753f977615", "algo": 2}], "sortableGameVersions": [{"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-09-01T00:00:00Z", "gameVersionName": "<PERSON><PERSON><PERSON>"}, {"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.1", "gameVersionReleaseDate": "2024-08-08T15:17:53.787Z", "gameVersionName": "1.21.1"}], "gameVersions": ["<PERSON><PERSON><PERSON>", "1.21.1"], "fileFingerprint": **********, "modId": 306612}, "modrinthProject": {"id": "P7dR8mSH", "slug": "fabric-api", "project_type": "mod", "team": "BZoBsPo6", "title": "Fabric API", "description": "Lightweight and modular API providing common hooks and intercompatibility measures utilized by mods using the Fabric toolchain.", "body": "# Fabric API\n\nEssential hooks for modding with Fabric.\n\nFabric API is the library for essential hooks and interoperability mechanisms for Fabric mods. Examples include:\n\n- Exposing functionality that is useful but difficult to access for many mods such as particles, biomes and dimensions\n- Adding events, hooks and APIs to improve interoperability between mods.\n- Essential features such as registry synchronization and adding information to crash reports.\n- An advanced rendering API designed for compatibility with optimization mods and graphics overhaul mods.\n\nAlso check out [Fabric Loader](https://github.com/FabricMC/fabric-loader), the (mostly) version-independent mod loader that powers Fabric. Fabric API is a mod like any other Fabric mod which requires Fabric Loader to be installed.\n\nFor support and discussion for both developers and users, visit [the Fabric Discord server](https://discord.gg/v6v4pMv).\n\n## Using Fabric API to play with mods\n\nMake sure you have installed fabric loader first. More information about installing Fabric Loader can be found [here](https://fabricmc.net/use/).\n\nThe downloaded jar file should be placed in your `mods` folder.", "published": "2021-01-22T11:04:41.419169Z", "updated": "2025-07-09T17:16:46.963438Z", "status": "approved", "license": {"id": "Apache-2.0", "name": "Apache License 2.0"}, "client_side": "optional", "server_side": "optional", "categories": ["library"], "loaders": ["fabric"], "icon_url": "https://cdn.modrinth.com/data/P7dR8mSH/icon.png", "issues_url": "https://github.com/FabricMC/fabric/issues", "source_url": "https://github.com/FabricMC/fabric", "wiki_url": "https://fabricmc.net/wiki/", "discord_url": "https://discord.gg/v6v4pMv", "donation_urls": [], "gallery": []}, "modrinthVersion": {"id": "19viawBV", "project_id": "P7dR8mSH", "author_id": "JZA4dW8o", "name": "[1.21.1] Fabric API 0.116.4+1.21.1", "version_number": "0.116.4+1.21.1", "changelog": "- Bump version (modmuss50)\n- [1.21.1] ArmorRenderer: Add option to disable default head item rendering (#4726) (Juuz)\n- [1.21.1] Fix FabricRecipeProvider.getRecipeIdentifier not applying to recipe advancements (#4724) (<PERSON>uz)\n", "date_published": "2025-06-27T12:09:19.269119Z", "version_type": "release", "files": [{"hashes": {"sha512": "95060def77b54ba6c6eb5af45b478ec6926f33f145d8f3933cd90cb2b6ab6913459d3d0afa3afcb01fca07d714628414a41ee8b6845261e88d92b83bfc668ad8", "sha1": "39d1a6b6213d012649821ecc516ed5b5142478ef"}, "url": "https://cdn.modrinth.com/data/P7dR8mSH/versions/19viawBV/fabric-api-0.116.4%2B1.21.1.jar", "filename": "fabric-api-0.116.4+1.21.1.jar", "primary": true, "size": 2423497}], "dependencies": [], "game_versions": ["1.21.1"], "loaders": ["fabric"]}}, {"name": "In-Game Account <PERSON><PERSON><PERSON>", "version": "IAS 9.0.4 (for Fabric/Quilt 1.21.1)", "optional": true, "file": "IAS-Fabric-1.21.1-9.0.4.jar", "type": "mods", "description": "This mod allows you to change your logged in account in-game, without restarting Minecraft", "disabled": false, "userAdded": true, "wasSelected": true, "skipped": false, "modrinthProject": {"id": "cudtvDnd", "slug": "in-game-account-switcher", "project_type": "mod", "team": "C6hAkScZ", "title": "In-Game Account <PERSON><PERSON><PERSON>", "description": "This mod allows you to change your logged in account in-game, without restarting Minecraft", "body": "# In-Game Account Switcher\n\nIn-Game Account Switcher allows you to change which account you are logged in to in-game,\nwithout having to restart Minecraft.\n\n## Dependencies\n\n**Fabric**: [Fabric API](https://modrinth.com/mod/fabric-api) (Required),\n[Mod Menu](https://modrinth.com/mod/modmenu) (Optional)  \n**Quilt**: [QFAPI/QSL](https://modrinth.com/mod/qsl) (Required),\n[Mod Menu](https://modrinth.com/mod/modmenu) (Optional)  \n**Forge**: (none)  \n**NeoForge**: (none)\n\n## FAQ\n\n**Q**: I need help, have some questions, or something else.  \n**A**: You can look at the docs for\n[Terms and Privacy](https://github.com/The-Fireplace-Minecraft-Mods/In-Game-Account-Switcher/blob/main/docs/TERMS.md),\n[Crypt](https://github.com/The-Fireplace-Minecraft-Mods/In-Game-Account-Switcher/blob/main/docs/CRYPT.md),\n[Stolen Accounts](https://github.com/The-Fireplace-Minecraft-Mods/In-Game-Account-Switcher/blob/main/docs/STOLEN.md),\n[Common Errors](https://github.com/The-Fireplace-Minecraft-Mods/In-Game-Account-Switcher/blob/main/docs/ERRORS.md),\n[Secure Log Sharing](https://github.com/The-Fireplace-Minecraft-Mods/In-Game-Account-Switcher/blob/main/docs/LOG_SHARING.md),\nor you can join the [Discord server](https://discord.gg/TpU2nEkSPk).\n\n**Q**: Where can I download this mod?  \n**A**: [Modrinth](https://modrinth.com/mod/in-game-account-switcher),\n[CurseForge](https://www.curseforge.com/minecraft/mc-mods/in-game-account-switcher),\n[GitHub](https://github.com/The-Fireplace-Minecraft-Mods/In-Game-Account-Switcher).\nYou can also find unstable builds at [GitHub Actions](https://github.com/The-Fireplace-Minecraft-Mods/In-Game-Account-Switcher/actions),\nto download them you'll need a GitHub account.\n\n**Q**: Which mod loaders are supported?  \n**A**: Forge, Fabric, NeoForge. Quilt should work too.\n\n**Q**: Which versions are supported?  \n**A**: Minecraft 1.18.2, 1.19.2, 1.19.4, 1.20.1, 1.20.2, 1.20.4, 1.20.6, 1.21.1, 1.21.3, 1.21.4, 1.21.5.\nOld mod versions (for MC 1.8.9, 1.12.2, etc.) are not supported.\n\n**Q**: Do I need Fabric API or Quilt Standard Libraries?  \n**A**: Yes, you'll need Fabric API for Fabric/Quilt. On Quilt, you can install QFAPI/QSL instead.\nObviously, you do NOT need them for Forge or NeoForge.\n\n**Q**: Help, I have shared my account with someone! Someone is using my account via In-Game Account Switcher!  \n**A**: Read the \"[Stolen Accounts](https://github.com/The-Fireplace-Minecraft-Mods/In-Game-Account-Switcher/blob/main/docs/STOLEN.md)\" page.\n\n**Q**: I'm using an old version (for 1.8.9/1.12.2) of the mod and it gives me the \"SSLHandshakeException\" error, how do I fix it?  \n**A**: You need to update your Java 8 to a newer build. You can use any Java vendor that you want, there's little to no difference, popular choices:\nEclipse Temurin ([Windows](https://adoptium.net/temurin/releases/?package=jre&version=8&os=windows),\n[macOS](https://adoptium.net/temurin/releases/?package=jre&version=8&os=mac),\n[Linux](https://adoptium.net/temurin/releases/?package=jre&version=8&os=linux)),\nAzul Zulu ([Windows](https://www.azul.com/downloads/?version=java-8-lts&os=windows&package=jre#zulu),\n[macOS](https://www.azul.com/downloads/?version=java-8-lts&os=macos&package=jre#zulu),\n[Linux](https://www.azul.com/downloads/?version=java-8-lts&os=linux&package=jre#zulu)),\nAmazon Corretto ([Windows](https://docs.aws.amazon.com/corretto/latest/corretto-8-ug/windows-install.html),\n[macOS](https://docs.aws.amazon.com/corretto/latest/corretto-8-ug/macos-install.html),\n[Linux](https://docs.aws.amazon.com/corretto/latest/corretto-8-ug/linux-info.html)).\n\n**Q**: I'm using Feather/Lunar Client and the mod doesn't work properly (the button doesn't appear, the account list is empty, etc), when you will fix this?  \n**A**: These clients break lots of things. Since they are closed-source, we cannot add compatibility with them.\nYou should ask client developers to fix any issues. Many of this clients have their own built-in account switcher, use it instead.\n\n**Q**: Can I import/export my accounts from/into a file? Can I use access tokens/cookies to add Microsoft accounts? Will you add this?  \n**A**: No. This kind of behavior is either shady or prohibited by [Minecraft EULA](https://minecraft.net/eula).\n\n**Q**: Is this mod open source?  \n**A**: [Yes.](https://github.com/The-Fireplace-Minecraft-Mods/In-Game-Account-Switcher) (Licensed\nunder [GNU LGPLv3](https://github.com/The-Fireplace-Minecraft-Mods/In-Game-Account-Switcher/blob/main/LICENSE))\n\n**Q**: I've found a bug.  \n**A**: Report it [here](https://github.com/The-Fireplace-Minecraft-Mods/In-Game-Account-Switcher/issues). If you are\nnot sure if this is a bug, you can join the [Discord](https://discord.gg/TpU2nEkSPk). If you think this bug is\ncritical enough not to be disclosed publicly, please, report it as described in the\n[security policy](https://github.com/The-Fireplace-Minecraft-Mods/In-Game-Account-Switcher/blob/main/SECURITY.md).\n\n## Screenshots\n\n![main menu](https://i.imgur.com/DX06VoG.png)\n![account selector](https://i.imgur.com/5hiQ6Om.png)\n\n## Credits\n\nThanks to the [minecraft.wiki/Microsoft_authentication](https://minecraft.wiki/Microsoft_authentication)\n(previously was on wiki.vg) page contributors for providing useful information about Microsoft authentication.\n\nThanks to the [Methanol developers](https://github.com/mizosoft/methanol) for providing\na cool HTTP client we use to automatically upload 30+ files to Modrinth, CurseForge, and GitHub.\n", "published": "2021-04-25T16:03:01.468727Z", "updated": "2025-07-01T11:36:12.810414Z", "status": "approved", "license": {"id": "LGPL-3.0-or-later", "name": "GNU Lesser General Public License v3.0 only"}, "client_side": "required", "server_side": "unsupported", "categories": ["management", "utility"], "loaders": ["fabric", "forge", "neoforge", "quilt"], "icon_url": "https://cdn.modrinth.com/data/cudtvDnd/fe9928884fecdd1f967ea37817e01ac987d4aa5a_96.webp", "issues_url": "https://github.com/The-Fireplace-Minecraft-Mods/In-Game-Account-Switcher/issues", "source_url": "https://github.com/The-Fireplace-Minecraft-Mods/In-Game-Account-Switcher", "wiki_url": "https://github.com/The-Fireplace-Minecraft-Mods/In-Game-Account-Switcher/tree/main/docs", "discord_url": "https://discord.gg/TpU2nEkSPk", "donation_urls": [{"id": "ko-fi", "platform": "Ko-fi", "url": "https://ko-fi.com/the_fireplace"}], "gallery": []}, "modrinthVersion": {"id": "1I6CbhGl", "project_id": "cudtvDnd", "author_id": "7nbTzIvd", "name": "IAS 9.0.4 (for Fabric/Quilt 1.21.1)", "version_number": "9.0.4", "changelog": "What's new:\n\n- Added 1.21.7 support.\n- Disclaimer I/O is now super fault-tolerant and should not block the ability of saving accounts.\n- Internal code changes.\n\n**Full Changelog**: [v9.0.3...v9.0.4](https://github.com/The-Fireplace-Minecraft-Mods/In-Game-Account-Switcher/compare/v9.0.3...v9.0.4)\n", "date_published": "2025-07-01T11:35:45.585974Z", "version_type": "release", "files": [{"hashes": {"sha512": "13612fa1eb9fccc326768afc017d5c2a750f659f6375e6992bf3d985adee1b5d857cc0870ba1f2c9d18121831a713c4e731d6e303922568ce0ade552f02f175b", "sha1": "165efcc4c01e6c3d0e0a89f3a8d2d71c7225137f"}, "url": "https://cdn.modrinth.com/data/cudtvDnd/versions/1I6CbhGl/IAS-Fabric-1.21.1-9.0.4.jar", "filename": "IAS-Fabric-1.21.1-9.0.4.jar", "primary": true, "size": 305600}], "dependencies": [{"project_id": "P7dR8mSH", "dependency_type": "required"}, {"project_id": "mOgUt4GM", "dependency_type": "optional"}], "game_versions": ["1.21.1"], "loaders": ["fabric", "quilt"]}}, {"name": "Just Enough Items (JEI)", "version": "19.22.0.315 for Fabric 1.21.1", "optional": true, "file": "jei-1.21.1-fabric-19.22.0.315.jar", "type": "mods", "description": "View Items and Recipes", "disabled": false, "userAdded": true, "wasSelected": true, "skipped": false, "curseForgeProjectId": 238222, "curseForgeFileId": 6756164, "curseForgeProject": {"id": 238222, "name": "Just Enough Items (JEI)", "authors": [{"id": 17072262, "name": "mezz", "url": "https://www.curseforge.com/members/mezz"}], "gameId": 432, "summary": "View Items and Recipes", "categories": [{"name": "API and Library", "slug": "library-api", "url": "https://www.curseforge.com/minecraft/mc-mods/library-api", "dateModified": "2014-05-23T03:21:44.06Z", "gameId": 432, "isClass": false, "id": 421, "iconUrl": "https://media.forgecdn.net/avatars/6/36/635351496947765531.png", "parentCategoryId": 6, "classId": 6}, {"name": "Map and Information", "slug": "map-information", "url": "https://www.curseforge.com/minecraft/mc-mods/map-information", "dateModified": "2014-05-08T17:42:23.74Z", "gameId": 432, "isClass": false, "id": 423, "iconUrl": "https://media.forgecdn.net/avatars/6/38/635351497437388438.png", "parentCategoryId": 6, "classId": 6}], "status": 4, "primaryCategoryId": 423, "classId": 6, "slug": "jei", "isFeatured": false, "dateModified": "2025-07-11T13:31:28.017Z", "dateCreated": "2025-07-11T13:21:10.74Z", "dateReleased": "2025-07-11T13:21:10.74Z", "links": {"websiteUrl": "https://www.curseforge.com/minecraft/mc-mods/jei", "wikiUrl": "", "issuesUrl": "https://github.com/mezz/JustEnoughItems/issues?q=is%3Aissue", "sourceUrl": "https://github.com/mezz/JustEnoughItems"}, "logo": {"id": 29069, "description": "", "thumbnailUrl": "https://media.forgecdn.net/avatars/thumbnails/29/69/256/256/635838945588716414.jpeg", "title": "635838945588716414.jpeg", "url": "https://media.forgecdn.net/avatars/29/69/635838945588716414.jpeg", "modId": 238222}, "allowModDistribution": true, "screenshots": [{"id": 31420, "description": "", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/31/420/310/172/tgafkma.png", "title": "Itemlist Edit Mode", "url": "https://media.forgecdn.net/attachments/31/420/tgafkma.png", "modId": 238222}, {"id": 31419, "description": "", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/31/419/310/172/t7f7jh6.png", "title": "Potions", "url": "https://media.forgecdn.net/attachments/31/419/t7f7jh6.png", "modId": 238222}, {"id": 31418, "description": "", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/31/418/310/172/9lngh5f.png", "title": "Big Screen Support", "url": "https://media.forgecdn.net/attachments/31/418/9lngh5f.png", "modId": 238222}, {"id": 31417, "description": "", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/31/417/310/172/thzzdin.png", "title": "Recipe Completion", "url": "https://media.forgecdn.net/attachments/31/417/thzzdin.png", "modId": 238222}], "mainFileId": 6614392}, "curseForgeFile": {"id": 6756164, "gameId": 432, "isAvailable": true, "displayName": "19.22.0.315 for Fabric 1.21.1", "fileName": "jei-1.21.1-fabric-19.22.0.315.jar", "releaseType": 2, "fileStatus": 4, "fileDate": "2025-07-11T13:19:56.183Z", "fileLength": 1494045, "dependencies": [], "alternateFileId": 0, "modules": [{"fingerprint": 2461505705, "name": "META-INF"}, {"fingerprint": 2433937975, "name": "assets"}, {"fingerprint": 2680608334, "name": "fabric.mod.json"}, {"fingerprint": 148796946, "name": "jei-1.21.1-fabric-refmap.json"}, {"fingerprint": 2007185424, "name": "jei-icon.png"}, {"fingerprint": 3252880134, "name": "jei.access<PERSON>ner"}, {"fingerprint": 623332486, "name": "jei.mixins.json"}, {"fingerprint": 2469054851, "name": "mezz"}, {"fingerprint": 2606738017, "name": "pack.mcmeta"}], "isServerPack": false, "hashes": [{"value": "646de7e965fdef755d3c58a2413b3b270ffead66", "algo": 1}, {"value": "7d09379bb6bce21c5fcd935bedc14103", "algo": 2}], "sortableGameVersions": [{"gameVersionPadded": "**********.**********", "gameVersion": "1.21", "gameVersionReleaseDate": "2024-06-14T00:00:00Z", "gameVersionName": "1.21"}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-09-01T00:00:00Z", "gameVersionName": "<PERSON><PERSON><PERSON>"}, {"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.1", "gameVersionReleaseDate": "2024-08-08T15:17:53.787Z", "gameVersionName": "1.21.1"}], "gameVersions": ["1.21", "<PERSON><PERSON><PERSON>", "1.21.1"], "fileFingerprint": 146330362, "modId": 238222}, "modrinthProject": {"id": "u6dRKJwZ", "slug": "jei", "project_type": "mod", "team": "H2zAm1Kt", "title": "Just Enough Items", "description": "JEI - View Items and Recipes", "body": "# Just Enough Items\nJEI is an item and recipe viewing mod for Minecraft, built from the ground up for stability and performance.\n\n![JEI Recipe GUI](https://cdn.modrinth.com/data/u6dRKJwZ/images/ee0ea4051573264d2b4edb53f9d4e634faa11f21.png)\n\n## Controls\n\n### Inventory:\n*(Minecraft Options -> Controls)*\n\nShow **R**ecipes: *Hover over item + R*  \nShow **U**ses: *Hover over item + U*  \nToggle Item List **O**verlay: *Control + O*  \n\n### Item List:\nShow Recipes: *Click Item or type R*  \nShow Uses: *Right-Click Item or type U*  \nNext/Previous page: *Scroll Wheel*  \n\n### Search:\nSelect Search Bar: *Click or Control + F*  \nClear Search: *Right Click*  \nPrevious Search: *Up Arrow * \nSearch by Mod Name: *Type @ in front of a word (@thaum)*  \nMultiple terms work together *(wand @thaum)*  \n\nExclude search terms: *Type - in front of a word (@thaum -wand)*  \n\nSearch terms that have spaces in them: *Type \"\" around the words (\"two words\")*\n\n### Recipe View:\nNext/Previous page: *Scroll Wheel*  \nShow all Recipes: *Click the recipe category's name*  \nMove items into crafting area: *Click the [+] button. Shift-Click to move multiple sets of items.*  \n\n<details>\n<summary>View advanced controls</summary>\n\n### Advanced Search:\nSearch by Tooltip Only: *Type # in front of a word*  \nSearch by OreDict name: *Type $ in front of a word*  \nSearch by Creative Tab: *Type % in front of a word*  \nMultiple Searches: *Separate terms with | characters, like \"wrench|hammer\"*  \n\n### With Cheat Mode Active:\nGet a full stack of Items: *Click Item*  \nGet one Item: *Right-Click Item*  \nDelete Item: *Drop an Item into the Item List*  \n\n### With Item List Edit Mode Active: \n*(These controls are shown in-game on the item tooltip)*\n\nHide Item: *Control-Click Item*  \nHide Items and Sub-Items: *Control-Right-Click Item*  \nHide all Items from a mod: *Control-Shift-Click Item*\n\n</details>\n\n## FAQ\n### Can I include JEI in my mod pack?\nOf course!\n\n### Can JEI run client-only, or with a vanilla server?\nYes, with some features disabled:\n- The \"Move items into crafting area\" [+] buttons.\n- Cheating in items that have lots of NBT data.\n- Deleting items.\n\n### Where can I find the ___ feature from NEI?\n\n[The chart here shows which mods support each feature.](https://docs.google.com/spreadsheets/d/1NEWZjKvWB-SibY1BXc1zoYkK7AhjMMBZess9wMy--6w/edit#gid=0)\n\n### I have an issue / bug / crash / report / request, where can I send it?\n[JEI Github Issues Page](https://github.com/mezz/JustEnoughItems/issues)\n\n## Support\n[Become a patron on Patreon](https://www.patreon.com/mezz)", "published": "2023-01-27T05:19:03.821699Z", "updated": "2025-07-11T13:21:15.169452Z", "status": "approved", "license": {"id": "MIT", "name": "MIT License"}, "client_side": "required", "server_side": "required", "categories": ["library", "utility"], "loaders": ["fabric", "forge", "neoforge"], "icon_url": "https://cdn.modrinth.com/data/u6dRKJwZ/4a3f18ac0d096c9f8e9176984c44be4e58f94c89_96.webp", "issues_url": "https://github.com/mezz/JustEnoughItems/issues?q=is%3Aissue", "source_url": "https://github.com/mezz/JustEnoughItems", "wiki_url": "https://github.com/mezz/JustEnoughItems/wiki", "discord_url": "https://discord.gg/sCQcWU2", "donation_urls": [{"id": "patreon", "platform": "Patreon", "url": "https://www.patreon.com/mezz"}], "gallery": [{"url": "https://cdn.modrinth.com/data/u6dRKJwZ/images/3eeba45a5139933d338cb1b3d93ba341693f9ed8_350.webp", "raw_url": "https://cdn.modrinth.com/data/u6dRKJwZ/images/4ebe4cc9942c4bf0b07fdac67f93026b42f9a4d4.png", "featured": false, "title": "Recipe Completion", "created": "2023-01-27T05:18:47.838148Z", "ordering": 0.0}, {"url": "https://cdn.modrinth.com/data/u6dRKJwZ/images/7e845f89fbd1cece7cbec9474c193d500d34e5d9_350.webp", "raw_url": "https://cdn.modrinth.com/data/u6dRKJwZ/images/ee0ea4051573264d2b4edb53f9d4e634faa11f21.png", "featured": false, "title": "JEI Recipe GUI", "created": "2023-01-27T05:27:31.621057Z", "ordering": 0.0}, {"url": "https://cdn.modrinth.com/data/u6dRKJwZ/images/f281af132332942e0739d557d6608e9263273fbb.jpeg", "raw_url": "https://cdn.modrinth.com/data/u6dRKJwZ/images/f281af132332942e0739d557d6608e9263273fbb.jpeg", "featured": true, "title": "jei-header", "description": "Zoomed image of JEI viewed on a high resolution CRT monitor", "created": "2023-01-29T23:20:13.068911Z", "ordering": 0.0}, {"url": "https://cdn.modrinth.com/data/u6dRKJwZ/images/631c360143228a749271d1b01e8c3d9963f9b495_350.webp", "raw_url": "https://cdn.modrinth.com/data/u6dRKJwZ/images/156edbd1e04f86d64edc4f41a6d00e849bc7984b.png", "featured": false, "title": "Potions", "created": "2023-01-27T05:18:47.838148Z", "ordering": 1.0}, {"url": "https://cdn.modrinth.com/data/u6dRKJwZ/images/8638bee21682cf82d79f8e841816866aeb13c6a6_350.webp", "raw_url": "https://cdn.modrinth.com/data/u6dRKJwZ/images/b9efa7c6ecb817461416e6a30d8dfc0cc01e8566.png", "featured": false, "title": "Itemlist Edit Mode", "created": "2023-01-27T05:18:47.838148Z", "ordering": 2.0}, {"url": "https://cdn.modrinth.com/data/u6dRKJwZ/images/bf2dadec207d0c42818396dd7eefd78fb91748e8_350.webp", "raw_url": "https://cdn.modrinth.com/data/u6dRKJwZ/images/b1d94b5957935666467e1b2073d5cf92993b4078.png", "featured": false, "title": "Big Screen Support", "created": "2023-01-27T05:18:47.838148Z", "ordering": 3.0}]}, "modrinthVersion": {"id": "tL9glYYQ", "project_id": "u6dRKJwZ", "author_id": "nMXRSbhF", "name": "19.22.0.315 for Fabric 1.21.1", "version_number": "19.22.0.315", "changelog": "  * [Fix fluidhelper](https://github.com/mezz/JustEnoughItems/commit/09c73b379ff0baae1750e1eb733bc98df905fc05) - mezz\n  * [Merge remote-tracking branch 'origin/1.21.x' into 1.21.1](https://github.com/mezz/JustEnoughItems/commit/d4a9bfdb9c8264470561c7e85d096cb28e44568c) - mezz\n  * [Fix #3931 Simplify tooltip handling and allow modifying rich tooltips](https://github.com/mezz/JustEnoughItems/commit/241349009918bb9ed48f341f78bfe0a86d05ed6f) - mezz\n  * [Update pt_br.json (#3966)](https://github.com/mezz/JustEnoughItems/commit/444790ce4d41f71182c665f74f93e2f8d7e80b24) - Sr.Xlr11\n  * [Update de_de.json (#3923)](https://github.com/mezz/JustEnoughItems/commit/9100f05ab294236cb87e9f74383f1421ee22965d) - <PERSON> Lienau\n  * [Added translation to Spanish (Ecuador) (#3906)](https://github.com/mezz/JustEnoughItems/commit/8d1cf24b9708d5b2ef9a50fc07a3ffbff311d8c8) - wajo21x\n  * [Update zh_cn.json (#3890)](https://github.com/mezz/JustEnoughItems/commit/88f80e05c7a759ac0886efa6baf00cc6e6b76ab3) - YocyCraft\n  * [🌐 add Argentine Spanish localization (#3979)](https://github.com/mezz/JustEnoughItems/commit/bba82778ea5b924421da5bf047307b9cee8f27bb) - Texaliuz\n  * [reduce CurseForge changelog length](https://github.com/mezz/JustEnoughItems/commit/d4ea796eb319efff2ff209f50c053c2a5a1dec05) - mezz\n  * [Improve compatibility of LimitedQuadItemModel with platform-specific rendering methods](https://github.com/mezz/JustEnoughItems/commit/401bb43d3131627f88386f882b47a257d35ef233) - mezz\n", "date_published": "2025-07-11T13:20:09.691206Z", "version_type": "beta", "files": [{"hashes": {"sha1": "646de7e965fdef755d3c58a2413b3b270ffead66", "sha512": "76c662ef53f7eff15ecfe95c64578d2c66d3a904b1b1207a27e20a343a83709afb3a78428be199bc4c5a6b725bbd1b444769c8b0356d29bf82a229c9672f5b95"}, "url": "https://cdn.modrinth.com/data/u6dRKJwZ/versions/tL9glYYQ/jei-1.21.1-fabric-19.22.0.315.jar", "filename": "jei-1.21.1-fabric-19.22.0.315.jar", "primary": true, "size": 1494045}], "dependencies": [], "game_versions": ["1.21", "1.21.1"], "loaders": ["fabric"]}}, {"name": "Sodium", "version": "Sodium 0.5.11", "optional": true, "file": "sodium-fabric-0.5.11+mc1.21.jar", "type": "mods", "description": "The fastest and most compatible rendering optimization mod for Minecraft. Now available for both NeoForge and Fabric!", "disabled": false, "userAdded": true, "wasSelected": true, "skipped": false, "curseForgeProjectId": 394468, "curseForgeFileId": 5485657, "curseForgeProject": {"id": 394468, "name": "Sodium", "authors": [{"id": 28746583, "name": "JellySquid", "url": "https://www.curseforge.com/members/jellysquid"}], "gameId": 432, "summary": "The fastest and most compatible rendering optimization mod for Minecraft. Now available for both NeoForge and Fabric!", "categories": [{"name": "Performance", "slug": "performance", "url": "https://www.curseforge.com/minecraft/mc-mods/performance", "dateModified": "2024-01-16T06:56:01.057Z", "gameId": 432, "isClass": false, "id": 6814, "iconUrl": "https://media.forgecdn.net/avatars/933/987/638409849610531091.png", "parentCategoryId": 6, "classId": 6}], "status": 4, "primaryCategoryId": 6814, "classId": 6, "slug": "sodium", "isFeatured": false, "dateModified": "2025-06-18T00:10:37.35Z", "dateCreated": "2020-07-08T23:18:29.833Z", "dateReleased": "2025-06-17T16:01:23.28Z", "links": {"websiteUrl": "https://www.curseforge.com/minecraft/mc-mods/sodium", "wikiUrl": "https://github.com/CaffeineMC/sodium-fabric/wiki", "issuesUrl": "https://github.com/CaffeineMC/sodium-fabric/issues", "sourceUrl": "https://github.com/CaffeineMC/sodium"}, "logo": {"id": 284773, "description": "", "thumbnailUrl": "https://media.forgecdn.net/avatars/thumbnails/284/773/256/256/637298471098686391.png", "title": "637298471098686391.png", "url": "https://media.forgecdn.net/avatars/284/773/637298471098686391.png", "modId": 394468}, "allowModDistribution": true, "screenshots": [{"id": 871369, "description": "Sodium greatly improves the shading of blocks, fixing a number of bugs while making block shadows look more smooth and consistent.", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/871/369/310/172/block-shading.webp", "title": "Block Shading Improvements", "url": "https://media.forgecdn.net/attachments/871/369/block-shading.webp", "modId": 394468}, {"id": 871368, "description": "Sodium implements smooth lighting for all fluids, fixing a long-standing issue in Minecraft.", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/871/368/310/172/fluid-rendering.webp", "title": "Fluid Rendering Improvements", "url": "https://media.forgecdn.net/attachments/871/368/fluid-rendering.webp", "modId": 394468}, {"id": 871367, "description": "Sodium improves biome blending to use gradients across each block, making the effect look much smoother.", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/871/367/310/172/biome-blending.webp", "title": "Biome Blending Improvements", "url": "https://media.forgecdn.net/attachments/871/367/biome-blending.webp", "modId": 394468}, {"id": 871366, "description": "Sodium fixes many graphical issues with smooth lighting while underwater.", "thumbnailUrl": "https://media.forgecdn.net/attachments/thumbnails/871/366/310/172/underwater-lighting.webp", "title": "Underwater Lighting Improvements", "url": "https://media.forgecdn.net/attachments/871/366/underwater-lighting.webp", "modId": 394468}], "mainFileId": 6382663}, "curseForgeFile": {"id": 5485657, "gameId": 432, "isAvailable": true, "displayName": "Sodium 0.5.11", "fileName": "sodium-fabric-0.5.11+mc1.21.jar", "releaseType": 1, "fileStatus": 4, "fileDate": "2024-06-29T14:32:16.563Z", "fileLength": 982671, "dependencies": [], "alternateFileId": 0, "modules": [{"fingerprint": 2695723518, "name": "META-INF"}, {"fingerprint": **********, "name": "COPYING"}, {"fingerprint": **********, "name": "COPYING.LESSER"}, {"fingerprint": 492796272, "name": "assets"}, {"fingerprint": 905910115, "name": "fabric.mod.json"}, {"fingerprint": 3682770097, "name": "me"}, {"fingerprint": 69356713, "name": "net"}, {"fingerprint": 1620673151, "name": "programmer_art"}, {"fingerprint": 431611011, "name": "sodium.accesswidener"}, {"fingerprint": 663415632, "name": "sodium.mixins.json"}], "isServerPack": false, "hashes": [{"value": "d67e66ea4bb2409997b636dae4203d33764cdcc8", "algo": 1}, {"value": "6c1f78cb7d596f5e4e50725bab6b7989", "algo": 2}], "sortableGameVersions": [{"gameVersionPadded": "**********.**********", "gameVersion": "1.21", "gameVersionReleaseDate": "2024-06-14T00:00:00Z", "gameVersionName": "1.21"}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-09-01T00:00:00Z", "gameVersionName": "<PERSON><PERSON><PERSON>"}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-12-08T00:00:00Z", "gameVersionName": "Client"}, {"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.1", "gameVersionReleaseDate": "2024-08-08T15:17:53.787Z", "gameVersionName": "1.21.1"}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-05-26T00:00:00Z", "gameVersionName": "Quilt"}], "gameVersions": ["1.21", "<PERSON><PERSON><PERSON>", "Client", "1.21.1", "Quilt"], "fileFingerprint": 251638478, "modId": 394468}, "modrinthProject": {"id": "AANobbMI", "slug": "sodium", "project_type": "mod", "team": "4reLOAKe", "title": "Sodium", "description": "The fastest and most compatible rendering optimization mod for Minecraft. Now available for both NeoForge and Fabric!", "body": "**This mod is the result of thousands of hours of development, and is made possible thanks to players like you.** If you would like to show a token of your appreciation for my work, and help support the development of Sodium in the process, then consider [buying me a coffee](https://caffeinemc.net/donate).\n\n[![Buy me a coffee](https://cdn.modrinth.com/data/cached_images/b888841adf2a7d4d76c5274d634c205ee88e9e20.png)](https://caffeinemc.net/donate)\n[![Join us on Discord](https://cdn.modrinth.com/data/cached_images/de7f2f606702569cd98fc1f6a0b29dbf817dd870.png)](https://caffeinemc.net/discord)\n\n---\n\n<center>\n <img src=\"https://cdn.modrinth.com/data/AANobbMI/images/5ca2e8f6c72f3d543dd3dc95b706964c5678d80e.png\" alt=\"Sodium 0.4 Comparison\"></img>\n <p>\n   <!-- This paragraph is load bearing, and ensures a margin is added before the following Markdown -->\n </p>\n</center>\n\nSodium is a powerful optimization mod for the Minecraft client, which greatly improves frame rates and micro-stutter, while fixing many graphical issues in Minecraft.\n\nUnlike other rendering-focused mods, it boasts wide compatibility with the wider modding ecosystem. And it does so without compromising on how the game looks, giving you that authentic block game feel.\n\n[![Available on Fabric](https://cdn.modrinth.com/data/cached_images/513035d1231a86dff137c74e04a5e64633593f10.png)](https://modrinth.com/mod/sodium/versions?l=fabric)\n[![Available on Quilt](https://cdn.modrinth.com/data/cached_images/a9ee492fd1a5fcbcc14d4105aa27be63f2518529.png)](https://modrinth.com/mod/sodium/versions?l=quilt)\n[![Available on NeoForge](https://cdn.modrinth.com/data/cached_images/b9ae07fbc26c08220b4856cb244f86904d0c172c.png)](https://modrinth.com/mod/sodium/versions?l=neoforge)\n\n<h1 id=\"installation\">📥 Installation Guide</h1>\n\nSodium is available for multiple mod loaders, but we recommend using [Fabric Loader](https://fabricmc.net/use) for the most simple and lightweight experience. If you are using a third-party launcher, it probably has built-in functionality to automatically install mod loaders. Once you have installed your mod loader, you can simply place Sodium into your _mods folder_ like anything else.\n\n**Not sure if you installed the mod correctly?** Check your _Video Settings_ screen in Minecraft, which should show our new and improved user interface for changing settings.\n\n**Not sure how to configure the mod?** Don't worry, you probably don't need to. By default, Sodium will enable all optimizations which are supported on your system, giving you the best experience possible. You should generally only change video settings related to performance and other advanced features if you are experiencing issues.\n\n**Need even more performance?** By design, Sodium only optimizes the rendering code of Minecraft. You should also install our other mods, such as [Lithium](https://modrinth.com/mod/lithium) (for MC 1.15+) and [Phosphor](https://modrinth.com/mod/phosphor) (for MC <1.19), to optimize the other parts of your game. This is done so that players can pick and choose which mods they want to use, but we generally recommend using [our entire collection](https://modrinth.com/user/jellysquid3).\n\n<h1 id=\"hardware-compatibility\">✅ Hardware Compatibility</h1>\n\nWe only provide official support for graphics cards which have up-to-date drivers that are compatible with OpenGL 4.5 or newer. Most graphics cards released in the past 12 years will meet these requirements, including the following:\n\n- INTEL HD Graphics 500 Series (Skylake) or newer\n- NVIDIA GeForce 400 Series (Fermi) or newer\n- AMD Radeon HD 7000 Series (GCN 1) or newer\n\n**Nearly all graphics cards that are already compatible with Minecraft (which requires OpenGL 3.3) should also work with Sodium.** But our team cannot ensure compatibility or provide support for older graphics cards, and they may not work with future versions of Sodium.\n\n**If you are running into problems, you should [make sure that your graphics drivers are up-to-date](https://github.com/CaffeineMC/sodium/wiki/Driver-Compatibility).** Out-of-date graphics drivers are often the reason for most crashes, rendering bugs, and performance issues.\n\nAndroid devices (and some Windows-on-ARM devices) which use OpenGL translation layers (such as GL4ES, ANGLE, GLonD3D12 etc) are not supported and will very likely not work with Sodium. These translation layers do not implement required functionality and they suffer from underlying driver bugs which cannot be worked around.\n\n# 🐛 How to Report Issues\n\nPlease use the [issue tracker](https://github.com/CaffeineMC/sodium/issues) linked at the top of the page to report bugs, crashes, and other issues. Make sure you include information about the mods you are using, and attach any crash/log files you have.\n\n---\n\n_**Note:** The level of performance you see while using the Sodium mod will vary depending on exact hardware and software configuration. For all performance comparisons provided on this page, we used a computer with integrated graphics which we believe to be representative of the casual player._\n\n<sup>_**Hardware configuration:** Intel Core i7-1165G7 (4c/8t, up to 4.70 GHz) with Intel Xe Graphics (96 EUs, up to 1.30 GHz), 2x16 GB DDR4-3200._</sup>\n<sup>_**Software configuration:** Fedora 41, Linux 6.12.4, Mesa 24.3.4, Prism Launcher 9.1, OpenJDK 21.0.7 runtime (as packaged by distribution), Fabric Loader 0.16.10._</sup>", "published": "2021-01-03T00:53:34.185936Z", "updated": "2025-06-17T16:00:07.567865Z", "status": "approved", "license": {"id": "LicenseRef-Polyform-Shield-1.0.0", "name": "", "url": "https://github.com/CaffeineMC/sodium/blob/dev/LICENSE.md"}, "client_side": "required", "server_side": "unsupported", "categories": ["optimization"], "loaders": ["fabric", "neoforge", "quilt"], "icon_url": "https://cdn.modrinth.com/data/AANobbMI/295862f4724dc3f78df3447ad6072b2dcd3ef0c9_96.webp", "issues_url": "https://github.com/CaffeineMC/sodium/issues", "source_url": "https://github.com/CaffeineMC/sodium", "wiki_url": "https://github.com/CaffeineMC/sodium/wiki", "discord_url": "https://caffeinemc.net/discord", "donation_urls": [{"id": "ko-fi", "platform": "Ko-fi", "url": "https://caffeinemc.net/donate"}], "gallery": [{"url": "https://cdn.modrinth.com/data/AANobbMI/images/d84313e6f57dc9e7896961dbd2dfc2689d482758_350.webp", "raw_url": "https://cdn.modrinth.com/data/AANobbMI/images/d84313e6f57dc9e7896961dbd2dfc2689d482758.webp", "featured": false, "title": "Underwater Lighting Improvements", "description": "Sodium fixes many graphical issues with smooth lighting while underwater.", "created": "2023-06-25T21:35:39.891789Z", "ordering": 0.0}, {"url": "https://cdn.modrinth.com/data/AANobbMI/images/6b0e58705156ba67a6d97a74b9f9ac05da69f502_350.webp", "raw_url": "https://cdn.modrinth.com/data/AANobbMI/images/6b0e58705156ba67a6d97a74b9f9ac05da69f502.webp", "featured": false, "title": "Biome Blending Improvements", "description": "Sodium improves biome blending to use gradients across each block, making the effect look much smoother.", "created": "2023-06-25T21:32:36.805733Z", "ordering": 1.0}, {"url": "https://cdn.modrinth.com/data/AANobbMI/images/b681a9e87daa53a0e85336a894db70427007149b_350.webp", "raw_url": "https://cdn.modrinth.com/data/AANobbMI/images/b681a9e87daa53a0e85336a894db70427007149b.webp", "featured": false, "title": "Fluid Rendering Improvements", "description": "Sodium implements smooth lighting for all fluids, fixing a long-standing issue in Minecraft.", "created": "2023-06-25T21:34:46.764479Z", "ordering": 10.0}, {"url": "https://cdn.modrinth.com/data/AANobbMI/images/56c4599efcb529a19aed0e118caad4178d7fd540.webp", "raw_url": "https://cdn.modrinth.com/data/AANobbMI/images/56c4599efcb529a19aed0e118caad4178d7fd540.webp", "featured": false, "title": "Block Shading Improvements", "description": "Sodium greatly improves the shading of blocks, fixing a number of bugs while making block shadows look more smooth and consistent.", "created": "2023-06-25T21:36:48.985787Z", "ordering": 30.0}, {"url": "https://cdn.modrinth.com/data/AANobbMI/images/5ca2e8f6c72f3d543dd3dc95b706964c5678d80e_350.webp", "raw_url": "https://cdn.modrinth.com/data/AANobbMI/images/5ca2e8f6c72f3d543dd3dc95b706964c5678d80e.png", "featured": false, "title": "Sodium 0.5.2", "description": "Sodium greatly optimizes the game's renderer to be faster and more efficient, which can often result in FPS improvements of 300% or more.", "created": "2023-08-19T17:00:08.788447Z", "ordering": 999.0}, {"url": "https://cdn.modrinth.com/data/AANobbMI/images/c39debea7fc623306e9c45148084585399fc59ee.webp", "raw_url": "https://cdn.modrinth.com/data/AANobbMI/images/c39debea7fc623306e9c45148084585399fc59ee.webp", "featured": false, "title": "Sodium 0.4.1", "description": "Sodium greatly optimizes the game's renderer to be faster and more efficient, which can often result in FPS improvements of 300% or more.", "created": "2023-06-25T21:30:34.663388Z", "ordering": 1000.0}]}, "modrinthVersion": {"id": "RncWhTxD", "project_id": "AANobbMI", "author_id": "DzLrfrbK", "name": "Sodium 0.5.11", "version_number": "mc1.21-0.5.11", "changelog": "This is a minor release of Sodium, further fixing driver checks, adding minor fixes for fluid rendering, and fixing a crash when using specific resource packs that changed the clouds texture.", "date_published": "2024-06-29T13:45:49.041594Z", "version_type": "release", "files": [{"hashes": {"sha1": "d67e66ea4bb2409997b636dae4203d33764cdcc8", "sha512": "a8a4520c2891e6a3c891b0b5c1106c29851a97c8e5a0131fe94cdcdfd755b679ce55dbaeafd14638ef7987b8efcd32619209aba4460762027f1a2ebe121a4046"}, "url": "https://cdn.modrinth.com/data/AANobbMI/versions/RncWhTxD/sodium-fabric-0.5.11%2Bmc1.21.jar", "filename": "sodium-fabric-0.5.11+mc1.21.jar", "primary": true, "size": 982671}], "dependencies": [], "game_versions": ["1.21", "1.21.1"], "loaders": ["fabric", "quilt"]}}, {"name": "Iris <PERSON>rs", "version": "Iris 1.7.3 for Minecraft 1.21", "optional": true, "file": "iris-1.7.3+mc1.21.jar", "type": "mods", "description": "A modern shaders mod for Minecraft intended to be compatible with existing Optifine shaders", "disabled": false, "userAdded": true, "wasSelected": true, "skipped": false, "curseForgeProjectId": 455508, "curseForgeFileId": 5490877, "curseForgeProject": {"id": 455508, "name": "Iris <PERSON>rs", "authors": [{"id": 44226090, "name": "coderbot", "url": "https://www.curseforge.com/members/coderbot"}, {"id": 101521578, "name": "IMS21", "url": "https://www.curseforge.com/members/ims21"}], "gameId": 432, "summary": "A modern shaders mod for Minecraft intended to be compatible with existing Optifine shaders", "categories": [{"name": "Miscellaneous", "slug": "mc-miscellaneous", "url": "https://www.curseforge.com/minecraft/mc-mods/mc-miscellaneous", "dateModified": "2014-06-15T04:27:14.543Z", "gameId": 432, "isClass": false, "id": 425, "iconUrl": "https://media.forgecdn.net/avatars/6/40/635351497693711265.png", "parentCategoryId": 6, "classId": 6}, {"name": "Cosmetic", "slug": "cosmetic", "url": "https://www.curseforge.com/minecraft/mc-mods/cosmetic", "dateModified": "2014-05-08T17:42:35.597Z", "gameId": 432, "isClass": false, "id": 424, "iconUrl": "https://media.forgecdn.net/avatars/6/39/635351497555976928.png", "parentCategoryId": 6, "classId": 6}], "status": 4, "primaryCategoryId": 425, "classId": 6, "slug": "irisshaders", "isFeatured": false, "dateModified": "2025-07-01T17:59:13.633Z", "dateCreated": "2021-03-10T22:15:48.643Z", "dateReleased": "2025-07-01T17:56:39.173Z", "links": {"websiteUrl": "https://www.curseforge.com/minecraft/mc-mods/irisshaders", "wikiUrl": "https://shaders.properties", "issuesUrl": "https://github.com/IrisShaders/Iris/issues", "sourceUrl": "https://github.com/IrisShaders/Iris"}, "logo": {"id": 1002450, "description": "", "thumbnailUrl": "https://media.forgecdn.net/avatars/thumbnails/1002/450/256/256/638522657871453967.png", "title": "638522657871453967.png", "url": "https://media.forgecdn.net/avatars/1002/450/638522657871453967.png", "modId": 455508}, "allowModDistribution": true, "screenshots": [], "mainFileId": 6717629}, "curseForgeFile": {"id": 5490877, "gameId": 432, "isAvailable": true, "displayName": "Iris 1.7.3 for Minecraft 1.21", "fileName": "iris-1.7.3+mc1.21.jar", "releaseType": 1, "fileStatus": 4, "fileDate": "2024-07-01T00:12:02.457Z", "fileLength": 2707610, "dependencies": [{"fileId": 0, "modId": 394468, "relationType": 3}], "alternateFileId": 0, "modules": [{"fingerprint": **********, "name": "META-INF"}, {"fingerprint": **********, "name": "LICENSE"}, {"fingerprint": **********, "name": "assets"}, {"fingerprint": **********, "name": "centerDepth.fsh"}, {"fingerprint": 462961270, "name": "centerDepth.vsh"}, {"fingerprint": **********, "name": "colorSpace.csh"}, {"fingerprint": **********, "name": "colorSpace.vsh"}, {"fingerprint": **********, "name": "com"}, {"fingerprint": **********, "name": "de"}, {"fingerprint": **********, "name": "fabric.mod.json"}, {"fingerprint": 195414054, "name": "iris-batched-entity-rendering.mixins.json"}, {"fingerprint": 588291438, "name": "iris.accesswidener"}, {"fingerprint": 407547315, "name": "kroppeb"}, {"fingerprint": **********, "name": "mixins.iris.bettermipmaps.json"}, {"fingerprint": **********, "name": "mixins.iris.compat.indigo.json"}, {"fingerprint": **********, "name": "mixins.iris.compat.indium.json"}, {"fingerprint": **********, "name": "mixins.iris.compat.sodium.json"}, {"fingerprint": **********, "name": "mixins.iris.devenvironment.json"}, {"fingerprint": **********, "name": "mixins.iris.fantastic.json"}, {"fingerprint": 3615898153, "name": "mixins.iris.fixes.maxfpscrash.json"}, {"fingerprint": 3729550497, "name": "mixins.iris.integrationtest.json"}, {"fingerprint": 2376067757, "name": "mixins.iris.json"}, {"fingerprint": 3011668326, "name": "mixins.iris.vertexformat.json"}, {"fingerprint": 2778527940, "name": "net"}], "isServerPack": false, "hashes": [{"value": "1b4e44a74c850d407518bc5cbe33eec9577ad9e9", "algo": 1}, {"value": "16c9b91d058d1a4be4e2c3cff5ad8102", "algo": 2}], "sortableGameVersions": [{"gameVersionPadded": "**********.**********", "gameVersion": "1.21", "gameVersionReleaseDate": "2024-06-14T00:00:00Z", "gameVersionName": "1.21"}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-09-01T00:00:00Z", "gameVersionName": "<PERSON><PERSON><PERSON>"}, {"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.1", "gameVersionReleaseDate": "2024-08-08T15:17:53.787Z", "gameVersionName": "1.21.1"}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-05-26T00:00:00Z", "gameVersionName": "Quilt"}], "gameVersions": ["1.21", "<PERSON><PERSON><PERSON>", "1.21.1", "Quilt"], "fileFingerprint": 2833477318, "modId": 455508}, "modrinthProject": {"id": "YL57xq9U", "slug": "iris", "project_type": "mod", "team": "1HMZl6Mn", "title": "Iris <PERSON>rs", "description": "A modern shader pack loader for Minecraft intended to be compatible with existing OptiFine shader packs", "body": "![Iris: An open-source shaders mod compatible with OptiFine shaderpacks](https://raw.githubusercontent.com/IrisShaders/Iris/multiloader-new/docs/banner.png)\n\n## Links\n\n* **Visit [our website](https://irisshaders.dev) for downloads and pretty screenshots!**\n* **Visit [the shaders section](https://modrinth.com/shaders) to find shader packs!**\n* Visit [our Discord server](https://discord.gg/jQJnav2jPu) to chat about the mod and get support! It's also a great place to get development updates right as they're happening.\n* Visit [the developer documentation](https://github.com/IrisShaders/Iris/tree/1.21/docs/development) for information on developing, building, and contributing to Iris!\n\n## Installation\n\nYou can find a guide to installation [here](https://github.com/IrisShaders/Iris/blob/multiloader-new/docs/guide.md).\n\n## FAQ\n\n- Find answers to frequently asked questions on our [FAQ page](https://github.com/IrisShaders/Iris/blob/multiloader-new/docs/faq.md).\n- <PERSON> supports almost all shaderpacks, but a list of unsupported shaderpacks is available [here](https://github.com/IrisShaders/Iris/blob/1.21/docs/unsupportedshaders.md).\n- A list of unfixable limitations in Iris is available [here](https://github.com/IrisShaders/Iris/blob/1.21/docs/usage/limitations.md).\n\n## More Info\n\nMore info can be found on our [README](https://github.com/IrisShaders/Iris/blob/1.21/README.md).", "published": "2021-05-27T05:11:15.690120Z", "updated": "2025-07-01T17:56:05.113596Z", "status": "approved", "license": {"id": "LGPL-3.0-only", "name": "GNU Lesser General Public License v3.0 only"}, "client_side": "required", "server_side": "unsupported", "categories": ["decoration", "optimization"], "loaders": ["fabric", "neoforge", "quilt"], "icon_url": "https://cdn.modrinth.com/data/YL57xq9U/18d0e7f076d3d6ed5bedd472b853909aac5da202_96.webp", "issues_url": "https://github.com/IrisShaders/Iris/issues", "source_url": "https://github.com/IrisShaders/Iris", "discord_url": "https://discord.gg/jQJnav2jPu", "donation_urls": [{"id": "patreon", "platform": "Patreon", "url": "https://www.patreon.com/ims21"}], "gallery": [{"url": "https://cdn.modrinth.com/data/YL57xq9U/images/17f09a3d424d6e2005fcecc81838780a6847f8ce_350.webp", "raw_url": "https://cdn.modrinth.com/data/YL57xq9U/images/d382106b9a2b943d06107c31c139c77849f1a0e8.png", "featured": true, "title": "A nice sunset", "description": "Complementary Shaders 4.4 dev5", "created": "2022-02-28T02:26:00.883959Z", "ordering": 0.0}, {"url": "https://cdn.modrinth.com/data/YL57xq9U/images/44245979cecbf237a3b26f375a0dc4f3a21dcf3d_350.webp", "raw_url": "https://cdn.modrinth.com/data/YL57xq9U/images/2fbd04f5d123577696e29b28bb7981a2bc95a0c8.png", "featured": false, "title": "Wide Mountain", "description": "woodiertexas - Complementary Shaders", "created": "2022-02-28T02:34:59.422420Z", "ordering": 0.0}, {"url": "https://cdn.modrinth.com/data/YL57xq9U/images/d69f13ea9889652c02f8cf91a3e55c8ef7cd6316_350.webp", "raw_url": "https://cdn.modrinth.com/data/YL57xq9U/images/1c3d0dfbedf31dbee4e30e20c5329e5db9a881ae.png", "featured": false, "title": "Crying Obsidian Sky", "description": "Complementary Shaders 4.4 dev3", "created": "2022-02-28T02:35:42.372271Z", "ordering": 0.0}]}, "modrinthVersion": {"id": "kuOV4Ece", "project_id": "YL57xq9U", "author_id": "DzLrfrbK", "name": "Iris 1.7.3 for Minecraft 1.21", "version_number": "1.7.3+1.21", "changelog": "This release fixes a critical bug relating to name tag crashes, as well as a less critical bug related to the dragon death animation.", "date_published": "2024-07-01T00:12:05.988508Z", "version_type": "release", "files": [{"hashes": {"sha512": "bcd9b4f5cef1521c00b59cc9409725556922a077c60f62ba834dca680e29f334660a7c68150ff827779fb888585042b1ba687dfce6fd4d650507d9c59f023703", "sha1": "1b4e44a74c850d407518bc5cbe33eec9577ad9e9"}, "url": "https://cdn.modrinth.com/data/YL57xq9U/versions/kuOV4Ece/iris-1.7.3%2Bmc1.21.jar", "filename": "iris-1.7.3+mc1.21.jar", "primary": true, "size": 2707610}], "dependencies": [{"project_id": "AANobbMI", "dependency_type": "required"}], "game_versions": ["1.21", "1.21.1"], "loaders": ["fabric", "quilt"]}}, {"name": "Carpet PVP", "version": "1.1", "optional": true, "file": "carpet-pvp-1.21-1.2.0+v250515.jar", "type": "mods", "description": "Fork of Carpet Mod that fixes PVP-related bugs with carpet fake players and adds some new commands.", "disabled": false, "userAdded": true, "wasSelected": true, "skipped": false, "modrinthProject": {"id": "9FuEJ7Wg", "slug": "carpet-pvp", "project_type": "mod", "team": "5TL8beRF", "title": "Carpet PVP", "description": "Fork of Carpet Mod that fixes PVP-related bugs with carpet fake players and adds some new commands.", "body": "This mod was made to be used by Theobald's PVP Practice map.\n\nThis mod fixes bugs and quirks with Carpet mod's fake players to be more consistent with real players, and adds extra commands for controlling players through a datapack", "published": "2024-09-10T04:21:49.451895Z", "updated": "2025-05-16T02:12:12.554853Z", "status": "approved", "license": {"id": "MIT", "name": "MIT License"}, "client_side": "unsupported", "server_side": "required", "categories": ["game-mechanics", "minigame", "utility"], "loaders": ["fabric"], "icon_url": "https://cdn.modrinth.com/data/9FuEJ7Wg/9cd92ec7c118575a834a605f476b997e1c534a5a_96.webp", "issues_url": "https://github.com/TheobaldTheBird/CarpetPVP/issues", "source_url": "https://github.com/TheobaldTheBird/CarpetPVP", "discord_url": "https://discord.gg/theobald", "donation_urls": [], "gallery": [{"url": "https://cdn.modrinth.com/data/9FuEJ7Wg/images/2b6bd6cf2aa33afac5bd995a295320821420bfcd_350.webp", "raw_url": "https://cdn.modrinth.com/data/9FuEJ7Wg/images/2b6bd6cf2aa33afac5bd995a295320821420bfcd.png", "featured": false, "title": "Shield knockback fixed", "created": "2024-09-10T08:56:00.986827Z", "ordering": 0.0}, {"url": "https://cdn.modrinth.com/data/9FuEJ7Wg/images/e9e3fbbc78953abf70a7be0e19c4000721556a87_350.webp", "raw_url": "https://cdn.modrinth.com/data/9FuEJ7Wg/images/e9e3fbbc78953abf70a7be0e19c4000721556a87.png", "featured": false, "title": "<PERSON><PERSON> in action", "created": "2024-09-10T08:56:16.133120Z", "ordering": 0.0}]}, "modrinthVersion": {"id": "QM4PZCgy", "project_id": "9FuEJ7Wg", "author_id": "x5ZtItcC", "name": "Carpet PVP 1.2.0", "version_number": "1.2.0", "changelog": "- Fakeplayer no longer disconnects on death, instead respawns where it was originally spawned, making bot death way less annoying to deal with in a datapack\n- Added `/player disconnect` subcommand for the fakeplayer\n- Added Carpet setting for toggling shield stunning! usage: `/carpet shieldStunning <true/false>`\n- Fixed knockback issues (mainly with knockback enchanted swords during fire ticks)\n- Added a poor man's `@s` target selector to the `/player` command. By using `/execute as ... run player s ...`, fake players are now able to target themselves with commands. This removes the need to hard code bot names in datapacks, and allows for controlling multiple players with a single `/execute` command", "date_published": "2025-05-16T02:03:00.501440Z", "version_type": "release", "files": [{"hashes": {"sha512": "5257484bf3439a99886373d1c57218b82498b82d24527ef9f1c23dedd9add1d4cf60cb50cf950a274c011b23ed979ce10d9f1b6445815f2d23f94dc66b4ff1a9", "sha1": "6923d096e11e5e4cec6f713f0c4c260fee747f68"}, "url": "https://cdn.modrinth.com/data/9FuEJ7Wg/versions/QM4PZCgy/carpet-pvp-1.21-1.2.0%2Bv250515.jar", "filename": "carpet-pvp-1.21-1.2.0+v250515.jar", "primary": true, "size": 1541015}], "dependencies": [], "game_versions": ["1.21", "1.21.1"], "loaders": ["fabric"]}}, {"name": "Carpet", "version": "1.4.147+v240613", "optional": true, "file": "fabric-carpet-1.21-1.4.147+v240613.jar", "type": "mods", "description": "Take full control over your vanilla game.", "disabled": false, "userAdded": true, "wasSelected": true, "skipped": false, "curseForgeProjectId": 349239, "curseForgeFileId": 5425253, "curseForgeProject": {"id": 349239, "name": "Carpet", "authors": [{"id": 100600533, "name": "gnembon", "url": "https://www.curseforge.com/members/gnembon"}], "gameId": 432, "summary": "Take full control over your vanilla game.", "categories": [{"name": "API and Library", "slug": "library-api", "url": "https://www.curseforge.com/minecraft/mc-mods/library-api", "dateModified": "2014-05-23T03:21:44.06Z", "gameId": 432, "isClass": false, "id": 421, "iconUrl": "https://media.forgecdn.net/avatars/6/36/635351496947765531.png", "parentCategoryId": 6, "classId": 6}, {"name": "Miscellaneous", "slug": "mc-miscellaneous", "url": "https://www.curseforge.com/minecraft/mc-mods/mc-miscellaneous", "dateModified": "2014-06-15T04:27:14.543Z", "gameId": 432, "isClass": false, "id": 425, "iconUrl": "https://media.forgecdn.net/avatars/6/40/635351497693711265.png", "parentCategoryId": 6, "classId": 6}, {"name": "Server Utility", "slug": "server-utility", "url": "https://www.curseforge.com/minecraft/mc-mods/server-utility", "dateModified": "2014-05-08T17:44:55.057Z", "gameId": 432, "isClass": false, "id": 435, "iconUrl": "https://media.forgecdn.net/avatars/6/48/635351498950580836.png", "parentCategoryId": 6, "classId": 6}, {"name": "Redstone", "slug": "redstone", "url": "https://www.curseforge.com/minecraft/mc-mods/redstone", "dateModified": "2016-01-19T22:21:51.64Z", "gameId": 432, "isClass": false, "id": 4558, "iconUrl": "https://media.forgecdn.net/avatars/32/937/635888173116238506.png", "parentCategoryId": 6, "classId": 6}], "status": 4, "primaryCategoryId": 425, "classId": 6, "slug": "carpet", "isFeatured": false, "dateModified": "2025-06-30T19:41:10.463Z", "dateCreated": "2019-10-25T16:31:19.607Z", "dateReleased": "2025-06-30T19:36:32.38Z", "links": {"websiteUrl": "https://www.curseforge.com/minecraft/mc-mods/carpet", "wikiUrl": "https://github.com/gnembon/fabric-carpet/wiki", "issuesUrl": "https://github.com/gnembon/fabric-carpet/issues", "sourceUrl": "https://github.com/gnembon/fabric-carpet"}, "logo": {"id": 233983, "description": "", "thumbnailUrl": "https://media.forgecdn.net/avatars/thumbnails/233/983/256/256/637075723026900986.png", "title": "637075723026900986.png", "url": "https://media.forgecdn.net/avatars/233/983/637075723026900986.png", "modId": 349239}, "allowModDistribution": true, "screenshots": [], "mainFileId": 6713262}, "curseForgeFile": {"id": 5425253, "gameId": 432, "isAvailable": true, "displayName": "Carpet v1.4.147 for 1.21 and 1.21.1", "fileName": "fabric-carpet-1.21-1.4.147+v240613.jar", "releaseType": 1, "fileStatus": 4, "fileDate": "2024-06-13T20:34:17.273Z", "fileLength": 1525027, "dependencies": [], "alternateFileId": 0, "modules": [{"fingerprint": **********, "name": "META-INF"}, {"fingerprint": **********, "name": "LICENSE_fabric-carpet"}, {"fingerprint": **********, "name": "assets"}, {"fingerprint": **********, "name": "carpet.accesswidener"}, {"fingerprint": **********, "name": "carpet.mixins.json"}, {"fingerprint": **********, "name": "carpet"}, {"fingerprint": **********, "name": "fabric-carpet-refmap.json"}, {"fingerprint": **********, "name": "fabric.mod.json"}], "isServerPack": false, "hashes": [{"value": "54f2907d83e67283d165336f26e5219d4bd36037", "algo": 1}, {"value": "01312e9955e79b21aaecc2caa7681446", "algo": 2}], "sortableGameVersions": [{"gameVersionPadded": "**********.**********", "gameVersion": "1.21", "gameVersionReleaseDate": "2024-06-14T00:00:00Z", "gameVersionName": "1.21"}, {"gameVersionPadded": "0", "gameVersion": "", "gameVersionReleaseDate": "2022-09-01T00:00:00Z", "gameVersionName": "<PERSON><PERSON><PERSON>"}, {"gameVersionPadded": "**********.**********.**********", "gameVersion": "1.21.1", "gameVersionReleaseDate": "2024-08-08T15:17:53.787Z", "gameVersionName": "1.21.1"}], "gameVersions": ["1.21", "<PERSON><PERSON><PERSON>", "1.21.1"], "fileFingerprint": 919074588, "modId": 349239}, "modrinthProject": {"id": "TQTTVgYE", "slug": "carpet", "project_type": "mod", "team": "7pOYs5RN", "title": "Carpet", "description": "Take full control over your vanilla game", "body": "## Carpet\n\nCarpet is a mod for vanilla Minecraft that allows you to take full control of what matters from a technical perspective of the game.\n\n* Test your farms over several hours in only a few minutes using [`/tick warp`](https://github.com/gnembon/fabric-carpet/wiki/Commands#usage-tick-warp-ticks-cmd), as fast as your computer can\n* ...and then see a detailed breakdown of the items they produce using [`hopperCounters`](https://github.com/gnembon/fabric-carpet/wiki/Current-Available-Settings#hoppercounters)\n* See the server mobcap, TPS, etc. update live with [`/log`](https://github.com/gnembon/fabric-carpet/wiki/Commands#log)\n* Let pistons push block entities (ie. chests) with [`movableBlockEntities`](https://github.com/gnembon/fabric-carpet/wiki/Current-Available-Settings#movableblockentities)\n\n## Settings\n\nSee the [List of all currently available settings][settings] on the wiki\n\n[settings]: https://github.com/gnembon/fabric-carpet/wiki/Current-Available-Settings\n\n## Scarpet\n\nCarpet contains Scarpet, a powerful in-game programming language that allows you to easily interact with the game or make powerful apps to further enhance your game.\n\nFind Scarpet apps in [the scarpet repo][scarpet repo] or check the docs to make your own by looking at examples and checking [the docs][scarpet docs]. Carpet also comes with [a few built-in apps][scarpet builtin] that you can load any time.\n\n[scarpet repo]: https://github.com/gnembon/scarpet/tree/master/programs#readme\n[scarpet docs]: https://github.com/gnembon/fabric-carpet/blob/master/docs/scarpet/Documentation.md\n[scarpet builtin]: https://github.com/gnembon/fabric-carpet/wiki/Built-in-Scarpet-apps\n\n## Extensions\n\nThere are also lots of carpet extensions out there, adding countless new rules and functionality! You can find a list of them [in the wiki][extension list].\n\n[extension list]: https://github.com/gnembon/fabric-carpet/wiki/List-of-Carpet-extensions\n\n## Older versions\n\nFor previous Minecraft versions, check the following Github repos:\n* 1.13: [gnembon/carpetmod](https://github.com/gnembon/carpetmod)\n* 1.12: [gnembon/carpetmod112](https://github.com/gnembon/carpetmod112)", "published": "2022-12-29T21:47:34.424756Z", "updated": "2025-07-01T16:55:42.700771Z", "status": "approved", "license": {"id": "MIT", "name": "MIT License"}, "client_side": "unsupported", "server_side": "required", "categories": ["game-mechanics", "utility"], "loaders": ["fabric"], "icon_url": "https://cdn.modrinth.com/data/TQTTVgYE/3ad650635d067b6bfa09403cf5e70e0947a05c07_96.webp", "issues_url": "https://github.com/gnembon/fabric-carpet/issues", "source_url": "https://github.com/gnembon/fabric-carpet", "wiki_url": "https://github.com/gnembon/fabric-carpet/wiki", "discord_url": "https://discord.gg/gn99m4QRY4", "donation_urls": [], "gallery": []}, "modrinthVersion": {"id": "f2mvlGrg", "project_id": "TQTTVgYE", "author_id": "BIC780Hy", "name": "Carpet 1.4.147 for Minecraft 1.21 and 1.21.1", "version_number": "1.4.147", "changelog": "What's new in Carpet 1.4.147:\n - support for Minecraft 1.21 and 1.21.1", "date_published": "2024-09-05T12:58:07.204965Z", "version_type": "release", "files": [{"hashes": {"sha512": "e6f33d13406796a34e7598d997113f25f7bea3e55f9d334b73842adda52b2c5d0a86b7b12ac812d7e758861e3f468bf201c6c710c40162bb79d6818938204151", "sha1": "54f2907d83e67283d165336f26e5219d4bd36037"}, "url": "https://cdn.modrinth.com/data/TQTTVgYE/versions/f2mvlGrg/fabric-carpet-1.21-1.4.147%2Bv240613.jar", "filename": "fabric-carpet-1.21-1.4.147+v240613.jar", "primary": true, "size": 1525027}], "dependencies": [], "game_versions": ["1.21", "1.21.1"], "loaders": ["fabric"]}}], "ignoredUpdates": [], "ignoreAllUpdates": false, "vanillaInstance": true, "lastPlayed": 1752574417971, "numPlays": 49}, "id": "1.21.1", "complianceLevel": 1, "javaVersion": {"component": "java-runtime-delta", "majorVersion": 21}, "arguments": {"game": ["--username", "${auth_player_name}", "--version", "${version_name}", "--gameDir", "${game_directory}", "--assetsDir", "${assets_root}", "--assetIndex", "${assets_index_name}", "--u<PERSON>", "${auth_uuid}", "--accessToken", "${auth_access_token}", "--clientId", "${clientid}", "--xuid", "${auth_xuid}", "--userType", "${user_type}", "--versionType", "${version_type}", {"rules": [{"action": "allow", "features": {}}], "value": "--demo"}, {"rules": [{"action": "allow", "features": {"has_custom_resolution": "true"}}], "value": ["--width", "${resolution_width}", "--height", "${resolution_height}"]}, {"rules": [{"action": "allow", "features": {}}], "value": ["--quick<PERSON><PERSON><PERSON><PERSON>", "${quickPlayPath}"]}, {"rules": [{"action": "allow", "features": {}}], "value": ["--quickPlaySingleplayer", "${quickPlaySingleplayer}"]}, {"rules": [{"action": "allow", "features": {}}], "value": ["--quickPlayMultiplayer", "${quickPlayMultiplayer}"]}, {"rules": [{"action": "allow", "features": {}}], "value": ["--quickPlayRealms", "${quickPlayRealms}"]}], "jvm": [{"rules": [{"action": "allow", "os": {"name": "osx"}}], "value": ["-XstartOnFirstThread"]}, {"rules": [{"action": "allow", "os": {"name": "windows"}}], "value": "-XX:HeapDumpPath=MojangTricksIntelDriversForPerformance_javaw.exe_minecraft.exe.heapdump"}, {"rules": [{"action": "allow", "os": {"arch": "x86"}}], "value": "-Xss1M"}, "-Djava.library.path=${natives_directory}", "-Djna.tmpdir=${natives_directory}", "-Dorg.lwjgl.system.SharedLibraryExtractPath=${natives_directory}", "-Dio.netty.native.workdir=${natives_directory}", "-Dminecraft.launcher.brand=${launcher_name}", "-Dminecraft.launcher.version=${launcher_version}", "-cp", "${classpath}", "-DFabricMcEmu= net.minecraft.client.main.Main "]}, "type": "release", "time": "2024-08-08T12:24:45+00:00", "releaseTime": "2024-08-08T12:24:45+00:00", "minimumLauncherVersion": "21", "assetIndex": {"id": "17", "sha1": "6ce56b86d85d644786eeb9c34c4a1d410ceaef6a", "size": 448666, "url": "https://piston-meta.mojang.com/v1/packages/6ce56b86d85d644786eeb9c34c4a1d410ceaef6a/17.json", "totalSize": 804078056}, "assets": "17", "downloads": {"client": {"sha1": "30c73b1c5da787909b2f73340419fdf13b9def88", "size": 26836906, "url": "https://piston-data.mojang.com/v1/objects/30c73b1c5da787909b2f73340419fdf13b9def88/client.jar"}, "server": {"sha1": "59353fb40c36d304f2035d51e7d6e6baa98dc05c", "size": 51627615, "url": "https://piston-data.mojang.com/v1/objects/59353fb40c36d304f2035d51e7d6e6baa98dc05c/server.jar"}}, "logging": {"client": {"argument": "-Dlog4j.configurationFile=${path}", "file": {"id": "client-1.12.xml", "sha1": "bd65e7d2e3c237be76cfbef4c2405033d7f91521", "size": 888, "url": "https://piston-data.mojang.com/v1/objects/bd65e7d2e3c237be76cfbef4c2405033d7f91521/client-1.12.xml"}, "type": "log4j2-xml"}}, "libraries": [{"name": "org.ow2.asm:asm:9.7.1", "downloads": {"artifact": {"path": "org/ow2/asm/asm/9.7.1/asm-9.7.1.jar", "size": -1, "url": "https://maven.fabricmc.net/org/ow2/asm/asm/9.7.1/asm-9.7.1.jar"}}}, {"name": "org.ow2.asm:asm-analysis:9.7.1", "downloads": {"artifact": {"path": "org/ow2/asm/asm-analysis/9.7.1/asm-analysis-9.7.1.jar", "size": -1, "url": "https://maven.fabricmc.net/org/ow2/asm/asm-analysis/9.7.1/asm-analysis-9.7.1.jar"}}}, {"name": "org.ow2.asm:asm-commons:9.7.1", "downloads": {"artifact": {"path": "org/ow2/asm/asm-commons/9.7.1/asm-commons-9.7.1.jar", "size": -1, "url": "https://maven.fabricmc.net/org/ow2/asm/asm-commons/9.7.1/asm-commons-9.7.1.jar"}}}, {"name": "org.ow2.asm:asm-tree:9.7.1", "downloads": {"artifact": {"path": "org/ow2/asm/asm-tree/9.7.1/asm-tree-9.7.1.jar", "size": -1, "url": "https://maven.fabricmc.net/org/ow2/asm/asm-tree/9.7.1/asm-tree-9.7.1.jar"}}}, {"name": "org.ow2.asm:asm-util:9.7.1", "downloads": {"artifact": {"path": "org/ow2/asm/asm-util/9.7.1/asm-util-9.7.1.jar", "size": -1, "url": "https://maven.fabricmc.net/org/ow2/asm/asm-util/9.7.1/asm-util-9.7.1.jar"}}}, {"name": "net.fabricmc:sponge-mixin:0.15.4+mixin.0.8.7", "downloads": {"artifact": {"path": "net/fabricmc/sponge-mixin/0.15.4+mixin.0.8.7/sponge-mixin-0.15.4+mixin.0.8.7.jar", "size": -1, "url": "https://maven.fabricmc.net/net/fabricmc/sponge-mixin/0.15.4+mixin.0.8.7/sponge-mixin-0.15.4+mixin.0.8.7.jar"}}}, {"name": "net.fabricmc:intermediary:1.21.1", "downloads": {"artifact": {"path": "net/fabricmc/intermediary/1.21.1/intermediary-1.21.1.jar", "size": -1, "url": "https://maven.fabricmc.net/net/fabricmc/intermediary/1.21.1/intermediary-1.21.1.jar"}}}, {"name": "net.fabricmc:fabric-loader:0.16.10", "downloads": {"artifact": {"path": "net/fabricmc/fabric-loader/0.16.10/fabric-loader-0.16.10.jar", "size": -1, "url": "https://maven.fabricmc.net/net/fabricmc/fabric-loader/0.16.10/fabric-loader-0.16.10.jar"}}}, {"name": "ca.weblite:java-objc-bridge:1.1", "downloads": {"artifact": {"path": "ca/weblite/java-objc-bridge/1.1/java-objc-bridge-1.1.jar", "sha1": "1227f9e0666314f9de41477e3ec277e542ed7f7b", "size": 1330045, "url": "https://libraries.minecraft.net/ca/weblite/java-objc-bridge/1.1/java-objc-bridge-1.1.jar"}}, "rules": [{"action": "allow", "os": {"name": "osx"}}]}, {"name": "com.github.oshi:oshi-core:6.4.10", "downloads": {"artifact": {"path": "com/github/oshi/oshi-core/6.4.10/oshi-core-6.4.10.jar", "sha1": "b1d8ab82d11d92fd639b56d639f8f46f739dd5fa", "size": 979212, "url": "https://libraries.minecraft.net/com/github/oshi/oshi-core/6.4.10/oshi-core-6.4.10.jar"}}}, {"name": "com.google.code.gson:gson:2.10.1", "downloads": {"artifact": {"path": "com/google/code/gson/gson/2.10.1/gson-2.10.1.jar", "sha1": "b3add478d4382b78ea20b1671390a858002feb6c", "size": 283367, "url": "https://libraries.minecraft.net/com/google/code/gson/gson/2.10.1/gson-2.10.1.jar"}}}, {"name": "com.google.guava:failureaccess:1.0.1", "downloads": {"artifact": {"path": "com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar", "sha1": "1dcf1de382a0bf95a3d8b0849546c88bac1292c9", "size": 4617, "url": "https://libraries.minecraft.net/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar"}}}, {"name": "com.google.guava:guava:32.1.2-jre", "downloads": {"artifact": {"path": "com/google/guava/guava/32.1.2-jre/guava-32.1.2-jre.jar", "sha1": "5e64ec7e056456bef3a4bc4c6fdaef71e8ab6318", "size": 3041591, "url": "https://libraries.minecraft.net/com/google/guava/guava/32.1.2-jre/guava-32.1.2-jre.jar"}}}, {"name": "com.ibm.icu:icu4j:73.2", "downloads": {"artifact": {"path": "com/ibm/icu/icu4j/73.2/icu4j-73.2.jar", "sha1": "61ad4ef7f9131fcf6d25c34b817f90d6da06c9e9", "size": 14567819, "url": "https://libraries.minecraft.net/com/ibm/icu/icu4j/73.2/icu4j-73.2.jar"}}}, {"name": "com.mojang:authlib:6.0.54", "downloads": {"artifact": {"path": "com/mojang/authlib/6.0.54/authlib-6.0.54.jar", "sha1": "de8bc95660e1b2fe8793fd427a7a10dcec5b3ea7", "size": 115242, "url": "https://libraries.minecraft.net/com/mojang/authlib/6.0.54/authlib-6.0.54.jar"}}}, {"name": "com.mojang:blocklist:1.0.10", "downloads": {"artifact": {"path": "com/mojang/blocklist/1.0.10/blocklist-1.0.10.jar", "sha1": "5c685c5ffa94c4cd39496c7184c1d122e515ecef", "size": 964, "url": "https://libraries.minecraft.net/com/mojang/blocklist/1.0.10/blocklist-1.0.10.jar"}}}, {"name": "com.mojang:brigadier:1.3.10", "downloads": {"artifact": {"path": "com/mojang/brigadier/1.3.10/brigadier-1.3.10.jar", "sha1": "d15b53a14cf20fdcaa98f731af5dda654452c010", "size": 80082, "url": "https://libraries.minecraft.net/com/mojang/brigadier/1.3.10/brigadier-1.3.10.jar"}}}, {"name": "com.mojang:datafixerupper:8.0.16", "downloads": {"artifact": {"path": "com/mojang/datafixerupper/8.0.16/datafixerupper-8.0.16.jar", "sha1": "67d4de6d7f95d89bcf5862995fb854ebaec02a34", "size": 724313, "url": "https://libraries.minecraft.net/com/mojang/datafixerupper/8.0.16/datafixerupper-8.0.16.jar"}}}, {"name": "com.mojang:logging:1.2.7", "downloads": {"artifact": {"path": "com/mojang/logging/1.2.7/logging-1.2.7.jar", "sha1": "24cb95ffb0e3433fd6e844c04e68009e504ca1c0", "size": 15343, "url": "https://libraries.minecraft.net/com/mojang/logging/1.2.7/logging-1.2.7.jar"}}}, {"name": "com.mojang:patchy:2.2.10", "downloads": {"artifact": {"path": "com/mojang/patchy/2.2.10/patchy-2.2.10.jar", "sha1": "da05971b07cbb379d002cf7eaec6a2048211fefc", "size": 4439, "url": "https://libraries.minecraft.net/com/mojang/patchy/2.2.10/patchy-2.2.10.jar"}}}, {"name": "com.mojang:text2speech:1.17.9", "downloads": {"artifact": {"path": "com/mojang/text2speech/1.17.9/text2speech-1.17.9.jar", "sha1": "3cad216e3a7f0c19b4b394388bc9ffc446f13b14", "size": 12243, "url": "https://libraries.minecraft.net/com/mojang/text2speech/1.17.9/text2speech-1.17.9.jar"}}}, {"name": "commons-codec:commons-codec:1.16.0", "downloads": {"artifact": {"path": "commons-codec/commons-codec/1.16.0/commons-codec-1.16.0.jar", "sha1": "4e3eb3d79888d76b54e28b350915b5dc3919c9de", "size": 360738, "url": "https://libraries.minecraft.net/commons-codec/commons-codec/1.16.0/commons-codec-1.16.0.jar"}}}, {"name": "commons-io:commons-io:2.15.1", "downloads": {"artifact": {"path": "commons-io/commons-io/2.15.1/commons-io-2.15.1.jar", "sha1": "f11560da189ab563a5c8e351941415430e9304ea", "size": 501218, "url": "https://libraries.minecraft.net/commons-io/commons-io/2.15.1/commons-io-2.15.1.jar"}}}, {"name": "commons-logging:commons-logging:1.2", "downloads": {"artifact": {"path": "commons-logging/commons-logging/1.2/commons-logging-1.2.jar", "sha1": "4bfc12adfe4842bf07b657f0369c4cb522955686", "size": 61829, "url": "https://libraries.minecraft.net/commons-logging/commons-logging/1.2/commons-logging-1.2.jar"}}}, {"name": "io.netty:netty-buffer:4.1.97.Final", "downloads": {"artifact": {"path": "io/netty/netty-buffer/4.1.97.Final/netty-buffer-4.1.97.Final.jar", "sha1": "f8f3d8644afa5e6e1a40a3a6aeb9d9aa970ecb4f", "size": 306590, "url": "https://libraries.minecraft.net/io/netty/netty-buffer/4.1.97.Final/netty-buffer-4.1.97.Final.jar"}}}, {"name": "io.netty:netty-codec:4.1.97.Final", "downloads": {"artifact": {"path": "io/netty/netty-codec/4.1.97.Final/netty-codec-4.1.97.Final.jar", "sha1": "384ba4d75670befbedb45c4d3b497a93639c206d", "size": 345274, "url": "https://libraries.minecraft.net/io/netty/netty-codec/4.1.97.Final/netty-codec-4.1.97.Final.jar"}}}, {"name": "io.netty:netty-common:4.1.97.Final", "downloads": {"artifact": {"path": "io/netty/netty-common/4.1.97.Final/netty-common-4.1.97.Final.jar", "sha1": "7cceacaf11df8dc63f23d0fb58e9d4640fc88404", "size": 659930, "url": "https://libraries.minecraft.net/io/netty/netty-common/4.1.97.Final/netty-common-4.1.97.Final.jar"}}}, {"name": "io.netty:netty-handler:4.1.97.Final", "downloads": {"artifact": {"path": "io/netty/netty-handler/4.1.97.Final/netty-handler-4.1.97.Final.jar", "sha1": "abb86c6906bf512bf2b797a41cd7d2e8d3cd7c36", "size": 560040, "url": "https://libraries.minecraft.net/io/netty/netty-handler/4.1.97.Final/netty-handler-4.1.97.Final.jar"}}}, {"name": "io.netty:netty-resolver:4.1.97.Final", "downloads": {"artifact": {"path": "io/netty/netty-resolver/4.1.97.Final/netty-resolver-4.1.97.Final.jar", "sha1": "cec8348108dc76c47cf87c669d514be52c922144", "size": 37792, "url": "https://libraries.minecraft.net/io/netty/netty-resolver/4.1.97.Final/netty-resolver-4.1.97.Final.jar"}}}, {"name": "io.netty:netty-transport-classes-epoll:4.1.97.Final", "downloads": {"artifact": {"path": "io/netty/netty-transport-classes-epoll/4.1.97.Final/netty-transport-classes-epoll-4.1.97.Final.jar", "sha1": "795da37ded759e862457a82d9d92c4d39ce8ecee", "size": 147139, "url": "https://libraries.minecraft.net/io/netty/netty-transport-classes-epoll/4.1.97.Final/netty-transport-classes-epoll-4.1.97.Final.jar"}}}, {"name": "io.netty:netty-transport-native-epoll:4.1.97.Final:linux-aarch_64", "downloads": {"artifact": {"path": "io/netty/netty-transport-native-epoll/4.1.97.Final/netty-transport-native-epoll-4.1.97.Final-linux-aarch_64.jar", "sha1": "5514744c588190ffda076b35a9b8c9f24946a960", "size": 40427, "url": "https://libraries.minecraft.net/io/netty/netty-transport-native-epoll/4.1.97.Final/netty-transport-native-epoll-4.1.97.Final-linux-aarch_64.jar"}}, "rules": [{"action": "allow", "os": {"name": "linux"}}]}, {"name": "io.netty:netty-transport-native-epoll:4.1.97.Final:linux-x86_64", "downloads": {"artifact": {"path": "io/netty/netty-transport-native-epoll/4.1.97.Final/netty-transport-native-epoll-4.1.97.Final-linux-x86_64.jar", "sha1": "54188f271e388e7f313aea995e82f58ce2cdb809", "size": 38954, "url": "https://libraries.minecraft.net/io/netty/netty-transport-native-epoll/4.1.97.Final/netty-transport-native-epoll-4.1.97.Final-linux-x86_64.jar"}}, "rules": [{"action": "allow", "os": {"name": "linux"}}]}, {"name": "io.netty:netty-transport-native-unix-common:4.1.97.Final", "downloads": {"artifact": {"path": "io/netty/netty-transport-native-unix-common/4.1.97.Final/netty-transport-native-unix-common-4.1.97.Final.jar", "sha1": "d469d84265ab70095b01b40886cabdd433b6e664", "size": 43897, "url": "https://libraries.minecraft.net/io/netty/netty-transport-native-unix-common/4.1.97.Final/netty-transport-native-unix-common-4.1.97.Final.jar"}}}, {"name": "io.netty:netty-transport:4.1.97.Final", "downloads": {"artifact": {"path": "io/netty/netty-transport/4.1.97.Final/netty-transport-4.1.97.Final.jar", "sha1": "f37380d23c9bb079bc702910833b2fd532c9abd0", "size": 489624, "url": "https://libraries.minecraft.net/io/netty/netty-transport/4.1.97.Final/netty-transport-4.1.97.Final.jar"}}}, {"name": "it.unimi.dsi:fastutil:8.5.12", "downloads": {"artifact": {"path": "it/unimi/dsi/fastutil/8.5.12/fastutil-8.5.12.jar", "sha1": "c24946d46824bd528054bface3231d2ecb7e95e8", "size": 23326598, "url": "https://libraries.minecraft.net/it/unimi/dsi/fastutil/8.5.12/fastutil-8.5.12.jar"}}}, {"name": "net.java.dev.jna:jna-platform:5.14.0", "downloads": {"artifact": {"path": "net/java/dev/jna/jna-platform/5.14.0/jna-platform-5.14.0.jar", "sha1": "28934d48aed814f11e4c584da55c49fa7032b31b", "size": 1369287, "url": "https://libraries.minecraft.net/net/java/dev/jna/jna-platform/5.14.0/jna-platform-5.14.0.jar"}}}, {"name": "net.java.dev.jna:jna:5.14.0", "downloads": {"artifact": {"path": "net/java/dev/jna/jna/5.14.0/jna-5.14.0.jar", "sha1": "67bf3eaea4f0718cb376a181a629e5f88fa1c9dd", "size": 1878533, "url": "https://libraries.minecraft.net/net/java/dev/jna/jna/5.14.0/jna-5.14.0.jar"}}}, {"name": "net.sf.jopt-simple:jopt-simple:5.0.4", "downloads": {"artifact": {"path": "net/sf/jopt-simple/jopt-simple/5.0.4/jopt-simple-5.0.4.jar", "sha1": "4fdac2fbe92dfad86aa6e9301736f6b4342a3f5c", "size": 78146, "url": "https://libraries.minecraft.net/net/sf/jopt-simple/jopt-simple/5.0.4/jopt-simple-5.0.4.jar"}}}, {"name": "org.apache.commons:commons-compress:1.26.0", "downloads": {"artifact": {"path": "org/apache/commons/commons-compress/1.26.0/commons-compress-1.26.0.jar", "sha1": "659feffdd12280201c8aacb8f7be94f9a883c824", "size": 1078328, "url": "https://libraries.minecraft.net/org/apache/commons/commons-compress/1.26.0/commons-compress-1.26.0.jar"}}}, {"name": "org.apache.commons:commons-lang3:3.14.0", "downloads": {"artifact": {"path": "org/apache/commons/commons-lang3/3.14.0/commons-lang3-3.14.0.jar", "sha1": "1ed471194b02f2c6cb734a0cd6f6f107c673afae", "size": 657952, "url": "https://libraries.minecraft.net/org/apache/commons/commons-lang3/3.14.0/commons-lang3-3.14.0.jar"}}}, {"name": "org.apache.httpcomponents:httpclient:4.5.13", "downloads": {"artifact": {"path": "org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.jar", "sha1": "e5f6cae5ca7ecaac1ec2827a9e2d65ae2869cada", "size": 780321, "url": "https://libraries.minecraft.net/org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.jar"}}}, {"name": "org.apache.httpcomponents:httpcore:4.4.16", "downloads": {"artifact": {"path": "org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16.jar", "sha1": "51cf043c87253c9f58b539c9f7e44c8894223850", "size": 327891, "url": "https://libraries.minecraft.net/org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16.jar"}}}, {"name": "org.apache.logging.log4j:log4j-api:2.22.1", "downloads": {"artifact": {"path": "org/apache/logging/log4j/log4j-api/2.22.1/log4j-api-2.22.1.jar", "sha1": "bea6fede6328fabafd7e68363161a7ea6605abd1", "size": 335001, "url": "https://libraries.minecraft.net/org/apache/logging/log4j/log4j-api/2.22.1/log4j-api-2.22.1.jar"}}}, {"name": "org.apache.logging.log4j:log4j-core:2.22.1", "downloads": {"artifact": {"path": "org/apache/logging/log4j/log4j-core/2.22.1/log4j-core-2.22.1.jar", "sha1": "7183a25510a02ad00cc6a95d3b3d2a7d3c5a8dc4", "size": 1900022, "url": "https://libraries.minecraft.net/org/apache/logging/log4j/log4j-core/2.22.1/log4j-core-2.22.1.jar"}}}, {"name": "org.apache.logging.log4j:log4j-slf4j2-impl:2.22.1", "downloads": {"artifact": {"path": "org/apache/logging/log4j/log4j-slf4j2-impl/2.22.1/log4j-slf4j2-impl-2.22.1.jar", "sha1": "d7e6693c2606cb7e7335047d7bb96dec52db5665", "size": 27364, "url": "https://libraries.minecraft.net/org/apache/logging/log4j/log4j-slf4j2-impl/2.22.1/log4j-slf4j2-impl-2.22.1.jar"}}}, {"name": "org.jcraft:jorbis:0.0.17", "downloads": {"artifact": {"path": "org/jcraft/jorbis/0.0.17/jorbis-0.0.17.jar", "sha1": "8872d22b293e8f5d7d56ff92be966e6dc28ebdc6", "size": 99701, "url": "https://libraries.minecraft.net/org/jcraft/jorbis/0.0.17/jorbis-0.0.17.jar"}}}, {"name": "org.joml:joml:1.10.5", "downloads": {"artifact": {"path": "org/joml/joml/1.10.5/joml-1.10.5.jar", "sha1": "22566d58af70ad3d72308bab63b8339906deb649", "size": 712082, "url": "https://libraries.minecraft.net/org/joml/joml/1.10.5/joml-1.10.5.jar"}}}, {"name": "org.lwjgl:lwjgl-freetype:3.3.3", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-freetype/3.3.3/lwjgl-freetype-3.3.3.jar", "sha1": "a0db6c84a8becc8ca05f9dbfa985edc348a824c7", "size": 450896, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-freetype/3.3.3/lwjgl-freetype-3.3.3.jar"}}}, {"name": "org.lwjgl:lwjgl-freetype:3.3.3:natives-linux", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-freetype/3.3.3/lwjgl-freetype-3.3.3-natives-linux.jar", "sha1": "149070a5480900347071b7074779531f25a6e3dc", "size": 1245129, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-freetype/3.3.3/lwjgl-freetype-3.3.3-natives-linux.jar"}}, "rules": [{"action": "allow", "os": {"name": "linux"}}]}, {"name": "org.lwjgl:lwjgl-freetype:3.3.3:natives-macos-arm64", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-freetype/3.3.3/lwjgl-freetype-3.3.3-natives-macos-arm64.jar", "sha1": "b0a8c9baa9d1f54ac61e1ab9640c7659e7fa700c", "size": 1040981, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-freetype/3.3.3/lwjgl-freetype-3.3.3-natives-macos-arm64.jar"}}, "rules": [{"action": "allow", "os": {"name": "osx"}}]}, {"name": "org.lwjgl:lwjgl-freetype:3.3.3:natives-macos-patch", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-freetype/3.3.3/lwjgl-freetype-3.3.3-natives-macos-patch.jar", "sha1": "806d869f37ce0df388a24e17aaaf5ca0894d851b", "size": 1071983, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-freetype/3.3.3/lwjgl-freetype-3.3.3-natives-macos-patch.jar"}}, "rules": [{"action": "allow", "os": {"name": "osx"}}]}, {"name": "org.lwjgl:lwjgl-freetype:3.3.3:natives-windows", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-freetype/3.3.3/lwjgl-freetype-3.3.3-natives-windows.jar", "sha1": "81091b006dbb43fab04c8c638e9ac87c51b4096d", "size": 1035586, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-freetype/3.3.3/lwjgl-freetype-3.3.3-natives-windows.jar"}}, "rules": [{"action": "allow", "os": {"name": "windows"}}]}, {"name": "org.lwjgl:lwjgl-freetype:3.3.3:natives-windows-arm64", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-freetype/3.3.3/lwjgl-freetype-3.3.3-natives-windows-arm64.jar", "sha1": "82028265a0a2ff33523ca75137ada7dc176e5210", "size": 886068, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-freetype/3.3.3/lwjgl-freetype-3.3.3-natives-windows-arm64.jar"}}, "rules": [{"action": "allow", "os": {"name": "windows"}}]}, {"name": "org.lwjgl:lwjgl-freetype:3.3.3:natives-windows-x86", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-freetype/3.3.3/lwjgl-freetype-3.3.3-natives-windows-x86.jar", "sha1": "15a8c1de7f51d07a92eae7ce1222557073a0c0c3", "size": 877480, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-freetype/3.3.3/lwjgl-freetype-3.3.3-natives-windows-x86.jar"}}, "rules": [{"action": "allow", "os": {"name": "windows"}}]}, {"name": "org.lwjgl:lwjgl-glfw:3.3.3", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-glfw/3.3.3/lwjgl-glfw-3.3.3.jar", "sha1": "efa1eb78c5ccd840e9f329717109b5e892d72f8e", "size": 135546, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-glfw/3.3.3/lwjgl-glfw-3.3.3.jar"}}}, {"name": "org.lwjgl:lwjgl-glfw:3.3.3:natives-linux", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-glfw/3.3.3/lwjgl-glfw-3.3.3-natives-linux.jar", "sha1": "a03684c5e4b1b1dbbe0d29dbbdc27b985b6840f2", "size": 118478, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-glfw/3.3.3/lwjgl-glfw-3.3.3-natives-linux.jar"}}, "rules": [{"action": "allow", "os": {"name": "linux"}}]}, {"name": "org.lwjgl:lwjgl-glfw:3.3.3:natives-macos", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-glfw/3.3.3/lwjgl-glfw-3.3.3-natives-macos.jar", "sha1": "a1bf400f6bc64e6195596cb1430dafda46090751", "size": 140884, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-glfw/3.3.3/lwjgl-glfw-3.3.3-natives-macos.jar"}}, "rules": [{"action": "allow", "os": {"name": "osx"}}]}, {"name": "org.lwjgl:lwjgl-glfw:3.3.3:natives-macos-arm64", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-glfw/3.3.3/lwjgl-glfw-3.3.3-natives-macos-arm64.jar", "sha1": "ee8cc78d0a4a5b3b4600fade6d927c9fc320c858", "size": 138288, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-glfw/3.3.3/lwjgl-glfw-3.3.3-natives-macos-arm64.jar"}}, "rules": [{"action": "allow", "os": {"name": "osx"}}]}, {"name": "org.lwjgl:lwjgl-glfw:3.3.3:natives-windows", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-glfw/3.3.3/lwjgl-glfw-3.3.3-natives-windows.jar", "sha1": "e449e28b4891fc423c54c85fbc5bb0b9efece67a", "size": 166368, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-glfw/3.3.3/lwjgl-glfw-3.3.3-natives-windows.jar"}}, "rules": [{"action": "allow", "os": {"name": "windows"}}]}, {"name": "org.lwjgl:lwjgl-glfw:3.3.3:natives-windows-arm64", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-glfw/3.3.3/lwjgl-glfw-3.3.3-natives-windows-arm64.jar", "sha1": "f27018dc74f6289574502b46cce55d52817554e2", "size": 141970, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-glfw/3.3.3/lwjgl-glfw-3.3.3-natives-windows-arm64.jar"}}, "rules": [{"action": "allow", "os": {"name": "windows"}}]}, {"name": "org.lwjgl:lwjgl-glfw:3.3.3:natives-windows-x86", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-glfw/3.3.3/lwjgl-glfw-3.3.3-natives-windows-x86.jar", "sha1": "32334f3fd5270a59bad9939a93115acb6de36dcf", "size": 157123, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-glfw/3.3.3/lwjgl-glfw-3.3.3-natives-windows-x86.jar"}}, "rules": [{"action": "allow", "os": {"name": "windows"}}]}, {"name": "org.lwjgl:lwjgl-jemalloc:3.3.3", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-jemalloc/3.3.3/lwjgl-jemalloc-3.3.3.jar", "sha1": "b543467b7ff3c6920539a88ee602d34098628be5", "size": 43896, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-jemalloc/3.3.3/lwjgl-jemalloc-3.3.3.jar"}}}, {"name": "org.lwjgl:lwjgl-jemalloc:3.3.3:natives-linux", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-jemalloc/3.3.3/lwjgl-jemalloc-3.3.3-natives-linux.jar", "sha1": "4f86728bf449b1dd61251c4e0ac01df1389cb51e", "size": 206779, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-jemalloc/3.3.3/lwjgl-jemalloc-3.3.3-natives-linux.jar"}}, "rules": [{"action": "allow", "os": {"name": "linux"}}]}, {"name": "org.lwjgl:lwjgl-jemalloc:3.3.3:natives-macos", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-jemalloc/3.3.3/lwjgl-jemalloc-3.3.3-natives-macos.jar", "sha1": "2906637657a57579847238c9c72d2c4bde7083f8", "size": 153131, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-jemalloc/3.3.3/lwjgl-jemalloc-3.3.3-natives-macos.jar"}}, "rules": [{"action": "allow", "os": {"name": "osx"}}]}, {"name": "org.lwjgl:lwjgl-jemalloc:3.3.3:natives-macos-arm64", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-jemalloc/3.3.3/lwjgl-jemalloc-3.3.3-natives-macos-arm64.jar", "sha1": "e9412c3ff8cb3a3bad1d3f52909ad74d8a5bdad1", "size": 141418, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-jemalloc/3.3.3/lwjgl-jemalloc-3.3.3-natives-macos-arm64.jar"}}, "rules": [{"action": "allow", "os": {"name": "osx"}}]}, {"name": "org.lwjgl:lwjgl-jemalloc:3.3.3:natives-windows", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-jemalloc/3.3.3/lwjgl-jemalloc-3.3.3-natives-windows.jar", "sha1": "426222fc027602a5f21b9c0fe79cde6a4c7a011f", "size": 180344, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-jemalloc/3.3.3/lwjgl-jemalloc-3.3.3-natives-windows.jar"}}, "rules": [{"action": "allow", "os": {"name": "windows"}}]}, {"name": "org.lwjgl:lwjgl-jemalloc:3.3.3:natives-windows-arm64", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-jemalloc/3.3.3/lwjgl-jemalloc-3.3.3-natives-windows-arm64.jar", "sha1": "ba1f3fed0ee4be0217eaa41c5bbfb4b9b1383c33", "size": 154415, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-jemalloc/3.3.3/lwjgl-jemalloc-3.3.3-natives-windows-arm64.jar"}}, "rules": [{"action": "allow", "os": {"name": "windows"}}]}, {"name": "org.lwjgl:lwjgl-jemalloc:3.3.3:natives-windows-x86", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-jemalloc/3.3.3/lwjgl-jemalloc-3.3.3-natives-windows-x86.jar", "sha1": "f6063b6e0f23be483c5c88d84ce51b39dc69126c", "size": 148612, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-jemalloc/3.3.3/lwjgl-jemalloc-3.3.3-natives-windows-x86.jar"}}, "rules": [{"action": "allow", "os": {"name": "windows"}}]}, {"name": "org.lwjgl:lwjgl-openal:3.3.3", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-openal/3.3.3/lwjgl-openal-3.3.3.jar", "sha1": "daada81ceb5fc0c291fbfdd4433cb8d9423577f2", "size": 110586, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-openal/3.3.3/lwjgl-openal-3.3.3.jar"}}}, {"name": "org.lwjgl:lwjgl-openal:3.3.3:natives-linux", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-openal/3.3.3/lwjgl-openal-3.3.3-natives-linux.jar", "sha1": "3037360cc4595079bea240af250b6d1a527e0905", "size": 573224, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-openal/3.3.3/lwjgl-openal-3.3.3-natives-linux.jar"}}, "rules": [{"action": "allow", "os": {"name": "linux"}}]}, {"name": "org.lwjgl:lwjgl-openal:3.3.3:natives-macos", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-openal/3.3.3/lwjgl-openal-3.3.3-natives-macos.jar", "sha1": "8df8338bfa77f2ebabef4e58964bd04d24805cbf", "size": 519824, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-openal/3.3.3/lwjgl-openal-3.3.3-natives-macos.jar"}}, "rules": [{"action": "allow", "os": {"name": "osx"}}]}, {"name": "org.lwjgl:lwjgl-openal:3.3.3:natives-macos-arm64", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-openal/3.3.3/lwjgl-openal-3.3.3-natives-macos-arm64.jar", "sha1": "0c78b078de2fb52f45aa55d04db889a560f3544f", "size": 471012, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-openal/3.3.3/lwjgl-openal-3.3.3-natives-macos-arm64.jar"}}, "rules": [{"action": "allow", "os": {"name": "osx"}}]}, {"name": "org.lwjgl:lwjgl-openal:3.3.3:natives-windows", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-openal/3.3.3/lwjgl-openal-3.3.3-natives-windows.jar", "sha1": "cf83862ae95d98496b26915024c7e666d8ab1c8f", "size": 698720, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-openal/3.3.3/lwjgl-openal-3.3.3-natives-windows.jar"}}, "rules": [{"action": "allow", "os": {"name": "windows"}}]}, {"name": "org.lwjgl:lwjgl-openal:3.3.3:natives-windows-arm64", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-openal/3.3.3/lwjgl-openal-3.3.3-natives-windows-arm64.jar", "sha1": "8e0615235116b9e4160dfe87bec90f5f6378bf72", "size": 630410, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-openal/3.3.3/lwjgl-openal-3.3.3-natives-windows-arm64.jar"}}, "rules": [{"action": "allow", "os": {"name": "windows"}}]}, {"name": "org.lwjgl:lwjgl-openal:3.3.3:natives-windows-x86", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-openal/3.3.3/lwjgl-openal-3.3.3-natives-windows-x86.jar", "sha1": "87b8d5050e3adb46bb58fe1cb2669a4a48fce10d", "size": 638424, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-openal/3.3.3/lwjgl-openal-3.3.3-natives-windows-x86.jar"}}, "rules": [{"action": "allow", "os": {"name": "windows"}}]}, {"name": "org.lwjgl:lwjgl-opengl:3.3.3", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-opengl/3.3.3/lwjgl-opengl-3.3.3.jar", "sha1": "02f6b0147078396a58979125a4c947664e98293a", "size": 929192, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-opengl/3.3.3/lwjgl-opengl-3.3.3.jar"}}}, {"name": "org.lwjgl:lwjgl-opengl:3.3.3:natives-linux", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-opengl/3.3.3/lwjgl-opengl-3.3.3-natives-linux.jar", "sha1": "62c70a4b00ca5391882b0f4b787c1588d24f1c86", "size": 80463, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-opengl/3.3.3/lwjgl-opengl-3.3.3-natives-linux.jar"}}, "rules": [{"action": "allow", "os": {"name": "linux"}}]}, {"name": "org.lwjgl:lwjgl-opengl:3.3.3:natives-macos", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-opengl/3.3.3/lwjgl-opengl-3.3.3-natives-macos.jar", "sha1": "1bd45997551ae8a28469f3a2b678f4b7289e12c0", "size": 41484, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-opengl/3.3.3/lwjgl-opengl-3.3.3-natives-macos.jar"}}, "rules": [{"action": "allow", "os": {"name": "osx"}}]}, {"name": "org.lwjgl:lwjgl-opengl:3.3.3:natives-macos-arm64", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-opengl/3.3.3/lwjgl-opengl-3.3.3-natives-macos-arm64.jar", "sha1": "d213ddef27637b1af87961ffa94d6b27036becc8", "size": 42487, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-opengl/3.3.3/lwjgl-opengl-3.3.3-natives-macos-arm64.jar"}}, "rules": [{"action": "allow", "os": {"name": "osx"}}]}, {"name": "org.lwjgl:lwjgl-opengl:3.3.3:natives-windows", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-opengl/3.3.3/lwjgl-opengl-3.3.3-natives-windows.jar", "sha1": "e6c1eec8be8a71951b830a4d69efc01c6531900c", "size": 101535, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-opengl/3.3.3/lwjgl-opengl-3.3.3-natives-windows.jar"}}, "rules": [{"action": "allow", "os": {"name": "windows"}}]}, {"name": "org.lwjgl:lwjgl-opengl:3.3.3:natives-windows-arm64", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-opengl/3.3.3/lwjgl-opengl-3.3.3-natives-windows-arm64.jar", "sha1": "65e956d3735a1abdc82eff4baec1b61174697d4b", "size": 83095, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-opengl/3.3.3/lwjgl-opengl-3.3.3-natives-windows-arm64.jar"}}, "rules": [{"action": "allow", "os": {"name": "windows"}}]}, {"name": "org.lwjgl:lwjgl-opengl:3.3.3:natives-windows-x86", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-opengl/3.3.3/lwjgl-opengl-3.3.3-natives-windows-x86.jar", "sha1": "0d32d833dcaa2f355a886eaf21f0408b5f03241d", "size": 88612, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-opengl/3.3.3/lwjgl-opengl-3.3.3-natives-windows-x86.jar"}}, "rules": [{"action": "allow", "os": {"name": "windows"}}]}, {"name": "org.lwjgl:lwjgl-stb:3.3.3", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-stb/3.3.3/lwjgl-stb-3.3.3.jar", "sha1": "25dd6161988d7e65f71d5065c99902402ee32746", "size": 120283, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-stb/3.3.3/lwjgl-stb-3.3.3.jar"}}}, {"name": "org.lwjgl:lwjgl-stb:3.3.3:natives-linux", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-stb/3.3.3/lwjgl-stb-3.3.3-natives-linux.jar", "sha1": "fd1271ccd9d85eff2fa31f3fd543e02ccfaf5041", "size": 231820, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-stb/3.3.3/lwjgl-stb-3.3.3-natives-linux.jar"}}, "rules": [{"action": "allow", "os": {"name": "linux"}}]}, {"name": "org.lwjgl:lwjgl-stb:3.3.3:natives-macos", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-stb/3.3.3/lwjgl-stb-3.3.3-natives-macos.jar", "sha1": "472792c98fb2c1557c060cb9da5fca6a9773621f", "size": 216456, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-stb/3.3.3/lwjgl-stb-3.3.3-natives-macos.jar"}}, "rules": [{"action": "allow", "os": {"name": "osx"}}]}, {"name": "org.lwjgl:lwjgl-stb:3.3.3:natives-macos-arm64", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-stb/3.3.3/lwjgl-stb-3.3.3-natives-macos-arm64.jar", "sha1": "51c6955571fbcdb7bb538c6aa589b953b584c6af", "size": 183628, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-stb/3.3.3/lwjgl-stb-3.3.3-natives-macos-arm64.jar"}}, "rules": [{"action": "allow", "os": {"name": "osx"}}]}, {"name": "org.lwjgl:lwjgl-stb:3.3.3:natives-windows", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-stb/3.3.3/lwjgl-stb-3.3.3-natives-windows.jar", "sha1": "1d9facdf6541de114b0f963be33505b7679c78cb", "size": 261297, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-stb/3.3.3/lwjgl-stb-3.3.3-natives-windows.jar"}}, "rules": [{"action": "allow", "os": {"name": "windows"}}]}, {"name": "org.lwjgl:lwjgl-stb:3.3.3:natives-windows-arm64", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-stb/3.3.3/lwjgl-stb-3.3.3-natives-windows-arm64.jar", "sha1": "a584ab44de569708871f0a79561f4d8c37487f2c", "size": 219511, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-stb/3.3.3/lwjgl-stb-3.3.3-natives-windows-arm64.jar"}}, "rules": [{"action": "allow", "os": {"name": "windows"}}]}, {"name": "org.lwjgl:lwjgl-stb:3.3.3:natives-windows-x86", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-stb/3.3.3/lwjgl-stb-3.3.3-natives-windows-x86.jar", "sha1": "b5c874687b9aac1a936501d4ed2c49567fd1b575", "size": 227800, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-stb/3.3.3/lwjgl-stb-3.3.3-natives-windows-x86.jar"}}, "rules": [{"action": "allow", "os": {"name": "windows"}}]}, {"name": "org.lwjgl:lwjgl-tinyfd:3.3.3", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-tinyfd/3.3.3/lwjgl-tinyfd-3.3.3.jar", "sha1": "82d755ca94b102e9ca77283b9e2dc46d1b15fbe5", "size": 13400, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-tinyfd/3.3.3/lwjgl-tinyfd-3.3.3.jar"}}}, {"name": "org.lwjgl:lwjgl-tinyfd:3.3.3:natives-linux", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-tinyfd/3.3.3/lwjgl-tinyfd-3.3.3-natives-linux.jar", "sha1": "d8d58daa0c3e5fd906fee96f5fddbcbc07cc308b", "size": 44192, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-tinyfd/3.3.3/lwjgl-tinyfd-3.3.3-natives-linux.jar"}}, "rules": [{"action": "allow", "os": {"name": "linux"}}]}, {"name": "org.lwjgl:lwjgl-tinyfd:3.3.3:natives-macos", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-tinyfd/3.3.3/lwjgl-tinyfd-3.3.3-natives-macos.jar", "sha1": "6598081e346a03038a8be68eb2de614a1c2eac68", "size": 45865, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-tinyfd/3.3.3/lwjgl-tinyfd-3.3.3-natives-macos.jar"}}, "rules": [{"action": "allow", "os": {"name": "osx"}}]}, {"name": "org.lwjgl:lwjgl-tinyfd:3.3.3:natives-macos-arm64", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-tinyfd/3.3.3/lwjgl-tinyfd-3.3.3-natives-macos-arm64.jar", "sha1": "406feedb977372085a61eb0fee358183f4f4c67a", "size": 42498, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-tinyfd/3.3.3/lwjgl-tinyfd-3.3.3-natives-macos-arm64.jar"}}, "rules": [{"action": "allow", "os": {"name": "osx"}}]}, {"name": "org.lwjgl:lwjgl-tinyfd:3.3.3:natives-windows", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-tinyfd/3.3.3/lwjgl-tinyfd-3.3.3-natives-windows.jar", "sha1": "a6697981b0449a5087c1d546fc08b4f73e8f98c9", "size": 130253, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-tinyfd/3.3.3/lwjgl-tinyfd-3.3.3-natives-windows.jar"}}, "rules": [{"action": "allow", "os": {"name": "windows"}}]}, {"name": "org.lwjgl:lwjgl-tinyfd:3.3.3:natives-windows-arm64", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-tinyfd/3.3.3/lwjgl-tinyfd-3.3.3-natives-windows-arm64.jar", "sha1": "a88c494f3006eb91a7433b12a3a55a9a6c20788b", "size": 110867, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-tinyfd/3.3.3/lwjgl-tinyfd-3.3.3-natives-windows-arm64.jar"}}, "rules": [{"action": "allow", "os": {"name": "windows"}}]}, {"name": "org.lwjgl:lwjgl-tinyfd:3.3.3:natives-windows-x86", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl-tinyfd/3.3.3/lwjgl-tinyfd-3.3.3-natives-windows-x86.jar", "sha1": "c336c84ee88cccb495c6ffa112395509e7378e8a", "size": 111797, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl-tinyfd/3.3.3/lwjgl-tinyfd-3.3.3-natives-windows-x86.jar"}}, "rules": [{"action": "allow", "os": {"name": "windows"}}]}, {"name": "org.lwjgl:lwjgl:3.3.3", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl/3.3.3/lwjgl-3.3.3.jar", "sha1": "29589b5f87ed335a6c7e7ee6a5775f81f97ecb84", "size": 785029, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl/3.3.3/lwjgl-3.3.3.jar"}}}, {"name": "org.lwjgl:lwjgl:3.3.3:natives-linux", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl/3.3.3/lwjgl-3.3.3-natives-linux.jar", "sha1": "1713758e3660ba66e1e954396fd18126038b33c0", "size": 114627, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl/3.3.3/lwjgl-3.3.3-natives-linux.jar"}}, "rules": [{"action": "allow", "os": {"name": "linux"}}]}, {"name": "org.lwjgl:lwjgl:3.3.3:natives-macos", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl/3.3.3/lwjgl-3.3.3-natives-macos.jar", "sha1": "33a6efa288390490ce6eb6c3df47ac21ecf648cf", "size": 60543, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl/3.3.3/lwjgl-3.3.3-natives-macos.jar"}}, "rules": [{"action": "allow", "os": {"name": "osx"}}]}, {"name": "org.lwjgl:lwjgl:3.3.3:natives-macos-arm64", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl/3.3.3/lwjgl-3.3.3-natives-macos-arm64.jar", "sha1": "226246e75f6bd8d4e1895bdce8638ef87808d114", "size": 48620, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl/3.3.3/lwjgl-3.3.3-natives-macos-arm64.jar"}}, "rules": [{"action": "allow", "os": {"name": "osx"}}]}, {"name": "org.lwjgl:lwjgl:3.3.3:natives-windows", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl/3.3.3/lwjgl-3.3.3-natives-windows.jar", "sha1": "a5ed18a2b82fc91b81f40d717cb1f64c9dcb0540", "size": 165442, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl/3.3.3/lwjgl-3.3.3-natives-windows.jar"}}, "rules": [{"action": "allow", "os": {"name": "windows"}}]}, {"name": "org.lwjgl:lwjgl:3.3.3:natives-windows-arm64", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl/3.3.3/lwjgl-3.3.3-natives-windows-arm64.jar", "sha1": "e9aca8c5479b520a2a7f0d542a118140e812c5e8", "size": 133378, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl/3.3.3/lwjgl-3.3.3-natives-windows-arm64.jar"}}, "rules": [{"action": "allow", "os": {"name": "windows"}}]}, {"name": "org.lwjgl:lwjgl:3.3.3:natives-windows-x86", "downloads": {"artifact": {"path": "org/lwjgl/lwjgl/3.3.3/lwjgl-3.3.3-natives-windows-x86.jar", "sha1": "9e670718e050aeaeea0c2d5b907cffb142f2e58f", "size": 139653, "url": "https://libraries.minecraft.net/org/lwjgl/lwjgl/3.3.3/lwjgl-3.3.3-natives-windows-x86.jar"}}, "rules": [{"action": "allow", "os": {"name": "windows"}}]}, {"name": "org.lz4:lz4-java:1.8.0", "downloads": {"artifact": {"path": "org/lz4/lz4-java/1.8.0/lz4-java-1.8.0.jar", "sha1": "4b986a99445e49ea5fbf5d149c4b63f6ed6c6780", "size": 682804, "url": "https://libraries.minecraft.net/org/lz4/lz4-java/1.8.0/lz4-java-1.8.0.jar"}}}, {"name": "org.slf4j:slf4j-api:2.0.9", "downloads": {"artifact": {"path": "org/slf4j/slf4j-api/2.0.9/slf4j-api-2.0.9.jar", "sha1": "7cf2726fdcfbc8610f9a71fb3ed639871f315340", "size": 64579, "url": "https://libraries.minecraft.net/org/slf4j/slf4j-api/2.0.9/slf4j-api-2.0.9.jar"}}}], "mainClass": "net.fabricmc.loader.impl.launch.knot.KnotClient"}