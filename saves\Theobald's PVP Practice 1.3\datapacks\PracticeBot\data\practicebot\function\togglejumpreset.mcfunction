execute if entity @a[name=<PERSON><PERSON>_Cutee,tag=!jreset] run tag Ng<PERSON>_Cutee add addjreset
tag Ngoc_Cutee remove jreset
execute if entity @a[name=Ngoc_Cutee,tag=addjreset] run tag Ngoc_Cutee add jreset
tag Ngoc_Cutee remove addjreset

execute if entity @a[name=Ngoc_Cutee,tag=jreset] run title @a actionbar [{"text":"JUMP RESETTING ","color":"yellow"},{"text":"ON", "color":"green"}]
execute if entity @a[name=Ngoc_Cutee,tag=!jreset] run title @a actionbar [{"text":"JUMP RESETTING ","color":"yellow"},{"text":"OFF", "color":"red"}] 

execute as <PERSON>oc_Cutee if entity @s[tag=jreset] run tag @s add tempjreset
execute as Ngoc_Cutee if entity @s[tag=!jreset] run tag @s remove tempjreset