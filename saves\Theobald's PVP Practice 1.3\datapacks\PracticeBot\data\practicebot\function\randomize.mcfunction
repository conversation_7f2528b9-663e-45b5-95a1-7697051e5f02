tag <PERSON><PERSON>_<PERSON>ee remove tempcrit
tag <PERSON><PERSON>_<PERSON>ee remove tempjreset
tag <PERSON><PERSON>_<PERSON>ee remove tempshield
tag <PERSON><PERSON>_<PERSON>ee remove tempstap
tag Ng<PERSON>_<PERSON>ee remove tempstrafe

execute as <PERSON><PERSON>_Cutee if entity @s[tag=crit] if predicate practicebot:random run tag <PERSON><PERSON>_<PERSON>ee add tempcrit
execute as <PERSON><PERSON>_Cutee if entity @s[tag=jreset] if predicate practicebot:random run tag Ng<PERSON>_Cutee add tempjreset
execute as <PERSON><PERSON>_Cutee if entity @s[tag=shield] if predicate practicebot:random run tag Ng<PERSON>_Cutee add tempshield
execute as <PERSON>oc_Cutee if entity @s[tag=stap] if predicate practicebot:random run tag Ng<PERSON>_<PERSON>ee add tempstap
execute as <PERSON><PERSON>_Cutee if entity @s[tag=strafe] if predicate practicebot:random run tag Ng<PERSON>_Cutee add tempstrafe

scoreboard players set Ngoc_Cutee randomcd 9
execute if predicate practicebot:random run scoreboard players add <PERSON><PERSON>_Cutee randomcd 5