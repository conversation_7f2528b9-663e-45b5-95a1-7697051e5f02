player <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> stop
player <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> move forward
player <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sprint

execute as <PERSON><PERSON><PERSON><PERSON>heB<PERSON> if entity @s[tag=sword,tag=shield] if score @p[name=!TheobaldTheBot] shieldcount matches 1.. if score @s difficulty matches 2.. run player <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hotbar 4
execute as <PERSON>baldTheB<PERSON> if entity @s[tag=sword,tag=shield] if score @p[name=!TheobaldTheBot] shieldcount matches 1.. if score @s difficulty matches 2.. run schedule function practicebot:hotbar1 2t
scoreboard players set @a[name=!TheobaldTheBot] shieldcount 0

player TheobaldTheBot attack once

execute if entity @a[name=TheobaldTheBot,tag=crystal] if score TheobaldTheBot difficulty matches 2.. run function practicebot:spawncrystal
scoreboard players set TheobaldTheBot hitcd 11

# schedule function practicebot:attack 4t

execute as TheobaldTheBot if entity @s[tag=shield,tag=sword] run schedule function practicebot:usecontinuous 2t
# execute if entity @a[name=TheobaldTheBot,tag=crystal] run scoreboard players set TheobaldTheBot hitcd 21