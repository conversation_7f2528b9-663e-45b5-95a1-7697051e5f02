{"hardInclude": "anything", "includeList": [], "includeListInSuperCategory": true, "excludeMode": "ONLY", "excludeList": ["minecraft:glow_item_frame", "minecraft:item_frame"], "name": "gui.xaero_entity_category_root", "protection": true, "settingOverrides": {"displayHeight": 0.0, "displayed": true, "heightBasedFade": true, "renderOrder": 0.0, "color": 13.0, "displayNameWhenIconFails": true, "entityNumber": 1000.0, "alwaysDisplayNametags": false, "startFadingAt": 0.0, "dotSize": 2.0, "renderOverMinimapFrame": 1.0, "icons": 1.0, "heightLimit": 20.0, "names": 0.0, "iconScale": 1.0}, "subCategories": [{"hardInclude": "living", "includeList": [], "includeListInSuperCategory": true, "excludeMode": "ONLY", "excludeList": ["minecraft:armor_stand"], "name": "gui.xaero_entity_category_living", "protection": true, "settingOverrides": {"renderOrder": 2.0, "color": 14.0}, "subCategories": [{"hardInclude": "players", "includeList": [], "includeListInSuperCategory": true, "excludeMode": "ONLY", "excludeList": [], "name": "gui.xaero_entity_category_players", "protection": true, "settingOverrides": {"renderOrder": 6.0, "heightLimit": 2050.0, "color": 15.0}, "subCategories": [{"hardInclude": "nothing", "includeList": [], "includeListInSuperCategory": true, "excludeMode": "ONLY", "excludeList": [], "name": "gui.xaero_entity_category_friend", "protection": true, "settingOverrides": {}, "subCategories": []}, {"hardInclude": "tracked", "includeList": [], "includeListInSuperCategory": true, "excludeMode": "ONLY", "excludeList": [], "name": "gui.xaero_entity_category_tracked", "protection": true, "settingOverrides": {"icons": 2.0}, "subCategories": []}, {"hardInclude": "same-team", "includeList": [], "includeListInSuperCategory": true, "excludeMode": "ONLY", "excludeList": [], "name": "gui.xaero_entity_category_same_team", "protection": true, "settingOverrides": {}, "subCategories": []}, {"hardInclude": "anything", "includeList": [], "includeListInSuperCategory": true, "excludeMode": "ONLY", "excludeList": [], "name": "gui.xaero_entity_category_other_teams", "protection": true, "settingOverrides": {"renderOrder": 7.0}, "subCategories": []}]}, {"hardInclude": "hostile", "includeList": [], "includeListInSuperCategory": true, "excludeMode": "ONLY", "excludeList": [], "name": "gui.xaero_entity_category_hostile", "protection": true, "settingOverrides": {"renderOrder": 3.0}, "subCategories": [{"hardInclude": "tamed", "includeList": [], "includeListInSuperCategory": true, "excludeMode": "ONLY", "excludeList": [], "name": "gui.xaero_entity_category_hostile_tamed", "protection": true, "settingOverrides": {"renderOrder": 5.0}, "subCategories": []}]}, {"hardInclude": "anything", "includeList": [], "includeListInSuperCategory": true, "excludeMode": "ONLY", "excludeList": [], "name": "gui.xaero_entity_category_friendly", "protection": true, "settingOverrides": {}, "subCategories": [{"hardInclude": "tamed", "includeList": [], "includeListInSuperCategory": true, "excludeMode": "ONLY", "excludeList": [], "name": "gui.xaero_entity_category_friendly_tamed", "protection": true, "settingOverrides": {"renderOrder": 4.0}, "subCategories": []}]}]}, {"hardInclude": "items", "includeList": [], "includeListInSuperCategory": true, "excludeMode": "ONLY", "excludeList": [], "name": "gui.xaero_entity_category_items", "protection": true, "settingOverrides": {"renderOrder": 1.0, "color": 12.0}, "subCategories": []}, {"hardInclude": "anything", "includeList": [], "includeListInSuperCategory": true, "excludeMode": "ONLY", "excludeList": [], "name": "gui.xaero_entity_category_other_entities", "protection": true, "settingOverrides": {"color": 5.0}, "subCategories": []}]}