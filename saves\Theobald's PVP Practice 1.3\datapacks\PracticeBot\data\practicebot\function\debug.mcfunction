# Debug function to check bot status
tellraw @a [{"text":"=== NGOC_CUTEE BOT DEBUG ===","color":"yellow"}]
execute if entity <PERSON>oc_Cutee run tellraw @a [{"text":"✓ Bot entity exists","color":"green"}]
execute unless entity <PERSON>oc_Cutee run tellraw @a [{"text":"✗ Bot entity NOT found","color":"red"}]
execute as Ngoc_Cutee run tellraw @a [{"text":"✓ Bot is responsive","color":"green"}]
execute unless entity Ngoc_Cutee run tellraw @a [{"text":"✗ Bot is NOT responsive","color":"red"}]
tellraw @a [{"text":"Bot gamemode: ","color":"white"},{"selector":"Ngoc_Cutee","color":"aqua"}]
execute as Ngoc_Cutee run tellraw @a [{"text":"Bot position: ","color":"white"},{"nbt":"Pos","entity":"@s","color":"aqua"}]
execute if score Ngoc_Cutee difficulty matches 0.. run tellraw @a [{"text":"Bot difficulty: ","color":"white"},{"score":{"name":"Ngoc_Cutee","objective":"difficulty"},"color":"aqua"}]
execute unless score Ngoc_Cutee difficulty matches 0.. run tellraw @a [{"text":"✗ Bot difficulty not set","color":"red"}]
execute if entity Ngoc_Cutee[tag=bot] run tellraw @a [{"text":"✓ Bot has 'bot' tag","color":"green"}]
execute unless entity Ngoc_Cutee[tag=bot] run tellraw @a [{"text":"✗ Bot missing 'bot' tag","color":"red"}]
execute if entity Ngoc_Cutee[tag=sword] run tellraw @a [{"text":"✓ Bot in sword mode","color":"green"}]
execute if entity Ngoc_Cutee[tag=crystal] run tellraw @a [{"text":"✓ Bot in crystal mode","color":"green"}]
execute unless entity Ngoc_Cutee[tag=sword] unless entity Ngoc_Cutee[tag=crystal] run tellraw @a [{"text":"✗ Bot has no mode set","color":"red"}]
tellraw @a [{"text":"=== END DEBUG ===","color":"yellow"}]
