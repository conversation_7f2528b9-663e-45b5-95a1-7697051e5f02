execute store result score TheobaldTheBot food run data get entity TheobaldTheBot foodLevel 1
execute run effect give @s[scores={food=..18}] minecraft:saturation 1 1 true
execute if entity @s[tag=sword] run effect give TheobaldTheBot resistance 1 255 true
#execute if entity @s[tag=!naked,tag=!shield] run function practicebot:refilltotem
scoreboard players remove @s hitcd 1
scoreboard players remove @s strafecd 1

execute if score @s combo matches 3.. run tag TheobaldTheBot add tempshield
execute if score @s shieldcd matches 1.. run scoreboard players remove @s shieldcd 1
execute at @s if entity @p[distance=6..] run scoreboard players set @s shieldcd 1
execute if entity @s[tag=tempshield] run player <PERSON>bald<PERSON><PERSON><PERSON><PERSON> use continuous
execute if entity @s[tag=!tempshield] run player <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> use
execute if score @s shieldcd matches 1.. run player <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> use

execute if score @s randomcd matches 1.. run scoreboard players remove @s randomcd 1

execute if entity @s[tag=random] if score @s randomcd matches 0 run function practicebot:randomize

execute store result score TheobaldTheBot vy run data get entity TheobaldTheBot Motion[1] 100
execute store result score TheobaldTheBot onground run data get entity TheobaldTheBot OnGround 
execute run function practicebot:botstats/fv

function practicebot:look
player TheobaldTheBot sprint
player TheobaldTheBot move forward

player TheobaldTheBot hotbar 1
execute as TheobaldTheBot at @s if entity @s[tag=neth,scores={difficulty=2..},tag=shield] at @p[name=!TheobaldTheBot,distance=8.5..] if block ~ ~-.1 ~ netherite_block run function practicebot:pearl

execute if score @s[tag=tempstap,tag=tempcrit] hitcd matches 5.. if score @s onground matches 1 run player TheobaldTheBot move backward
execute if score @s[tag=tempstap,tag=tempcrit] fz matches ..0 if score @s onground matches 0 run player TheobaldTheBot move backward
execute at @s[tag=tempcrit] if entity @p[name=!TheobaldTheBot,distance=..2] run player TheobaldTheBot move forward
execute if score @s[tag=tempcrit] fz matches 1.. at TheobaldTheBot if entity @p[name=!TheobaldTheBot,distance=3.5..6] run function practicebot:straightjump
execute if score @s[tag=tempcrit] fz matches 1.. at TheobaldTheBot if entity @p[name=!TheobaldTheBot,distance=..3.5] run function practicebot:jump
execute if entity @p[name=!TheobaldTheBot,distance=..2] run function practicebot:jump
execute if entity @a[name=TheobaldTheBot,tag=speed,tag=!tempstap,tag=!tempcrit,tag=tempshield] if score TheobaldTheBot hitcd matches 2.. if score TheobaldTheBot shieldcd matches 1.. run player TheobaldTheBot move
execute if entity @a[name=TheobaldTheBot,tag=!tempstap,tag=!tempcrit,tag=tempshield] if score TheobaldTheBot hitcd matches 5.. if score TheobaldTheBot shieldcd matches 1.. run player TheobaldTheBot move
execute if entity @a[name=TheobaldTheBot,tag=speed,tag=!tempstap,tag=!tempcrit] if score TheobaldTheBot hitcd matches 2.. run player TheobaldTheBot move
execute if entity @a[name=TheobaldTheBot,tag=!tempstap,tag=!tempcrit] if score TheobaldTheBot hitcd matches 5.. run player TheobaldTheBot move
execute if entity @a[name=TheobaldTheBot,tag=tempstap,tag=!tempcrit] if score TheobaldTheBot hitcd matches 7.. run player TheobaldTheBot move backward
execute if entity @s[scores={strafecd=..0},tag=tempstrafe] run function practicebot:choosedirection
#execute at @s unless entity @p[name=!TheobaldTheBot,distance=4.5..] run 
execute if entity @s[tag=tempstrafe] run function practicebot:strafe


execute if entity @s[tag=!tempcrit] run function practicebot:swordhit
execute if entity @s[tag=tempcrit] if score @s vy matches ..0 if score @s onground matches 0 run function practicebot:swordcrit
execute if score @s[tag=tempcrit] hitcd matches ..-2 if score @s onground matches 1 if score @s difficulty matches 3..4 run function practicebot:swordhit
execute if score @s[tag=tempcrit] hitcd matches ..-7 if score @s difficulty matches 3..4 run function practicebot:swordhit

execute if score @s[tag=!tempcrit] difficulty matches 0 at TheobaldTheBot if entity @p[name=!TheobaldTheBot,distance=..1.5,tag=!tempcrit,tag=!shield] run player TheobaldTheBot move
execute if entity @s[nbt={HurtTime:10s},tag=tempjreset] run function practicebot:jumpreset
item modify entity TheobaldTheBot weapon.mainhand practicebot:refill

#execute at @s unless block ^ ^.5 ^1 air run player TheobaldTheBot jump
#execute at @s unless block ^ ^.5 ^2 air run player TheobaldTheBot jump
#execute at @s unless block ^-1 ^.5 ^2 air run player TheobaldTheBot jump
#execute at @s unless block ^1 ^.5 ^2 air run player TheobaldTheBot jump

execute if entity @s[tag=tempcrit] run scoreboard players set !crit randoms 1
execute if entity @s[tag=!tempcrit] run scoreboard players set !crit randoms 0

execute if entity @s[tag=tempjreset] run scoreboard players set !jreset randoms 1
execute if entity @s[tag=!tempjreset] run scoreboard players set !jreset randoms 0

execute if entity @s[tag=tempstap] run scoreboard players set !stap randoms 1
execute if entity @s[tag=!tempstap] run scoreboard players set !stap randoms 0

execute if entity @s[tag=tempshield] run scoreboard players set !shield randoms 1
execute if entity @s[tag=!tempshield] run scoreboard players set !shield randoms 0

execute if entity @s[tag=tempstrafe] run scoreboard players set !strafe randoms 1
execute if entity @s[tag=!tempstrafe] run scoreboard players set !strafe randoms 0

