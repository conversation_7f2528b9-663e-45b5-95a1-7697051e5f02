[16:29:27] [main/INFO]: Loading Minecraft 1.21.1 with Fabric Loader 0.16.10
[16:29:27] [ForkJoinPool-1-worker-11/WARN]: Mod org_cloudburstmc_netty_netty-transport-raknet uses the version 1.0.0.CR3-SNAPSHOT which isn't compatible with Loader's extended semantic version format (Could not parse version number component 'CR3'!), <PERSON>m<PERSON>er is recommended for reliably evaluating dependencies and prioritizing newer version
[16:29:27] [ForkJoinPool-1-worker-10/WARN]: Mod com_github_oryxel_cubeconverter uses the version 5ae1e90e4f which isn't compatible with Loader's extended semantic version format (Could not parse version number component '5ae1e90e4f'!), <PERSON><PERSON><PERSON><PERSON> is recommended for reliably evaluating dependencies and prioritizing newer version
[16:29:27] [main/INFO]: Loading 113 mods:
	- appleskin 3.0.6+mc1.21
	   \-- cloth-config 15.0.127
	        \-- cloth-basic-math 0.6.1
	- bobby 5.2.4+mc1.21
	   |-- com_typesafe_config 1.4.2
	   |-- fabric-api-base 0.4.42+6573ed8c6a
	   |-- fabric-command-api-v2 2.2.28+6ced4dd96a
	   |-- io_leangen_geantyref_geantyref 1.3.13
	   |-- org_spongepowered_configurate-core 4.1.2
	   \-- org_spongepowered_configurate-hocon 4.1.2
	- carpet 1.4.147+v240613
	- clumps 19.0.0.1
	- fabric-api 0.116.4+1.21.1
	   |-- fabric-api-lookup-api-v1 1.6.71+b559734419
	   |-- fabric-biome-api-v1 13.0.31+d527f9fd19
	   |-- fabric-block-api-v1 1.1.0+0bc3503219
	   |-- fabric-block-view-api-v2 1.0.11+ebb2264e19
	   |-- fabric-blockrenderlayer-v1 1.1.52+0af3f5a719
	   |-- fabric-client-tags-api-v1 1.1.15+6573ed8c19
	   |-- fabric-command-api-v1 1.2.49+f71b366f19
	   |-- fabric-commands-v0 0.2.66+df3654b319
	   |-- fabric-content-registries-v0 8.0.19+b559734419
	   |-- fabric-convention-tags-v1 2.1.5+7f945d5b19
	   |-- fabric-convention-tags-v2 2.11.1+a406e79519
	   |-- fabric-crash-report-info-v1 0.2.29+0af3f5a719
	   |-- fabric-data-attachment-api-v1 1.4.5+6116a37819
	   |-- fabric-data-generation-api-v1 20.2.32+5b9dea2e19
	   |-- fabric-dimensions-v1 4.0.0+6fc22b9919
	   |-- fabric-entity-events-v1 1.8.0+2b27e0a419
	   |-- fabric-events-interaction-v0 0.7.13+ba9dae0619
	   |-- fabric-game-rule-api-v1 1.0.53+6ced4dd919
	   |-- fabric-item-api-v1 11.1.1+d5debaed19
	   |-- fabric-item-group-api-v1 4.1.7+def88e3a19
	   |-- fabric-key-binding-api-v1 1.0.47+0af3f5a719
	   |-- fabric-keybindings-v0 0.2.45+df3654b319
	   |-- fabric-lifecycle-events-v1 2.6.0+0865547519
	   |-- fabric-loot-api-v2 3.0.15+3f89f5a519
	   |-- fabric-loot-api-v3 1.0.3+3f89f5a519
	   |-- fabric-message-api-v1 6.0.14+8aaf3aca19
	   |-- fabric-model-loading-api-v1 2.0.0+fe474d6b19
	   |-- fabric-networking-api-v1 4.3.0+c7469b2119
	   |-- fabric-object-builder-api-v1 15.2.1+40875a9319
	   |-- fabric-particles-v1 4.0.2+6573ed8c19
	   |-- fabric-recipe-api-v1 5.0.14+248df81c19
	   |-- fabric-registry-sync-v0 5.3.1+e3eddc2119
	   |-- fabric-renderer-api-v1 3.4.0+c705a49c19
	   |-- fabric-renderer-indigo 1.7.0+c705a49c19
	   |-- fabric-renderer-registries-v1 3.2.69+df3654b319
	   |-- fabric-rendering-data-attachment-v1 0.3.49+73761d2e19
	   |-- fabric-rendering-fluids-v1 3.1.6+1daea21519
	   |-- fabric-rendering-v0 1.1.72+df3654b319
	   |-- fabric-rendering-v1 5.1.0+ab4c25a019
	   |-- fabric-resource-conditions-api-v1 4.3.0+8dc279b119
	   |-- fabric-resource-loader-v0 1.3.1+5b5275af19
	   |-- fabric-screen-api-v1 2.0.25+8b68f1c719
	   |-- fabric-screen-handler-api-v1 1.3.90+b559734419
	   |-- fabric-sound-api-v1 1.0.23+6573ed8c19
	   |-- fabric-transfer-api-v1 5.4.3+c24bd99419
	   \-- fabric-transitive-access-wideners-v1 6.2.0+45b9699719
	- fabricloader 0.16.10
	   \-- mixinextras 0.4.1
	- fpsreducer 1.21-2.9
	- ias 9.0.4
	- indium 1.0.35+mc1.21
	- iris 1.7.3+mc1.21
	   |-- io_github_douira_glsl-transformer 2.0.1
	   |-- org_anarres_jcpp 1.4.14
	   \-- org_antlr_antlr4-runtime 4.13.1
	- java 21
	- jei 19.22.0.315
	- justzoom 2.1.0
	- konkrete 1.9.9
	- lithium 0.15.0+mc1.21.1
	- minecraft 1.21.1
	- modmenu 11.0.3
	- mousetweaks 2.26
	- nvidium 0.3.1
	- placeholder-api 2.4.2+1.21
	- shulkerboxslot 6.0.0+1.21.1
	   \-- spectrelib 0.17.2+1.21
	        |-- com_electronwill_night-config_core 3.8.0
	        \-- com_electronwill_night-config_toml 3.8.0
	- shulkerboxtooltip 5.1.6+1.21.1
	- sodium 0.5.11+mc1.21
	- trinkets 3.10.0
	   |-- cardinal-components-base 6.1.0
	   \-- cardinal-components-entity 6.1.0
	- viafabricplus 3.4.9
	   |-- com_github_oryxel_cubeconverter 5ae1e90e4f
	   |-- com_google_code_findbugs_jsr305 3.0.2
	   |-- com_vdurmont_semver4j 3.1.0
	   |-- com_viaversion_viabackwards-common 5.1.2-SNAPSHOT
	   |-- com_viaversion_viaversion-common 5.1.2-SNAPSHOT
	   |-- de_florianmichael_classic4j 2.1.1-SNAPSHOT
	   |-- io_jsonwebtoken_jjwt-api 0.12.6
	   |-- io_jsonwebtoken_jjwt-gson 0.12.6
	   |-- io_jsonwebtoken_jjwt-impl 0.12.6
	   |-- io_netty_netty-codec-http 4.1.114
	   |-- net_jodah_expiringmap 0.5.10
	   |-- net_lenni0451_commons_httpclient 1.6.0
	   |-- net_lenni0451_mcping 1.4.2
	   |-- net_lenni0451_mcstructs-bedrock_forms 1.2.1
	   |-- net_lenni0451_mcstructs-bedrock_text 1.2.1
	   |-- net_lenni0451_reflect 1.3.4
	   |-- net_raphimc_minecraftauth 4.1.1
	   |-- net_raphimc_viaaprilfools-common 3.0.5-SNAPSHOT
	   |-- net_raphimc_viabedrock 0.0.13-SNAPSHOT
	   |-- net_raphimc_vialegacy 3.0.6-SNAPSHOT
	   |-- net_raphimc_vialoader 3.0.5-SNAPSHOT
	   |-- org_cloudburstmc_netty_netty-transport-raknet 1.0.0.CR3-SNAPSHOT
	   \-- org_lz4_lz4-pure-java 1.8.0
	- xaerominimap 25.2.6
	- xaeroworldmap 1.39.9
[16:29:28] [main/INFO]: SpongePowered MIXIN Subsystem Version=0.8.7 Source=file:/D:/Minecraft_Main/Minecraft_Launchers/ATLauncher/libraries/net/fabricmc/sponge-mixin/0.15.4+mixin.0.8.7/sponge-mixin-0.15.4+mixin.0.8.7.jar Service=Knot/Fabric Env=CLIENT
[16:29:28] [main/INFO]: Compatibility level set to JAVA_21
[16:29:28] [main/INFO]: OptiFine was NOT detected.
[16:29:28] [main/INFO]: OptiFabric was NOT detected.
[16:29:28] [main/INFO]: Option 'mixin.entity.collisions.fluid' requires 'mixin.util.block_tracking=true' but found 'false'. Setting 'mixin.entity.collisions.fluid=false'.
[16:29:28] [main/INFO]: Option 'mixin.experimental.entity.block_caching.block_support' requires 'mixin.util.block_tracking=true' but found 'false'. Setting 'mixin.experimental.entity.block_caching.block_support=false'.
[16:29:28] [main/INFO]: Option 'mixin.experimental.entity.block_caching.fluid_pushing' requires 'mixin.util.block_tracking=true' but found 'false'. Setting 'mixin.experimental.entity.block_caching.fluid_pushing=false'.
[16:29:28] [main/INFO]: Option 'mixin.experimental.entity.block_caching.block_touching' requires 'mixin.util.block_tracking=true' but found 'false'. Setting 'mixin.experimental.entity.block_caching.block_touching=false'.
[16:29:28] [main/INFO]: Option 'mixin.experimental.entity.block_caching.suffocation' requires 'mixin.util.block_tracking=true' but found 'false'. Setting 'mixin.experimental.entity.block_caching.suffocation=false'.
[16:29:28] [main/INFO]: Option 'mixin.experimental.entity.block_caching' requires 'mixin.util.block_tracking=true' but found 'false'. Setting 'mixin.experimental.entity.block_caching=false'.
[16:29:28] [main/INFO]: Loaded configuration file for Lithium: 149 options available, 0 override(s) found
[16:29:28] [main/INFO]: Loaded configuration file for Sodium: 42 options available, 3 override(s) found
[16:29:29] [main/INFO]: bre2el.fpsreducer.mixin.MinecraftClientMixin will be applied.
[16:29:29] [main/INFO]: bre2el.fpsreducer.mixin.KeyboardMixin will be applied.
[16:29:29] [main/INFO]: bre2el.fpsreducer.mixin.RenderSystemMixin will be applied.
[16:29:29] [main/INFO]: bre2el.fpsreducer.mixin.WindowMixin will NOT be applied because OptiFine was NOT detected.
[16:29:29] [main/WARN]: Force-disabling mixin 'features.render.entity.CuboidMixin' as rule 'mixin.features.render.entity' (added by mods [iris]) disables it and children
[16:29:29] [main/WARN]: Force-disabling mixin 'features.render.entity.ModelPartMixin' as rule 'mixin.features.render.entity' (added by mods [iris]) disables it and children
[16:29:29] [main/WARN]: Force-disabling mixin 'features.render.entity.cull.EntityRendererMixin' as rule 'mixin.features.render.entity' (added by mods [iris]) disables it and children
[16:29:29] [main/WARN]: Force-disabling mixin 'features.render.entity.shadows.EntityRenderDispatcherMixin' as rule 'mixin.features.render.entity' (added by mods [iris]) disables it and children
[16:29:29] [main/WARN]: Force-disabling mixin 'features.render.gui.font.GlyphRendererMixin' as rule 'mixin.features.render.gui.font' (added by mods [iris]) disables it and children
[16:29:29] [main/WARN]: Force-disabling mixin 'features.render.world.sky.BackgroundRendererMixin' as rule 'mixin.features.render.world.sky' (added by mods [iris]) disables it and children
[16:29:29] [main/WARN]: Force-disabling mixin 'features.render.world.sky.ClientWorldMixin' as rule 'mixin.features.render.world.sky' (added by mods [iris]) disables it and children
[16:29:29] [main/WARN]: Force-disabling mixin 'features.render.world.sky.WorldRendererMixin' as rule 'mixin.features.render.world.sky' (added by mods [iris]) disables it and children
[16:29:30] [main/INFO]: Searching for graphics cards...
[16:29:30] [main/INFO]: Found graphics adapter: AdapterInfo{vendor=NVIDIA, description='NVIDIA GeForce RTX 3050 Laptop GPU', adapterType=0x00002313, openglIcdFilePath='C:\WINDOWS\System32\DriverStore\FileRepository\nvhmi.inf_amd64_1a65d370d6f288d6\nvoglv64.dll', openglIcdVersion=32.0.15.7270}
[16:29:30] [main/INFO]: Found graphics adapter: AdapterInfo{vendor=INTEL, description='Intel(R) UHD Graphics', adapterType=0x0000232B, openglIcdFilePath='C:\WINDOWS\System32\DriverStore\FileRepository\iigd_dch.inf_amd64_c99c0bc5914694e5\igxelpicd64.dll', openglIcdVersion=31.0.101.5186}
[16:29:30] [main/WARN]: Sodium has applied one or more workarounds to prevent crashes or other issues on your system: [NVIDIA_THREADED_OPTIMIZATIONS]
[16:29:30] [main/WARN]: This is not necessarily an issue, but it may result in certain features or optimizations being disabled. You can sometimes fix these issues by upgrading your graphics driver.
[16:29:30] [main/INFO]: Initializing MixinExtras via com.llamalad7.mixinextras.service.MixinExtrasServiceImpl(version=0.4.1).
[16:29:33] [Datafixer Bootstrap/INFO]: 226 Datafixer optimizations took 400 milliseconds
[16:29:41] [main/INFO]: Completely ignored arguments: []
[16:29:41] [Render thread/INFO]: Environment: Environment[sessionHost=https://sessionserver.mojang.com, servicesHost=https://api.minecraftservices.com, name=PROD]
[16:29:41] [Render thread/INFO]: Setting user: F0meG04
[16:29:42] [Via-Mappingloader-0/INFO]: Loading block connection mappings ...
[16:29:42] [Render thread/INFO]: [JUST ZOOM] Starting version 2.1.0 on Fabric..
[16:29:42] [Render thread/INFO]: [KONKRETE] Loading v1.9.9 in client-side mode on FABRIC!
[16:29:42] [Render thread/INFO]: ---------------------------
[16:29:42] [Render thread/INFO]: KONKRETE SHIPS AND USES THE FOLLOWING LIBRARIES:
[16:29:42] [Render thread/INFO]:  
[16:29:42] [Render thread/INFO]: Open Imaging Copyright © 2014 Dhyan Blum.
[16:29:42] [Render thread/INFO]: Open Imaging is licensed under Apache-2.0.
[16:29:42] [Render thread/INFO]:  
[16:29:42] [Render thread/INFO]: JsonPath Copyright © 2017 Jayway.
[16:29:42] [Render thread/INFO]: JsonPath is licensed under Apache-2.0.
[16:29:42] [Render thread/INFO]:  
[16:29:42] [Render thread/INFO]: Json-smart Copyright © netplex.
[16:29:42] [Render thread/INFO]: Json-smart is licensed under Apache-2.0.
[16:29:42] [Render thread/INFO]:  
[16:29:42] [Render thread/INFO]: Exp4j Copyright © Frank Asseg.
[16:29:42] [Render thread/INFO]: Exp4j is licensed under Apache-2.0. https://github.com/fasseg/exp4j
[16:29:42] [Render thread/INFO]: ---------------------------
[16:29:42] [Render thread/INFO]: [KONKRETE] Server-side modules initialized and ready to use!
[16:29:42] [Via-Mappingloader-0/INFO]: Using FastUtil Long2ObjectOpenHashMap for block connections
[16:29:42] [Render thread/INFO]: [ShulkerBoxTooltip] Could not find configuration file, creating default file
[16:29:43] [ForkJoinPool.commonPool-worker-1/INFO]: Loading translations...
[16:29:43] [ForkJoinPool.commonPool-worker-1/INFO]: Registering protocols...
[16:29:43] [Render thread/INFO]: [Indigo] Different rendering plugin detected; not applying Indigo.
[16:29:43] [Render thread/INFO]: IAS: Booting up... (version: 9.0.4, loader: Fabric, loader version: 0.16.10, game version: 1.21.1)
[16:29:43] [Render thread/INFO]: IAS: Initializing IAS...
[16:29:43] [Render thread/INFO]: IAS: IAS has been loaded.
[16:29:44] [Render thread/INFO]: Sending ConfigManager...
[16:29:44] [Render thread/INFO]: Sending ConfigManager took 3.018 milliseconds
[16:29:44] [Render thread/INFO]: Checking mod updates...
[16:29:44] [Render thread/INFO]: [STDOUT]: [Mouse Tweaks] Main.initialize()
[16:29:44] [Render thread/INFO]: [STDOUT]: [Mouse Tweaks] Initialized.
[16:29:44] [Render thread/INFO]: Loading Xaero's Minimap - Stage 1/2
[16:29:44] [ModMenu/Update Checker/Fabric Loader/INFO]: Update available for 'fabricloader@0.16.10'
[16:29:44] [Render thread/INFO]: Loading Xaero's World Map - Stage 1/2
[16:29:44] [ForkJoinPool.commonPool-worker-1/INFO]: Started resource pack HTTP server on http://127.0.0.1:49850/
[16:29:45] [Worker-Main-1/INFO]: Update available for 'sodium@0.5.11+mc1.21', (-> mc1.21.1-0.6.13-fabric)
[16:29:45] [Worker-Main-1/INFO]: Update available for 'iris@1.7.3+mc1.21', (-> 1.8.8+1.21.1-fabric)
[16:29:45] [ForkJoinPool.commonPool-worker-1/INFO]: ViaVersion detected lowest supported version by the proxy: c0.0.15a-1 (0)
[16:29:45] [ForkJoinPool.commonPool-worker-1/INFO]: Highest supported version by the proxy: 1.21.2-1.21.3 (768)
[16:29:45] [ForkJoinPool.commonPool-worker-1/INFO]: Environment: Environment[sessionHost=https://sessionserver.mojang.com, servicesHost=https://api.minecraftservices.com, name=PROD]
[16:29:47] [ForkJoinPool.commonPool-worker-1/WARN]: [Iris Update Check] This version doesn't have an update index, skipping.
[16:29:47] [Render thread/INFO]: Backend library: LWJGL version 3.3.3-snapshot
[16:29:47] [Render thread/WARN]: Applying workaround: Prevent the NVIDIA OpenGL driver from using broken optimizations (NVIDIA_THREADED_OPTIMIZATIONS)
[16:29:48] [Render thread/INFO]: All capabilities met
[16:29:48] [Render thread/INFO]: Enabling Nvidium
[16:29:49] [Render thread/INFO]: Debug functionality is disabled.
[16:29:49] [Render thread/INFO]: ARB_direct_state_access detected, enabling DSA.
[16:29:49] [Render thread/INFO]: Shaders are disabled because no valid shaderpack is selected
[16:29:50] [Render thread/INFO]: Hardware information:
[16:29:50] [Render thread/INFO]: CPU: 12x 12th Gen Intel(R) Core(TM) i5-12450H
[16:29:50] [Render thread/INFO]: GPU: NVIDIA GeForce RTX 3050 Laptop GPU/PCIe/SSE2 (Supports OpenGL 3.2.0 NVIDIA 572.70)
[16:29:50] [Render thread/INFO]: OS: Windows 11 (10.0)
[16:29:51] [Render thread/INFO]: Reloading ResourceManager: vanilla, fabric, appleskin, bobby, cardinal-components-base, cardinal-components-entity, carpet, cloth-config, com_viaversion_viabackwards-common, com_viaversion_viaversion-common, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-blockrenderlayer-v1, fabric-client-tags-api-v1, fabric-command-api-v1, fabric-command-api-v2, fabric-commands-v0, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-keybindings-v0, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1, fabric-registry-sync-v0, fabric-renderer-api-v1, fabric-renderer-indigo, fabric-renderer-registries-v1, fabric-rendering-data-attachment-v1, fabric-rendering-fluids-v1, fabric-rendering-v0, fabric-rendering-v1, fabric-resource-conditions-api-v1, fabric-resource-loader-v0, fabric-screen-api-v1, fabric-screen-handler-api-v1, fabric-sound-api-v1, fabric-transfer-api-v1, fabric-transitive-access-wideners-v1, fabricloader, fpsreducer, ias, indium, iris, jei, justzoom, konkrete, lithium, modmenu, net_raphimc_viaaprilfools-common, net_raphimc_viabedrock, net_raphimc_vialegacy, nvidium, placeholder-api, shulkerboxslot, shulkerboxtooltip, sodium, spectrelib, trinkets, viafabricplus, xaerominimap, xaeroworldmap
[16:29:51] [Worker-Main-3/INFO]: Found unifont_all_no_pua-15.1.05.hex, loading
[16:29:51] [Render thread/INFO]: [KONKRETE] Client-side modules initialized and ready to use!
[16:29:51] [Worker-Main-11/INFO]: Found unifont_jp_patch-15.1.05.hex, loading
[16:29:51] [Render thread/INFO]: Loading Xaero's World Map - Stage 2/2
[16:29:51] [Render thread/INFO]: New world map region cache hash code: -419401326
[16:29:51] [Render thread/INFO]: Registered player tracker system: map_synced
[16:29:51] [Render thread/INFO]: Xaero's WorldMap Mod: Xaero's minimap found!
[16:29:51] [Render thread/INFO]: Registered player tracker system: minimap_synced
[16:29:51] [Render thread/INFO]: No Optifine!
[16:29:51] [Render thread/INFO]: Xaero's World Map: No Vivecraft!
[16:29:51] [Render thread/INFO]: Xaero's World Map: Iris found!
[16:29:51] [Render thread/INFO]: Loading Xaero's Minimap - Stage 2/2
[16:29:52] [Render thread/INFO]: Registered player tracker system: minimap_synced
[16:29:52] [Render thread/INFO]: Xaero's Minimap: World Map found!
[16:29:52] [Render thread/INFO]: No Optifine!
[16:29:52] [Render thread/INFO]: Xaero's Minimap: No Vivecraft!
[16:29:52] [Render thread/INFO]: Xaero's Minimap: Iris found!
[16:29:53] [Via Async Scheduler 0/INFO]: Finished mapping loading, shutting down loader executor.
[16:29:53] [Render thread/WARN]: Missing sound for event: minecraft:item.goat_horn.play
[16:29:53] [Render thread/WARN]: Missing sound for event: minecraft:entity.goat.screaming.horn_break
[16:29:53] [Render thread/INFO]: OpenAL initialized on device OpenAL Soft on Speaker (2- Realtek(R) Audio)
[16:29:53] [Render thread/INFO]: Sound engine started
[16:29:53] [Render thread/INFO]: Created: 1024x512x4 minecraft:textures/atlas/blocks.png-atlas
[16:29:53] [Render thread/INFO]: Created: 256x256x4 minecraft:textures/atlas/signs.png-atlas
[16:29:53] [Render thread/INFO]: Created: 512x512x4 minecraft:textures/atlas/shield_patterns.png-atlas
[16:29:53] [Render thread/INFO]: Created: 512x512x4 minecraft:textures/atlas/banner_patterns.png-atlas
[16:29:53] [Render thread/INFO]: Created: 1024x1024x4 minecraft:textures/atlas/armor_trims.png-atlas
[16:29:53] [Render thread/INFO]: Created: 128x64x4 minecraft:textures/atlas/decorated_pot.png-atlas
[16:29:53] [Render thread/INFO]: Created: 256x256x4 minecraft:textures/atlas/chest.png-atlas
[16:29:53] [Render thread/INFO]: Created: 512x256x4 minecraft:textures/atlas/shulker_boxes.png-atlas
[16:29:53] [Render thread/INFO]: Created: 512x256x4 minecraft:textures/atlas/beds.png-atlas
[16:29:54] [Render thread/INFO]: Created: 512x256x0 minecraft:textures/atlas/particles.png-atlas
[16:29:54] [Render thread/INFO]: Created: 512x256x0 minecraft:textures/atlas/paintings.png-atlas
[16:29:54] [Render thread/INFO]: Created: 256x128x0 minecraft:textures/atlas/mob_effects.png-atlas
[16:29:54] [Render thread/INFO]: Created: 64x64x0 minecraft:textures/atlas/map_decorations.png-atlas
[16:29:54] [Render thread/INFO]: Created: 1024x512x0 minecraft:textures/atlas/gui.png-atlas
[16:29:54] [Render thread/WARN]: Shader rendertype_entity_translucent_emissive could not find sampler named Sampler2 in the specified shader program.
[16:29:54] [Render thread/INFO]: Created: 256x256x0 jei:textures/atlas/gui.png-atlas
[16:29:54] [Render thread/INFO]: Successfully reloaded the minimap shaders!
[16:29:54] [Render thread/INFO]: Successfully reloaded the world map shaders!
[16:29:54] [Render thread/INFO]: Creating pipeline for dimension minecraft:overworld
[16:29:58] [Render thread/INFO]: Loaded 1290 recipes
[16:29:59] [Render thread/INFO]: Loaded 1401 advancements
[16:29:59] [Render thread/INFO]: Applied 0 biome modifications to 0 of 64 new biomes in 1.541 ms
[16:29:59] [Server thread/INFO]: Starting integrated minecraft server version 1.21.1
[16:29:59] [Server thread/INFO]: Generating keypair
[16:29:59] [Server thread/INFO]: [CM] Loaded 3 settings from carpet.conf
[16:29:59] [Server thread/INFO]: Preparing start region for dimension minecraft:overworld
[16:30:00] [Render thread/INFO]: Preparing spawn area: 0%
[16:30:00] [Render thread/INFO]: Time elapsed: 473 ms
[16:30:00] [Server thread/INFO]: Changing view distance to 12, from 10
[16:30:00] [Server thread/INFO]: Changing simulation distance to 12, from 0
[16:30:03] [Server thread/INFO]: [STDOUT]: Lithium Cached BlockState Flags are disabled!
[16:30:03] [Server thread/INFO]: Ngoc_Cutee[local] logged in with entity id 60 at (8.5, 13.0, 14.5)
[16:30:03] [Server thread/INFO]: Ngoc_Cutee joined the game
[16:30:04] [Server thread/INFO]: Ngoc_Cutee[local] logged in with entity id 66 at (8.5, 13.0, 14.5)
[16:30:04] [Server thread/INFO]: Ngoc_Cutee joined the game
[16:30:04] [Server thread/WARN]: Force-added player with duplicate UUID cf49c072-f5b0-43fb-b3e7-9458855a2652
[16:30:04] [Server thread/INFO]: F0meG04[local:E:e7e14584] logged in with entity id 72 at (8.457762242204888, 13.0, 8.840338644188787)
[16:30:04] [Server thread/INFO]: F0meG04 joined the game
[16:30:04] [Render thread/INFO]: New Xaero hud session initialized!
[16:30:04] [Render thread/INFO]: New world map session initialized!
[16:30:04] [Render thread/INFO]: Reloading pipeline on dimension change: minecraft:overworld => minecraft:overworld
[16:30:04] [Render thread/INFO]: Destroying pipeline minecraft:overworld
[16:30:04] [Render thread/INFO]: Creating pipeline for dimension minecraft:overworld
[16:30:04] [Render thread/INFO]: Started 6 worker threads
[16:30:04] [Render thread/INFO]: [ShulkerBoxTooltip] Server integration enabled, attempting handshake...
[16:30:04] [Server thread/INFO]: [ShulkerBoxTooltip] F0meG04: protocol version: 2.0
[16:30:04] [Server thread/INFO]: [ShulkerBoxTooltip Plugins] Loading 1 plugin: shulkerboxtooltip
[16:30:04] [Render thread/INFO]: Starting JEI...
[16:30:04] [Server thread/INFO]: [ShulkerBoxTooltip Plugins] Registered 31 providers for mod shulkerboxtooltip
[16:30:04] [Render thread/INFO]: Registering item subtypes...
[16:30:04] [Render thread/INFO]: Registering item subtypes took 6.695 milliseconds
[16:30:04] [Render thread/INFO]: Registering fluid subtypes...
[16:30:04] [Render thread/INFO]: Registering fluid subtypes took 1.858 milliseconds
[16:30:04] [Render thread/INFO]: Registering ingredients...
[16:30:05] [Render thread/INFO]: Registering ingredients: jei:minecraft took 110.0 milliseconds
[16:30:05] [Render thread/INFO]: Registering ingredients took 110.8 milliseconds
[16:30:05] [Render thread/INFO]: Registering extra ingredients...
[16:30:05] [Render thread/INFO]: Registering extra ingredients took 313.0 microseconds
[16:30:05] [Render thread/INFO]: Registering search ingredient aliases...
[16:30:05] [Render thread/INFO]: Registering search ingredient aliases took 670.3 microseconds
[16:30:05] [Render thread/INFO]: Registering Mod Info...
[16:30:05] [Render thread/INFO]: Registering Mod Info took 383.6 microseconds
[16:30:05] [Render thread/INFO]: Registering categories...
[16:30:05] [Render thread/INFO]: Registering categories: jei:minecraft took 44.51 milliseconds
[16:30:05] [Render thread/INFO]: Registering categories took 46.37 milliseconds
[16:30:05] [Render thread/INFO]: Registering vanilla category extensions...
[16:30:05] [Render thread/INFO]: Registering vanilla category extensions took 3.786 milliseconds
[16:30:05] [Render thread/INFO]: Registering recipe catalysts...
[16:30:05] [Render thread/INFO]: Registering recipe catalysts took 954.0 microseconds
[16:30:05] [Render thread/INFO]: Building recipe registry...
[16:30:05] [Render thread/INFO]: Building recipe registry took 20.92 milliseconds
[16:30:05] [Render thread/INFO]: Registering advanced plugins...
[16:30:05] [Render thread/INFO]: Registering advanced plugins took 377.7 microseconds
[16:30:05] [Render thread/INFO]: Registering recipes...
[16:30:05] [Render thread/INFO]: Registering recipes: jei:minecraft took 157.0 milliseconds
[16:30:05] [Render thread/INFO]: Registering recipes took 157.8 milliseconds
[16:30:05] [Render thread/INFO]: Registering recipes transfer handlers...
[16:30:05] [Render thread/INFO]: Registering recipes transfer handlers took 3.270 milliseconds
[16:30:05] [Render thread/INFO]: Building runtime...
[16:30:05] [Render thread/INFO]: Registering gui handlers...
[16:30:05] [Render thread/INFO]: Registering gui handlers took 11.06 milliseconds
[16:30:05] [Render thread/INFO]: Registering Runtime...
[16:30:05] [Render thread/INFO]: Starting JEI GUI
[16:30:05] [Render thread/INFO]: Building ingredient list...
[16:30:05] [Render thread/INFO]: Building ingredient list took 28.47 milliseconds
[16:30:05] [Render thread/INFO]: Building ingredient filter...
[16:30:05] [Render thread/INFO]: Adding 1688 ingredients
[16:30:05] [Render thread/INFO]: Added 1688 ingredients
[16:30:05] [Render thread/INFO]: Building ingredient filter took 217.2 milliseconds
[16:30:05] [Render thread/INFO]: Registering Runtime: jei:fabric_gui took 324.9 milliseconds
[16:30:05] [Render thread/INFO]: Registering Runtime took 325.8 milliseconds
[16:30:05] [Render thread/INFO]: Building runtime took 344.7 milliseconds
[16:30:05] [Render thread/INFO]: Sending Runtime...
[16:30:05] [Render thread/INFO]: Sending Runtime took 301.2 microseconds
[16:30:05] [Render thread/INFO]: Starting JEI took 792.4 milliseconds
[16:30:05] [Render thread/INFO]: Loaded 2 advancements
[16:30:05] [Render thread/INFO]: [ShulkerBoxTooltip Plugins] Registered 19 color keys for mod shulkerboxtooltip
[16:30:05] [Render thread/INFO]: [ShulkerBoxTooltip] Handshake succeeded
[16:30:05] [Render thread/INFO]: [ShulkerBoxTooltip] Server protocol version: 2.0
[16:30:05] [Render thread/INFO]: Minimap updated server level id: -1755197486 for world ResourceKey[minecraft:dimension / minecraft:overworld]
[16:30:06] [Render thread/INFO]: Reloading radar icon resources...
[16:30:06] [Render thread/INFO]: Reloaded radar icon resources!
[16:30:07] [Render thread/WARN]: Class xaero.hud.minimap.radar.icon.creator.render.form.model.part.RadarIconModelPartPrerenderer$VertexConsumerWrapper does not support optimized vertex writing code paths, which may cause reduced rendering performance
[16:30:08] [Server thread/INFO]: Loaded 1290 recipes
[16:30:08] [Server thread/INFO]: Loaded 1401 advancements
[16:30:08] [Render thread/INFO]: Stopping JEI
[16:30:08] [Render thread/INFO]: Stopping JEI
[16:30:08] [Render thread/INFO]: Sending Runtime Unavailable...
[16:30:08] [Render thread/INFO]: Stopping JEI GUI
[16:30:08] [Render thread/INFO]: Sending Runtime Unavailable took 710.8 microseconds
[16:30:08] [Render thread/INFO]: Starting JEI...
[16:30:08] [Render thread/INFO]: Registering item subtypes...
[16:30:08] [Render thread/INFO]: Registering item subtypes took 311.0 microseconds
[16:30:08] [Render thread/INFO]: Registering fluid subtypes...
[16:30:08] [Render thread/INFO]: Registering fluid subtypes took 226.4 microseconds
[16:30:08] [Render thread/INFO]: Registering ingredients...
[16:30:08] [Render thread/INFO]: Registering ingredients: jei:minecraft took 15.68 milliseconds
[16:30:08] [Render thread/INFO]: Registering ingredients took 16.36 milliseconds
[16:30:08] [Render thread/INFO]: Registering extra ingredients...
[16:30:08] [Render thread/INFO]: Registering extra ingredients took 301.9 microseconds
[16:30:08] [Render thread/INFO]: Registering search ingredient aliases...
[16:30:08] [Render thread/INFO]: Registering search ingredient aliases took 363.2 microseconds
[16:30:08] [Render thread/INFO]: Registering Mod Info...
[16:30:08] [Render thread/INFO]: Registering Mod Info took 331.8 microseconds
[16:30:08] [Render thread/INFO]: Registering categories...
[16:30:08] [Render thread/INFO]: Registering categories took 738.8 microseconds
[16:30:08] [Render thread/INFO]: Registering vanilla category extensions...
[16:30:08] [Render thread/INFO]: Registering vanilla category extensions took 456.7 microseconds
[16:30:08] [Render thread/INFO]: Registering recipe catalysts...
[16:30:08] [Render thread/INFO]: Registering recipe catalysts took 400.6 microseconds
[16:30:08] [Render thread/INFO]: Building recipe registry...
[16:30:08] [Render thread/INFO]: Building recipe registry took 462.0 microseconds
[16:30:08] [Render thread/INFO]: Registering advanced plugins...
[16:30:08] [Render thread/INFO]: Registering advanced plugins took 388.9 microseconds
[16:30:08] [Render thread/INFO]: Registering recipes...
[16:30:08] [Render thread/INFO]: Registering recipes: jei:minecraft took 56.02 milliseconds
[16:30:08] [Render thread/INFO]: Registering recipes took 56.91 milliseconds
[16:30:08] [Render thread/INFO]: Registering recipes transfer handlers...
[16:30:08] [Render thread/INFO]: Registering recipes transfer handlers took 499.9 microseconds
[16:30:08] [Render thread/INFO]: Building runtime...
[16:30:08] [Render thread/INFO]: Registering gui handlers...
[16:30:08] [Render thread/INFO]: Registering gui handlers took 346.5 microseconds
[16:30:08] [Render thread/INFO]: Registering Runtime...
[16:30:08] [Render thread/INFO]: Starting JEI GUI
[16:30:08] [Render thread/INFO]: Building ingredient list...
[16:30:08] [Render thread/INFO]: Building ingredient list took 12.70 milliseconds
[16:30:08] [Render thread/INFO]: Building ingredient filter...
[16:30:08] [Render thread/INFO]: Adding 1688 ingredients
[16:30:08] [Render thread/INFO]: Added 1688 ingredients
[16:30:08] [Render thread/INFO]: Building ingredient filter took 102.0 milliseconds
[16:30:08] [Render thread/INFO]: Registering Runtime: jei:fabric_gui took 121.3 milliseconds
[16:30:08] [Render thread/INFO]: Registering Runtime took 122.1 milliseconds
[16:30:08] [Render thread/INFO]: Building runtime took 123.3 milliseconds
[16:30:08] [Render thread/INFO]: Sending Runtime...
[16:30:08] [Render thread/INFO]: Sending Runtime took 336.7 microseconds
[16:30:08] [Render thread/INFO]: Starting JEI took 210.4 milliseconds
[16:30:08] [Render thread/INFO]: Loaded 2 advancements
[16:30:12] [Server thread/INFO]: [F0meG04: Killed Ngoc_Cutee]
[16:30:12] [Server thread/INFO]: Ngoc_Cutee lost connection: Killed
[16:30:12] [Server thread/INFO]: Ngoc_Cutee left the game
[16:30:12] [Render thread/INFO]: [System] [CHAT] Killed Ngoc_Cutee
[16:30:12] [Render thread/INFO]: [System] [CHAT] Ngoc_Cutee left the game
[16:30:15] [Server thread/INFO]: Loaded 1290 recipes
[16:30:15] [Server thread/INFO]: Loaded 1401 advancements
[16:30:15] [Render thread/INFO]: Stopping JEI
[16:30:15] [Render thread/INFO]: Stopping JEI
[16:30:15] [Render thread/INFO]: Sending Runtime Unavailable...
[16:30:15] [Render thread/INFO]: Stopping JEI GUI
[16:30:15] [Render thread/INFO]: Sending Runtime Unavailable took 541.7 microseconds
[16:30:15] [Render thread/INFO]: Starting JEI...
[16:30:15] [Render thread/INFO]: Registering item subtypes...
[16:30:15] [Render thread/INFO]: Registering item subtypes took 225.5 microseconds
[16:30:15] [Render thread/INFO]: Registering fluid subtypes...
[16:30:15] [Render thread/INFO]: Registering fluid subtypes took 172.7 microseconds
[16:30:15] [Render thread/INFO]: Registering ingredients...
[16:30:15] [Render thread/INFO]: Registering ingredients took 9.170 milliseconds
[16:30:15] [Render thread/INFO]: Registering extra ingredients...
[16:30:15] [Render thread/INFO]: Registering extra ingredients took 241.8 microseconds
[16:30:15] [Render thread/INFO]: Registering search ingredient aliases...
[16:30:15] [Render thread/INFO]: Registering search ingredient aliases took 257.5 microseconds
[16:30:15] [Render thread/INFO]: Registering Mod Info...
[16:30:15] [Render thread/INFO]: Registering Mod Info took 275.2 microseconds
[16:30:15] [Render thread/INFO]: Registering categories...
[16:30:15] [Render thread/INFO]: Registering categories took 626.6 microseconds
[16:30:15] [Render thread/INFO]: Registering vanilla category extensions...
[16:30:15] [Render thread/INFO]: Registering vanilla category extensions took 265.9 microseconds
[16:30:15] [Render thread/INFO]: Registering recipe catalysts...
[16:30:15] [Render thread/INFO]: Registering recipe catalysts took 239.1 microseconds
[16:30:15] [Render thread/INFO]: Building recipe registry...
[16:30:15] [Render thread/INFO]: Building recipe registry took 254.0 microseconds
[16:30:15] [Render thread/INFO]: Registering advanced plugins...
[16:30:15] [Render thread/INFO]: Registering advanced plugins took 207.6 microseconds
[16:30:15] [Render thread/INFO]: Registering recipes...
[16:30:15] [Render thread/INFO]: Registering recipes: jei:minecraft took 37.32 milliseconds
[16:30:15] [Render thread/INFO]: Registering recipes took 37.93 milliseconds
[16:30:15] [Render thread/INFO]: Registering recipes transfer handlers...
[16:30:15] [Render thread/INFO]: Registering recipes transfer handlers took 356.2 microseconds
[16:30:15] [Render thread/INFO]: Building runtime...
[16:30:15] [Render thread/INFO]: Registering gui handlers...
[16:30:15] [Render thread/INFO]: Registering gui handlers took 321.5 microseconds
[16:30:15] [Render thread/INFO]: Registering Runtime...
[16:30:15] [Render thread/INFO]: Starting JEI GUI
[16:30:15] [Render thread/INFO]: Building ingredient list...
[16:30:15] [Render thread/INFO]: Building ingredient list took 9.369 milliseconds
[16:30:15] [Render thread/INFO]: Building ingredient filter...
[16:30:15] [Render thread/INFO]: Adding 1688 ingredients
[16:30:15] [Render thread/INFO]: Added 1688 ingredients
[16:30:15] [Render thread/INFO]: Building ingredient filter took 57.16 milliseconds
[16:30:15] [Render thread/INFO]: Registering Runtime: jei:fabric_gui took 71.74 milliseconds
[16:30:15] [Render thread/INFO]: Registering Runtime took 72.19 milliseconds
[16:30:15] [Render thread/INFO]: Building runtime took 73.01 milliseconds
[16:30:15] [Render thread/INFO]: Sending Runtime...
[16:30:15] [Render thread/INFO]: Sending Runtime took 239.0 microseconds
[16:30:15] [Render thread/INFO]: Starting JEI took 130.0 milliseconds
[16:30:15] [Render thread/INFO]: Loaded 2 advancements
[16:30:17] [Render thread/WARN]: Ignoring player info update for unknown player cf49c072-f5b0-43fb-b3e7-9458855a2652 ([UPDATE_GAME_MODE])
[16:30:17] [Render thread/INFO]: [System] [CHAT] Your game mode has been updated to Survival Mode
[16:30:30] [Render thread/WARN]: Ignoring player info update for unknown player cf49c072-f5b0-43fb-b3e7-9458855a2652 ([UPDATE_LATENCY])
[16:31:00] [Render thread/WARN]: Ignoring player info update for unknown player cf49c072-f5b0-43fb-b3e7-9458855a2652 ([UPDATE_LATENCY])
[16:31:30] [Render thread/WARN]: Ignoring player info update for unknown player cf49c072-f5b0-43fb-b3e7-9458855a2652 ([UPDATE_LATENCY])
[16:32:00] [Render thread/WARN]: Ignoring player info update for unknown player cf49c072-f5b0-43fb-b3e7-9458855a2652 ([UPDATE_LATENCY])
[16:32:30] [Render thread/WARN]: Ignoring player info update for unknown player cf49c072-f5b0-43fb-b3e7-9458855a2652 ([UPDATE_LATENCY])
[16:32:49] [Server thread/INFO]: [F0meG04: Killed F0meG04]
[16:32:49] [Render thread/INFO]: [System] [CHAT] Killed F0meG04
[16:32:49] [Render thread/INFO]: Minimap updated server level id: -1755197486 for world ResourceKey[minecraft:dimension / minecraft:overworld]
[16:32:49] [Render thread/WARN]: Ignoring player info update for unknown player cf49c072-f5b0-43fb-b3e7-9458855a2652 ([UPDATE_GAME_MODE])
[16:32:49] [Render thread/INFO]: [System] [CHAT] <Ngoc_Cutee> gg ez
[16:32:49] [Render thread/WARN]: Server attempted to add player prior to sending player info (Player id cf49c072-f5b0-43fb-b3e7-9458855a2652)
[16:32:49] [Render thread/WARN]: Skipping Entity with id entity.minecraft.player
[16:32:51] [Server thread/INFO]: Loaded 1290 recipes
[16:32:51] [Server thread/INFO]: Loaded 1401 advancements
[16:32:52] [Render thread/INFO]: Stopping JEI
[16:32:52] [Render thread/INFO]: Stopping JEI
[16:32:52] [Render thread/INFO]: Sending Runtime Unavailable...
[16:32:52] [Render thread/INFO]: Stopping JEI GUI
[16:32:52] [Render thread/INFO]: Sending Runtime Unavailable took 545.7 microseconds
[16:32:52] [Render thread/INFO]: Starting JEI...
[16:32:52] [Render thread/INFO]: Registering item subtypes...
[16:32:52] [Render thread/INFO]: Registering item subtypes took 289.6 microseconds
[16:32:52] [Render thread/INFO]: Registering fluid subtypes...
[16:32:52] [Render thread/INFO]: Registering fluid subtypes took 277.5 microseconds
[16:32:52] [Render thread/INFO]: Registering ingredients...
[16:32:52] [Render thread/INFO]: Registering ingredients took 8.869 milliseconds
[16:32:52] [Render thread/INFO]: Registering extra ingredients...
[16:32:52] [Render thread/INFO]: Registering extra ingredients took 359.8 microseconds
[16:32:52] [Render thread/INFO]: Registering search ingredient aliases...
[16:32:52] [Render thread/INFO]: Registering search ingredient aliases took 302.1 microseconds
[16:32:52] [Render thread/INFO]: Registering Mod Info...
[16:32:52] [Render thread/INFO]: Registering Mod Info took 288.1 microseconds
[16:32:52] [Render thread/INFO]: Registering categories...
[16:32:52] [Render thread/INFO]: Registering categories took 478.9 microseconds
[16:32:52] [Render thread/INFO]: Registering vanilla category extensions...
[16:32:52] [Render thread/INFO]: Registering vanilla category extensions took 204.7 microseconds
[16:32:52] [Render thread/INFO]: Registering recipe catalysts...
[16:32:52] [Render thread/INFO]: Registering recipe catalysts took 169.0 microseconds
[16:32:52] [Render thread/INFO]: Building recipe registry...
[16:32:52] [Render thread/INFO]: Building recipe registry took 275.7 microseconds
[16:32:52] [Render thread/INFO]: Registering advanced plugins...
[16:32:52] [Render thread/INFO]: Registering advanced plugins took 215.1 microseconds
[16:32:52] [Render thread/INFO]: Registering recipes...
[16:32:52] [Render thread/INFO]: Registering recipes: jei:minecraft took 31.54 milliseconds
[16:32:52] [Render thread/INFO]: Registering recipes took 32.08 milliseconds
[16:32:52] [Render thread/INFO]: Registering recipes transfer handlers...
[16:32:52] [Render thread/INFO]: Registering recipes transfer handlers took 388.7 microseconds
[16:32:52] [Render thread/INFO]: Building runtime...
[16:32:52] [Render thread/INFO]: Registering gui handlers...
[16:32:52] [Render thread/INFO]: Registering gui handlers took 347.8 microseconds
[16:32:52] [Render thread/INFO]: Registering Runtime...
[16:32:52] [Render thread/INFO]: Starting JEI GUI
[16:32:52] [Render thread/INFO]: Building ingredient list...
[16:32:52] [Render thread/INFO]: Building ingredient list took 8.752 milliseconds
[16:32:52] [Render thread/INFO]: Building ingredient filter...
[16:32:52] [Render thread/INFO]: Adding 1688 ingredients
[16:32:52] [Render thread/INFO]: Added 1688 ingredients
[16:32:52] [Render thread/INFO]: Building ingredient filter took 51.93 milliseconds
[16:32:52] [Render thread/INFO]: Registering Runtime: jei:fabric_gui took 91.42 milliseconds
[16:32:52] [Render thread/INFO]: Registering Runtime took 92.01 milliseconds
[16:32:52] [Render thread/INFO]: Building runtime took 92.99 milliseconds
[16:32:52] [Render thread/INFO]: Sending Runtime...
[16:32:52] [Render thread/INFO]: Sending Runtime took 283.7 microseconds
[16:32:52] [Render thread/INFO]: Starting JEI took 144.1 milliseconds
[16:32:52] [Render thread/INFO]: Loaded 2 advancements
[16:32:55] [Server thread/INFO]: [F0meG04: Killed F0meG04]
[16:32:55] [Render thread/INFO]: [System] [CHAT] Killed F0meG04
[16:32:55] [Render thread/INFO]: Minimap updated server level id: -1755197486 for world ResourceKey[minecraft:dimension / minecraft:overworld]
[16:32:56] [Render thread/INFO]: [System] [CHAT] <Ngoc_Cutee> gg ez
[16:32:56] [Server thread/INFO]: Saving and pausing game...
[16:32:56] [Server thread/INFO]: Saving chunks for level 'ServerLevel[§6Theobald's PVP Practice 1.3 §7- §a§l1.21]'/minecraft:overworld
[16:32:56] [Render thread/WARN]: Server attempted to add player prior to sending player info (Player id cf49c072-f5b0-43fb-b3e7-9458855a2652)
[16:32:56] [Render thread/WARN]: Skipping Entity with id entity.minecraft.player
[16:32:56] [Server thread/INFO]: Saving chunks for level 'ServerLevel[§6Theobald's PVP Practice 1.3 §7- §a§l1.21]'/minecraft:the_nether
[16:32:56] [Server thread/INFO]: Saving chunks for level 'ServerLevel[§6Theobald's PVP Practice 1.3 §7- §a§l1.21]'/minecraft:the_end
[16:32:56] [Render thread/INFO]: Xaero hud session finalized.
[16:32:56] [Render thread/INFO]: Finalizing world map session...
[16:32:56] [Thread-6/INFO]: World map force-cleaned!
[16:32:56] [Render thread/INFO]: World map session finalized.
[16:32:56] [Server thread/INFO]: F0meG04 lost connection: Disconnected
[16:32:56] [Server thread/INFO]: F0meG04 left the game
[16:32:56] [Server thread/INFO]: Stopping singleplayer server as player logged out
[16:32:57] [Server thread/INFO]: Stopping server
[16:32:57] [Server thread/INFO]: Saving players
[16:32:57] [Server thread/INFO]: Saving worlds
[16:32:57] [Server thread/INFO]: Saving chunks for level 'ServerLevel[§6Theobald's PVP Practice 1.3 §7- §a§l1.21]'/minecraft:overworld
[16:32:57] [Server thread/INFO]: Saving chunks for level 'ServerLevel[§6Theobald's PVP Practice 1.3 §7- §a§l1.21]'/minecraft:the_nether
[16:32:57] [Server thread/INFO]: Saving chunks for level 'ServerLevel[§6Theobald's PVP Practice 1.3 §7- §a§l1.21]'/minecraft:the_end
[16:32:57] [Server thread/INFO]: ThreadedAnvilChunkStorage (Theobald's PVP Practice 1.3): All chunks are saved
[16:32:57] [Server thread/INFO]: ThreadedAnvilChunkStorage (DIM-1): All chunks are saved
[16:32:57] [Server thread/INFO]: ThreadedAnvilChunkStorage (DIM1): All chunks are saved
[16:32:57] [Server thread/INFO]: ThreadedAnvilChunkStorage: All dimensions are saved
[16:32:57] [Render thread/INFO]: Stopping worker threads
[16:32:58] [Render thread/INFO]: Loaded 1290 recipes
[16:32:59] [Render thread/INFO]: Loaded 1401 advancements
[16:32:59] [Render thread/INFO]: Stopping JEI
[16:32:59] [Render thread/INFO]: Stopping JEI
[16:32:59] [Render thread/INFO]: Sending Runtime Unavailable...
[16:32:59] [Render thread/INFO]: Stopping JEI GUI
[16:32:59] [Render thread/INFO]: Sending Runtime Unavailable took 530.0 microseconds
[16:32:59] [Render thread/ERROR]: Failed to start JEI, there is no Minecraft client level.
[16:32:59] [Render thread/INFO]: Applied 0 biome modifications to 0 of 64 new biomes in 392.6 μs
[16:32:59] [Server thread/INFO]: Starting integrated minecraft server version 1.21.1
[16:32:59] [Server thread/INFO]: Generating keypair
[16:32:59] [Server thread/INFO]: [CM] Loaded 3 settings from carpet.conf
[16:32:59] [Server thread/INFO]: Preparing start region for dimension minecraft:overworld
[16:32:59] [Render thread/INFO]: Preparing spawn area: 0%
[16:32:59] [Render thread/INFO]: Time elapsed: 159 ms
[16:32:59] [Server thread/INFO]: Changing view distance to 12, from 10
[16:32:59] [Server thread/INFO]: Changing simulation distance to 12, from 0
[16:33:02] [Server thread/INFO]: Ngoc_Cutee[local] logged in with entity id 332 at (8.5, 13.0, 14.5)
[16:33:02] [Server thread/INFO]: Ngoc_Cutee joined the game
[16:33:02] [Server thread/INFO]: Ngoc_Cutee[local] logged in with entity id 338 at (8.5, 13.0, 14.5)
[16:33:02] [Server thread/INFO]: Ngoc_Cutee joined the game
[16:33:02] [Server thread/WARN]: Force-added player with duplicate UUID cf49c072-f5b0-43fb-b3e7-9458855a2652
[16:33:02] [Server thread/WARN]: Can't keep up! Is the server overloaded? Running 2686ms or 53 ticks behind
[16:33:02] [Server thread/INFO]: F0meG04[local:E:3d60f400] logged in with entity id 344 at (8.5, 13.0, 8.5)
[16:33:02] [Server thread/INFO]: F0meG04 joined the game
[16:33:02] [Render thread/INFO]: New Xaero hud session initialized!
[16:33:02] [Render thread/INFO]: New world map session initialized!
[16:33:02] [Render thread/INFO]: Reloading pipeline on dimension change: minecraft:overworld => minecraft:overworld
[16:33:02] [Render thread/INFO]: Destroying pipeline minecraft:overworld
[16:33:02] [Render thread/INFO]: Creating pipeline for dimension minecraft:overworld
[16:33:02] [Render thread/INFO]: Started 6 worker threads
[16:33:02] [Render thread/INFO]: [ShulkerBoxTooltip] Server integration enabled, attempting handshake...
[16:33:02] [Server thread/INFO]: [ShulkerBoxTooltip] F0meG04: protocol version: 2.0
[16:33:02] [Render thread/INFO]: Starting JEI...
[16:33:02] [Render thread/INFO]: Registering item subtypes...
[16:33:02] [Render thread/INFO]: Registering item subtypes took 325.4 microseconds
[16:33:02] [Render thread/INFO]: Registering fluid subtypes...
[16:33:02] [Render thread/INFO]: Registering fluid subtypes took 272.7 microseconds
[16:33:02] [Render thread/INFO]: Registering ingredients...
[16:33:02] [Render thread/INFO]: Registering ingredients took 8.890 milliseconds
[16:33:02] [Render thread/INFO]: Registering extra ingredients...
[16:33:02] [Render thread/INFO]: Registering extra ingredients took 297.5 microseconds
[16:33:02] [Render thread/INFO]: Registering search ingredient aliases...
[16:33:02] [Render thread/INFO]: Registering search ingredient aliases took 337.5 microseconds
[16:33:02] [Render thread/INFO]: Registering Mod Info...
[16:33:02] [Render thread/INFO]: Registering Mod Info took 353.9 microseconds
[16:33:02] [Render thread/INFO]: Registering categories...
[16:33:02] [Render thread/INFO]: Registering categories took 728.0 microseconds
[16:33:02] [Render thread/INFO]: Registering vanilla category extensions...
[16:33:02] [Render thread/INFO]: Registering vanilla category extensions took 287.9 microseconds
[16:33:02] [Render thread/INFO]: Registering recipe catalysts...
[16:33:02] [Render thread/INFO]: Registering recipe catalysts took 313.4 microseconds
[16:33:02] [Render thread/INFO]: Building recipe registry...
[16:33:02] [Render thread/INFO]: Building recipe registry took 250.9 microseconds
[16:33:02] [Render thread/INFO]: Registering advanced plugins...
[16:33:02] [Render thread/INFO]: Registering advanced plugins took 285.9 microseconds
[16:33:02] [Render thread/INFO]: Registering recipes...
[16:33:02] [Render thread/INFO]: Registering recipes: jei:minecraft took 33.05 milliseconds
[16:33:02] [Render thread/INFO]: Registering recipes took 33.93 milliseconds
[16:33:02] [Render thread/INFO]: Registering recipes transfer handlers...
[16:33:02] [Render thread/INFO]: Registering recipes transfer handlers took 430.9 microseconds
[16:33:02] [Render thread/INFO]: Building runtime...
[16:33:02] [Render thread/INFO]: Registering gui handlers...
[16:33:02] [Render thread/INFO]: Registering gui handlers took 336.4 microseconds
[16:33:02] [Render thread/INFO]: Registering Runtime...
[16:33:02] [Render thread/INFO]: Starting JEI GUI
[16:33:02] [Render thread/INFO]: Building ingredient list...
[16:33:02] [Render thread/INFO]: Building ingredient list took 10.65 milliseconds
[16:33:02] [Render thread/INFO]: Building ingredient filter...
[16:33:02] [Render thread/INFO]: Adding 1688 ingredients
[16:33:02] [Render thread/INFO]: Added 1688 ingredients
[16:33:02] [Render thread/INFO]: Building ingredient filter took 94.31 milliseconds
[16:33:02] [Render thread/INFO]: Registering Runtime: jei:fabric_gui took 111.0 milliseconds
[16:33:02] [Render thread/INFO]: Registering Runtime took 111.8 milliseconds
[16:33:02] [Render thread/INFO]: Building runtime took 112.8 milliseconds
[16:33:02] [Render thread/INFO]: Sending Runtime...
[16:33:02] [Render thread/INFO]: Sending Runtime took 370.9 microseconds
[16:33:02] [Render thread/INFO]: Starting JEI took 167.0 milliseconds
[16:33:02] [Render thread/INFO]: Stopping JEI
[16:33:02] [Render thread/INFO]: Stopping JEI
[16:33:02] [Render thread/INFO]: Sending Runtime Unavailable...
[16:33:02] [Render thread/INFO]: Stopping JEI GUI
[16:33:02] [Render thread/INFO]: Sending Runtime Unavailable took 404.0 microseconds
[16:33:02] [Render thread/INFO]: Starting JEI...
[16:33:02] [Render thread/INFO]: Registering item subtypes...
[16:33:02] [Render thread/INFO]: Registering item subtypes took 272.0 microseconds
[16:33:02] [Render thread/INFO]: Registering fluid subtypes...
[16:33:02] [Render thread/INFO]: Registering fluid subtypes took 204.9 microseconds
[16:33:02] [Render thread/INFO]: Registering ingredients...
[16:33:02] [Render thread/INFO]: Registering ingredients took 8.326 milliseconds
[16:33:02] [Render thread/INFO]: Registering extra ingredients...
[16:33:02] [Render thread/INFO]: Registering extra ingredients took 363.5 microseconds
[16:33:02] [Render thread/INFO]: Registering search ingredient aliases...
[16:33:02] [Render thread/INFO]: Registering search ingredient aliases took 323.5 microseconds
[16:33:02] [Render thread/INFO]: Registering Mod Info...
[16:33:02] [Render thread/INFO]: Registering Mod Info took 419.9 microseconds
[16:33:02] [Render thread/INFO]: Registering categories...
[16:33:02] [Render thread/INFO]: Registering categories took 522.0 microseconds
[16:33:02] [Render thread/INFO]: Registering vanilla category extensions...
[16:33:02] [Render thread/INFO]: Registering vanilla category extensions took 461.9 microseconds
[16:33:02] [Render thread/INFO]: Registering recipe catalysts...
[16:33:02] [Render thread/INFO]: Registering recipe catalysts took 365.9 microseconds
[16:33:02] [Render thread/INFO]: Building recipe registry...
[16:33:02] [Render thread/INFO]: Building recipe registry took 249.0 microseconds
[16:33:02] [Render thread/INFO]: Registering advanced plugins...
[16:33:02] [Render thread/INFO]: Registering advanced plugins took 417.4 microseconds
[16:33:02] [Render thread/INFO]: Registering recipes...
[16:33:02] [Render thread/INFO]: Registering recipes: jei:minecraft took 29.73 milliseconds
[16:33:02] [Render thread/INFO]: Registering recipes took 30.80 milliseconds
[16:33:02] [Render thread/INFO]: Registering recipes transfer handlers...
[16:33:02] [Render thread/INFO]: Registering recipes transfer handlers took 265.8 microseconds
[16:33:02] [Render thread/INFO]: Building runtime...
[16:33:02] [Render thread/INFO]: Registering gui handlers...
[16:33:02] [Render thread/INFO]: Registering gui handlers took 317.0 microseconds
[16:33:02] [Render thread/INFO]: Registering Runtime...
[16:33:02] [Render thread/INFO]: Starting JEI GUI
[16:33:02] [Render thread/INFO]: Building ingredient list...
[16:33:02] [Render thread/INFO]: Building ingredient list took 7.533 milliseconds
[16:33:02] [Render thread/INFO]: Building ingredient filter...
[16:33:02] [Render thread/INFO]: Adding 1688 ingredients
[16:33:02] [Render thread/INFO]: Added 1688 ingredients
[16:33:02] [Render thread/INFO]: Building ingredient filter took 65.94 milliseconds
[16:33:02] [Render thread/INFO]: Registering Runtime: jei:fabric_gui took 78.58 milliseconds
[16:33:02] [Render thread/INFO]: Registering Runtime took 79.32 milliseconds
[16:33:02] [Render thread/INFO]: Building runtime took 80.19 milliseconds
[16:33:02] [Render thread/INFO]: Sending Runtime...
[16:33:02] [Render thread/INFO]: Sending Runtime took 417.7 microseconds
[16:33:02] [Render thread/INFO]: Starting JEI took 130.8 milliseconds
[16:33:02] [Render thread/INFO]: Loaded 2 advancements
[16:33:02] [Render thread/INFO]: [ShulkerBoxTooltip] Handshake succeeded
[16:33:02] [Render thread/INFO]: [ShulkerBoxTooltip] Server protocol version: 2.0
[16:33:02] [Render thread/INFO]: Minimap updated server level id: -1755197486 for world ResourceKey[minecraft:dimension / minecraft:overworld]
[16:33:03] [Server thread/INFO]: Saving and pausing game...
[16:33:03] [Server thread/INFO]: Saving chunks for level 'ServerLevel[§6Theobald's PVP Practice 1.3 §7- §a§l1.21]'/minecraft:overworld
[16:33:03] [Server thread/INFO]: Saving chunks for level 'ServerLevel[§6Theobald's PVP Practice 1.3 §7- §a§l1.21]'/minecraft:the_nether
[16:33:03] [Server thread/INFO]: Saving chunks for level 'ServerLevel[§6Theobald's PVP Practice 1.3 §7- §a§l1.21]'/minecraft:the_end
[16:33:15] [Render thread/INFO]: Xaero hud session finalized.
[16:33:15] [Render thread/INFO]: Finalizing world map session...
[16:33:15] [Thread-6/INFO]: World map force-cleaned!
[16:33:15] [Render thread/INFO]: World map session finalized.
[16:33:15] [Server thread/INFO]: F0meG04 lost connection: Disconnected
[16:33:15] [Server thread/INFO]: F0meG04 left the game
[16:33:15] [Server thread/INFO]: Stopping singleplayer server as player logged out
[16:33:15] [Server thread/INFO]: Stopping server
[16:33:15] [Server thread/INFO]: Saving players
[16:33:15] [Server thread/INFO]: Saving worlds
[16:33:15] [Server thread/INFO]: Saving chunks for level 'ServerLevel[§6Theobald's PVP Practice 1.3 §7- §a§l1.21]'/minecraft:overworld
[16:33:15] [Server thread/INFO]: Saving chunks for level 'ServerLevel[§6Theobald's PVP Practice 1.3 §7- §a§l1.21]'/minecraft:the_nether
[16:33:15] [Server thread/INFO]: Saving chunks for level 'ServerLevel[§6Theobald's PVP Practice 1.3 §7- §a§l1.21]'/minecraft:the_end
[16:33:15] [Server thread/INFO]: ThreadedAnvilChunkStorage (Theobald's PVP Practice 1.3): All chunks are saved
[16:33:15] [Server thread/INFO]: ThreadedAnvilChunkStorage (DIM-1): All chunks are saved
[16:33:15] [Server thread/INFO]: ThreadedAnvilChunkStorage (DIM1): All chunks are saved
[16:33:15] [Server thread/INFO]: ThreadedAnvilChunkStorage: All dimensions are saved
[16:33:15] [Render thread/INFO]: Stopping worker threads
[16:33:26] [Render thread/INFO]: Loaded 1290 recipes
[16:33:26] [Render thread/INFO]: Loaded 1401 advancements
[16:33:26] [Render thread/INFO]: Stopping JEI
[16:33:26] [Render thread/INFO]: Stopping JEI
[16:33:26] [Render thread/INFO]: Sending Runtime Unavailable...
[16:33:26] [Render thread/INFO]: Stopping JEI GUI
[16:33:26] [Render thread/INFO]: Sending Runtime Unavailable took 581.8 microseconds
[16:33:26] [Render thread/ERROR]: Failed to start JEI, there is no Minecraft client level.
[16:33:27] [Render thread/INFO]: Applied 0 biome modifications to 0 of 64 new biomes in 357.0 μs
[16:33:27] [Server thread/INFO]: Starting integrated minecraft server version 1.21.1
[16:33:27] [Server thread/INFO]: Generating keypair
[16:33:27] [Server thread/INFO]: [CM] Loaded 3 settings from carpet.conf
[16:33:27] [Server thread/INFO]: Preparing start region for dimension minecraft:overworld
[16:33:27] [Render thread/INFO]: Preparing spawn area: 0%
[16:33:27] [Server thread/INFO]: Changing view distance to 12, from 10
[16:33:27] [Server thread/INFO]: Changing simulation distance to 12, from 0
[16:33:27] [Render thread/INFO]: Time elapsed: 113 ms
[16:33:27] [Server thread/INFO]: F0meG04[local:E:63fef2dc] logged in with entity id 463 at (8.5, 13.0, 8.5)
[16:33:27] [Server thread/INFO]: F0meG04 joined the game
[16:33:27] [Render thread/INFO]: New Xaero hud session initialized!
[16:33:27] [Render thread/INFO]: New world map session initialized!
[16:33:27] [Render thread/INFO]: Reloading pipeline on dimension change: minecraft:overworld => minecraft:overworld
[16:33:27] [Render thread/INFO]: Destroying pipeline minecraft:overworld
[16:33:27] [Render thread/INFO]: Creating pipeline for dimension minecraft:overworld
[16:33:27] [Render thread/INFO]: Started 6 worker threads
[16:33:27] [Render thread/INFO]: [ShulkerBoxTooltip] Server integration enabled, attempting handshake...
[16:33:27] [Render thread/INFO]: Starting JEI...
[16:33:27] [Render thread/INFO]: Registering item subtypes...
[16:33:27] [Render thread/INFO]: Registering item subtypes took 439.4 microseconds
[16:33:27] [Render thread/INFO]: Registering fluid subtypes...
[16:33:27] [Render thread/INFO]: Registering fluid subtypes took 346.0 microseconds
[16:33:27] [Render thread/INFO]: Registering ingredients...
[16:33:27] [Render thread/INFO]: Registering ingredients took 9.206 milliseconds
[16:33:27] [Render thread/INFO]: Registering extra ingredients...
[16:33:27] [Render thread/INFO]: Registering extra ingredients took 316.5 microseconds
[16:33:27] [Render thread/INFO]: Registering search ingredient aliases...
[16:33:27] [Render thread/INFO]: Registering search ingredient aliases took 269.0 microseconds
[16:33:27] [Render thread/INFO]: Registering Mod Info...
[16:33:27] [Render thread/INFO]: Registering Mod Info took 296.5 microseconds
[16:33:27] [Render thread/INFO]: Registering categories...
[16:33:27] [Render thread/INFO]: Registering categories took 563.0 microseconds
[16:33:27] [Render thread/INFO]: Registering vanilla category extensions...
[16:33:27] [Render thread/INFO]: Registering vanilla category extensions took 328.8 microseconds
[16:33:27] [Render thread/INFO]: Registering recipe catalysts...
[16:33:27] [Render thread/INFO]: Registering recipe catalysts took 293.6 microseconds
[16:33:27] [Render thread/INFO]: Building recipe registry...
[16:33:27] [Render thread/INFO]: Building recipe registry took 248.9 microseconds
[16:33:27] [Render thread/INFO]: Registering advanced plugins...
[16:33:27] [Render thread/INFO]: Registering advanced plugins took 343.7 microseconds
[16:33:27] [Render thread/INFO]: Registering recipes...
[16:33:27] [Render thread/INFO]: Registering recipes: jei:minecraft took 27.85 milliseconds
[16:33:27] [Render thread/INFO]: Registering recipes took 28.52 milliseconds
[16:33:27] [Render thread/INFO]: Registering recipes transfer handlers...
[16:33:27] [Render thread/INFO]: Registering recipes transfer handlers took 327.3 microseconds
[16:33:27] [Render thread/INFO]: Building runtime...
[16:33:27] [Render thread/INFO]: Registering gui handlers...
[16:33:27] [Render thread/INFO]: Registering gui handlers took 309.0 microseconds
[16:33:27] [Render thread/INFO]: Registering Runtime...
[16:33:27] [Render thread/INFO]: Starting JEI GUI
[16:33:27] [Render thread/INFO]: Building ingredient list...
[16:33:27] [Render thread/INFO]: Building ingredient list took 7.600 milliseconds
[16:33:27] [Render thread/INFO]: Building ingredient filter...
[16:33:27] [Render thread/INFO]: Adding 1688 ingredients
[16:33:27] [Render thread/INFO]: Added 1688 ingredients
[16:33:27] [Render thread/INFO]: Building ingredient filter took 62.66 milliseconds
[16:33:27] [Render thread/INFO]: Registering Runtime: jei:fabric_gui took 75.78 milliseconds
[16:33:27] [Render thread/INFO]: Registering Runtime took 76.61 milliseconds
[16:33:27] [Render thread/INFO]: Building runtime took 77.32 milliseconds
[16:33:27] [Render thread/INFO]: Sending Runtime...
[16:33:27] [Render thread/INFO]: Sending Runtime took 278.2 microseconds
[16:33:27] [Render thread/INFO]: Starting JEI took 125.8 milliseconds
[16:33:27] [Render thread/INFO]: Stopping JEI
[16:33:27] [Render thread/INFO]: Stopping JEI
[16:33:27] [Render thread/INFO]: Sending Runtime Unavailable...
[16:33:27] [Render thread/INFO]: Stopping JEI GUI
[16:33:27] [Render thread/INFO]: Sending Runtime Unavailable took 254.0 microseconds
[16:33:27] [Render thread/INFO]: Starting JEI...
[16:33:27] [Render thread/INFO]: Registering item subtypes...
[16:33:27] [Render thread/INFO]: Registering item subtypes took 189.6 microseconds
[16:33:27] [Render thread/INFO]: Registering fluid subtypes...
[16:33:27] [Render thread/INFO]: Registering fluid subtypes took 132.4 microseconds
[16:33:27] [Render thread/INFO]: Registering ingredients...
[16:33:27] [Render thread/INFO]: Registering ingredients took 9.926 milliseconds
[16:33:27] [Render thread/INFO]: Registering extra ingredients...
[16:33:27] [Render thread/INFO]: Registering extra ingredients took 320.5 microseconds
[16:33:27] [Render thread/INFO]: Registering search ingredient aliases...
[16:33:27] [Render thread/INFO]: Registering search ingredient aliases took 342.5 microseconds
[16:33:27] [Render thread/INFO]: Registering Mod Info...
[16:33:27] [Render thread/INFO]: Registering Mod Info took 323.3 microseconds
[16:33:27] [Render thread/INFO]: Registering categories...
[16:33:27] [Render thread/INFO]: Registering categories took 466.4 microseconds
[16:33:27] [Render thread/INFO]: Registering vanilla category extensions...
[16:33:27] [Render thread/INFO]: Registering vanilla category extensions took 208.4 microseconds
[16:33:27] [Render thread/INFO]: Registering recipe catalysts...
[16:33:27] [Render thread/INFO]: Registering recipe catalysts took 209.9 microseconds
[16:33:27] [Render thread/INFO]: Building recipe registry...
[16:33:27] [Render thread/INFO]: Building recipe registry took 196.3 microseconds
[16:33:27] [Render thread/INFO]: Registering advanced plugins...
[16:33:27] [Render thread/INFO]: Registering advanced plugins took 230.3 microseconds
[16:33:27] [Render thread/INFO]: Registering recipes...
[16:33:27] [Render thread/INFO]: Registering recipes: jei:minecraft took 26.06 milliseconds
[16:33:27] [Render thread/INFO]: Registering recipes took 26.75 milliseconds
[16:33:27] [Render thread/INFO]: Registering recipes transfer handlers...
[16:33:27] [Render thread/INFO]: Registering recipes transfer handlers took 392.1 microseconds
[16:33:27] [Render thread/INFO]: Building runtime...
[16:33:27] [Render thread/INFO]: Registering gui handlers...
[16:33:27] [Render thread/INFO]: Registering gui handlers took 379.4 microseconds
[16:33:27] [Render thread/INFO]: Registering Runtime...
[16:33:27] [Render thread/INFO]: Starting JEI GUI
[16:33:27] [Render thread/INFO]: Building ingredient list...
[16:33:27] [Render thread/INFO]: Building ingredient list took 7.860 milliseconds
[16:33:27] [Render thread/INFO]: Building ingredient filter...
[16:33:27] [Render thread/INFO]: Adding 1688 ingredients
[16:33:27] [Render thread/INFO]: Added 1688 ingredients
[16:33:27] [Render thread/INFO]: Building ingredient filter took 47.31 milliseconds
[16:33:27] [Render thread/INFO]: Registering Runtime: jei:fabric_gui took 59.55 milliseconds
[16:33:27] [Render thread/INFO]: Registering Runtime took 60.17 milliseconds
[16:33:27] [Render thread/INFO]: Building runtime took 61.05 milliseconds
[16:33:27] [Render thread/INFO]: Sending Runtime...
[16:33:27] [Render thread/INFO]: Sending Runtime took 305.3 microseconds
[16:33:27] [Render thread/INFO]: Starting JEI took 106.3 milliseconds
[16:33:27] [Render thread/INFO]: Stopping JEI
[16:33:27] [Render thread/INFO]: Stopping JEI
[16:33:27] [Render thread/INFO]: Sending Runtime Unavailable...
[16:33:27] [Render thread/INFO]: Stopping JEI GUI
[16:33:27] [Render thread/INFO]: Sending Runtime Unavailable took 342.2 microseconds
[16:33:27] [Render thread/INFO]: Starting JEI...
[16:33:27] [Render thread/INFO]: Registering item subtypes...
[16:33:27] [Render thread/INFO]: Registering item subtypes took 194.4 microseconds
[16:33:27] [Render thread/INFO]: Registering fluid subtypes...
[16:33:27] [Render thread/INFO]: Registering fluid subtypes took 151.3 microseconds
[16:33:27] [Render thread/INFO]: Registering ingredients...
[16:33:27] [Render thread/INFO]: Registering ingredients took 8.575 milliseconds
[16:33:27] [Render thread/INFO]: Registering extra ingredients...
[16:33:27] [Render thread/INFO]: Registering extra ingredients took 296.4 microseconds
[16:33:27] [Render thread/INFO]: Registering search ingredient aliases...
[16:33:27] [Render thread/INFO]: Registering search ingredient aliases took 230.2 microseconds
[16:33:27] [Render thread/INFO]: Registering Mod Info...
[16:33:27] [Render thread/INFO]: Registering Mod Info took 385.5 microseconds
[16:33:27] [Render thread/INFO]: Registering categories...
[16:33:27] [Render thread/INFO]: Registering categories took 467.5 microseconds
[16:33:27] [Render thread/INFO]: Registering vanilla category extensions...
[16:33:27] [Render thread/INFO]: Registering vanilla category extensions took 213.2 microseconds
[16:33:27] [Render thread/INFO]: Registering recipe catalysts...
[16:33:27] [Render thread/INFO]: Registering recipe catalysts took 206.5 microseconds
[16:33:27] [Render thread/INFO]: Building recipe registry...
[16:33:27] [Render thread/INFO]: Building recipe registry took 174.7 microseconds
[16:33:27] [Render thread/INFO]: Registering advanced plugins...
[16:33:27] [Render thread/INFO]: Registering advanced plugins took 255.5 microseconds
[16:33:27] [Render thread/INFO]: Registering recipes...
[16:33:27] [Render thread/INFO]: Registering recipes: jei:minecraft took 18.61 milliseconds
[16:33:27] [Render thread/INFO]: Registering recipes took 19.30 milliseconds
[16:33:27] [Render thread/INFO]: Registering recipes transfer handlers...
[16:33:27] [Render thread/INFO]: Registering recipes transfer handlers took 331.6 microseconds
[16:33:27] [Render thread/INFO]: Building runtime...
[16:33:27] [Render thread/INFO]: Registering gui handlers...
[16:33:27] [Render thread/INFO]: Registering gui handlers took 236.9 microseconds
[16:33:27] [Render thread/INFO]: Registering Runtime...
[16:33:27] [Render thread/INFO]: Starting JEI GUI
[16:33:27] [Render thread/INFO]: Building ingredient list...
[16:33:27] [Render thread/INFO]: Building ingredient list took 10.02 milliseconds
[16:33:27] [Render thread/INFO]: Building ingredient filter...
[16:33:27] [Render thread/INFO]: Adding 1688 ingredients
[16:33:27] [Render thread/INFO]: Added 1688 ingredients
[16:33:27] [Render thread/INFO]: Building ingredient filter took 76.34 milliseconds
[16:33:27] [Render thread/INFO]: Registering Runtime: jei:fabric_gui took 89.93 milliseconds
[16:33:27] [Render thread/INFO]: Registering Runtime took 90.44 milliseconds
[16:33:27] [Render thread/INFO]: Building runtime took 91.01 milliseconds
[16:33:27] [Render thread/INFO]: Sending Runtime...
[16:33:27] [Render thread/INFO]: Sending Runtime took 266.5 microseconds
[16:33:27] [Render thread/INFO]: Starting JEI took 127.3 milliseconds
[16:33:27] [Render thread/INFO]: Minimap updated server level id: -1755197486 for world ResourceKey[minecraft:dimension / minecraft:overworld]
[16:33:28] [Server thread/INFO]: Ngoc_Cutee[local] logged in with entity id 465 at (8.5, 13.0, 14.5)
[16:33:28] [Server thread/INFO]: Ngoc_Cutee joined the game
[16:33:28] [Render thread/INFO]: [System] [CHAT] Ngoc_Cutee joined the game
[16:33:28] [Render thread/INFO]: Loaded 2 advancements
[16:33:28] [Server thread/INFO]: Ngoc_Cutee[local] logged in with entity id 471 at (8.5, 13.0, 14.5)
[16:33:28] [Server thread/INFO]: Ngoc_Cutee joined the game
[16:33:28] [Server thread/WARN]: Force-added player with duplicate UUID cf49c072-f5b0-43fb-b3e7-9458855a2652
[16:33:28] [Server thread/INFO]: [ShulkerBoxTooltip] F0meG04: protocol version: 2.0
[16:33:28] [Render thread/INFO]: [System] [CHAT] Ngoc_Cutee joined the game
[16:33:28] [Render thread/INFO]: [ShulkerBoxTooltip] Handshake succeeded
[16:33:28] [Render thread/INFO]: [ShulkerBoxTooltip] Server protocol version: 2.0
[16:33:33] [Server thread/INFO]: Loaded 1290 recipes
[16:33:33] [Server thread/INFO]: Loaded 1401 advancements
[16:33:33] [Render thread/INFO]: Stopping JEI
[16:33:33] [Render thread/INFO]: Stopping JEI
[16:33:33] [Render thread/INFO]: Sending Runtime Unavailable...
[16:33:33] [Render thread/INFO]: Stopping JEI GUI
[16:33:33] [Render thread/INFO]: Sending Runtime Unavailable took 616.0 microseconds
[16:33:33] [Render thread/INFO]: Starting JEI...
[16:33:33] [Render thread/INFO]: Registering item subtypes...
[16:33:33] [Render thread/INFO]: Registering item subtypes took 399.8 microseconds
[16:33:33] [Render thread/INFO]: Registering fluid subtypes...
[16:33:33] [Render thread/INFO]: Registering fluid subtypes took 177.3 microseconds
[16:33:33] [Render thread/INFO]: Registering ingredients...
[16:33:33] [Render thread/INFO]: Registering ingredients took 8.097 milliseconds
[16:33:33] [Render thread/INFO]: Registering extra ingredients...
[16:33:33] [Render thread/INFO]: Registering extra ingredients took 282.0 microseconds
[16:33:33] [Render thread/INFO]: Registering search ingredient aliases...
[16:33:33] [Render thread/INFO]: Registering search ingredient aliases took 266.6 microseconds
[16:33:33] [Render thread/INFO]: Registering Mod Info...
[16:33:33] [Render thread/INFO]: Registering Mod Info took 338.4 microseconds
[16:33:33] [Render thread/INFO]: Registering categories...
[16:33:33] [Render thread/INFO]: Registering categories took 540.6 microseconds
[16:33:33] [Render thread/INFO]: Registering vanilla category extensions...
[16:33:33] [Render thread/INFO]: Registering vanilla category extensions took 252.8 microseconds
[16:33:33] [Render thread/INFO]: Registering recipe catalysts...
[16:33:33] [Render thread/INFO]: Registering recipe catalysts took 245.5 microseconds
[16:33:33] [Render thread/INFO]: Building recipe registry...
[16:33:33] [Render thread/INFO]: Building recipe registry took 190.3 microseconds
[16:33:33] [Render thread/INFO]: Registering advanced plugins...
[16:33:33] [Render thread/INFO]: Registering advanced plugins took 201.7 microseconds
[16:33:33] [Render thread/INFO]: Registering recipes...
[16:33:33] [Render thread/INFO]: Registering recipes: jei:minecraft took 27.66 milliseconds
[16:33:33] [Render thread/INFO]: Registering recipes took 28.30 milliseconds
[16:33:33] [Render thread/INFO]: Registering recipes transfer handlers...
[16:33:33] [Render thread/INFO]: Registering recipes transfer handlers took 407.6 microseconds
[16:33:33] [Render thread/INFO]: Building runtime...
[16:33:33] [Render thread/INFO]: Registering gui handlers...
[16:33:33] [Render thread/INFO]: Registering gui handlers took 348.8 microseconds
[16:33:33] [Render thread/INFO]: Registering Runtime...
[16:33:33] [Render thread/INFO]: Starting JEI GUI
[16:33:33] [Render thread/INFO]: Building ingredient list...
[16:33:33] [Render thread/INFO]: Building ingredient list took 6.817 milliseconds
[16:33:33] [Render thread/INFO]: Building ingredient filter...
[16:33:33] [Render thread/INFO]: Adding 1688 ingredients
[16:33:33] [Render thread/INFO]: Added 1688 ingredients
[16:33:33] [Render thread/INFO]: Building ingredient filter took 51.78 milliseconds
[16:33:33] [Render thread/INFO]: Registering Runtime: jei:fabric_gui took 63.07 milliseconds
[16:33:33] [Render thread/INFO]: Registering Runtime took 63.59 milliseconds
[16:33:33] [Render thread/INFO]: Building runtime took 64.27 milliseconds
[16:33:33] [Render thread/INFO]: Sending Runtime...
[16:33:33] [Render thread/INFO]: Sending Runtime took 259.7 microseconds
[16:33:33] [Render thread/INFO]: Starting JEI took 110.8 milliseconds
[16:33:33] [Render thread/INFO]: Stopping JEI
[16:33:33] [Render thread/INFO]: Stopping JEI
[16:33:33] [Render thread/INFO]: Sending Runtime Unavailable...
[16:33:33] [Render thread/INFO]: Stopping JEI GUI
[16:33:33] [Render thread/INFO]: Sending Runtime Unavailable took 212.7 microseconds
[16:33:33] [Render thread/INFO]: Starting JEI...
[16:33:33] [Render thread/INFO]: Registering item subtypes...
[16:33:33] [Render thread/INFO]: Registering item subtypes took 125.3 microseconds
[16:33:33] [Render thread/INFO]: Registering fluid subtypes...
[16:33:33] [Render thread/INFO]: Registering fluid subtypes took 108.6 microseconds
[16:33:33] [Render thread/INFO]: Registering ingredients...
[16:33:33] [Render thread/INFO]: Registering ingredients took 7.140 milliseconds
[16:33:33] [Render thread/INFO]: Registering extra ingredients...
[16:33:33] [Render thread/INFO]: Registering extra ingredients took 368.5 microseconds
[16:33:33] [Render thread/INFO]: Registering search ingredient aliases...
[16:33:33] [Render thread/INFO]: Registering search ingredient aliases took 276.9 microseconds
[16:33:33] [Render thread/INFO]: Registering Mod Info...
[16:33:33] [Render thread/INFO]: Registering Mod Info took 403.6 microseconds
[16:33:33] [Render thread/INFO]: Registering categories...
[16:33:33] [Render thread/INFO]: Registering categories took 526.2 microseconds
[16:33:33] [Render thread/INFO]: Registering vanilla category extensions...
[16:33:33] [Render thread/INFO]: Registering vanilla category extensions took 222.5 microseconds
[16:33:33] [Render thread/INFO]: Registering recipe catalysts...
[16:33:33] [Render thread/INFO]: Registering recipe catalysts took 365.2 microseconds
[16:33:33] [Render thread/INFO]: Building recipe registry...
[16:33:33] [Render thread/INFO]: Building recipe registry took 223.7 microseconds
[16:33:33] [Render thread/INFO]: Registering advanced plugins...
[16:33:33] [Render thread/INFO]: Registering advanced plugins took 307.9 microseconds
[16:33:33] [Render thread/INFO]: Registering recipes...
[16:33:33] [Render thread/INFO]: Registering recipes: jei:minecraft took 22.44 milliseconds
[16:33:33] [Render thread/INFO]: Registering recipes took 23.20 milliseconds
[16:33:33] [Render thread/INFO]: Registering recipes transfer handlers...
[16:33:33] [Render thread/INFO]: Registering recipes transfer handlers took 429.6 microseconds
[16:33:33] [Render thread/INFO]: Building runtime...
[16:33:33] [Render thread/INFO]: Registering gui handlers...
[16:33:33] [Render thread/INFO]: Registering gui handlers took 359.9 microseconds
[16:33:33] [Render thread/INFO]: Registering Runtime...
[16:33:33] [Render thread/INFO]: Starting JEI GUI
[16:33:33] [Render thread/INFO]: Building ingredient list...
[16:33:33] [Render thread/INFO]: Building ingredient list took 7.797 milliseconds
[16:33:33] [Render thread/INFO]: Building ingredient filter...
[16:33:33] [Render thread/INFO]: Adding 1688 ingredients
[16:33:33] [Render thread/INFO]: Added 1688 ingredients
[16:33:33] [Render thread/INFO]: Building ingredient filter took 51.32 milliseconds
[16:33:33] [Render thread/INFO]: Registering Runtime: jei:fabric_gui took 64.35 milliseconds
[16:33:33] [Render thread/INFO]: Registering Runtime took 65.05 milliseconds
[16:33:33] [Render thread/INFO]: Building runtime took 65.95 milliseconds
[16:33:33] [Render thread/INFO]: Sending Runtime...
[16:33:33] [Render thread/INFO]: Sending Runtime took 350.0 microseconds
[16:33:33] [Render thread/INFO]: Starting JEI took 105.2 milliseconds
[16:33:33] [Render thread/INFO]: Stopping JEI
[16:33:33] [Render thread/INFO]: Stopping JEI
[16:33:33] [Render thread/INFO]: Sending Runtime Unavailable...
[16:33:33] [Render thread/INFO]: Stopping JEI GUI
[16:33:33] [Render thread/INFO]: Sending Runtime Unavailable took 364.7 microseconds
[16:33:33] [Render thread/INFO]: Starting JEI...
[16:33:33] [Render thread/INFO]: Registering item subtypes...
[16:33:33] [Render thread/INFO]: Registering item subtypes took 176.0 microseconds
[16:33:33] [Render thread/INFO]: Registering fluid subtypes...
[16:33:33] [Render thread/INFO]: Registering fluid subtypes took 130.9 microseconds
[16:33:33] [Render thread/INFO]: Registering ingredients...
[16:33:33] [Render thread/INFO]: Registering ingredients took 6.393 milliseconds
[16:33:33] [Render thread/INFO]: Registering extra ingredients...
[16:33:33] [Render thread/INFO]: Registering extra ingredients took 417.8 microseconds
[16:33:33] [Render thread/INFO]: Registering search ingredient aliases...
[16:33:33] [Render thread/INFO]: Registering search ingredient aliases took 220.6 microseconds
[16:33:33] [Render thread/INFO]: Registering Mod Info...
[16:33:33] [Render thread/INFO]: Registering Mod Info took 359.0 microseconds
[16:33:33] [Render thread/INFO]: Registering categories...
[16:33:33] [Render thread/INFO]: Registering categories took 593.9 microseconds
[16:33:33] [Render thread/INFO]: Registering vanilla category extensions...
[16:33:33] [Render thread/INFO]: Registering vanilla category extensions took 272.6 microseconds
[16:33:33] [Render thread/INFO]: Registering recipe catalysts...
[16:33:33] [Render thread/INFO]: Registering recipe catalysts took 235.6 microseconds
[16:33:33] [Render thread/INFO]: Building recipe registry...
[16:33:33] [Render thread/INFO]: Building recipe registry took 243.3 microseconds
[16:33:33] [Render thread/INFO]: Registering advanced plugins...
[16:33:33] [Render thread/INFO]: Registering advanced plugins took 195.2 microseconds
[16:33:33] [Render thread/INFO]: Registering recipes...
[16:33:33] [Render thread/INFO]: Registering recipes: jei:minecraft took 16.67 milliseconds
[16:33:33] [Render thread/INFO]: Registering recipes took 17.23 milliseconds
[16:33:33] [Render thread/INFO]: Registering recipes transfer handlers...
[16:33:33] [Render thread/INFO]: Registering recipes transfer handlers took 302.3 microseconds
[16:33:33] [Render thread/INFO]: Building runtime...
[16:33:33] [Render thread/INFO]: Registering gui handlers...
[16:33:33] [Render thread/INFO]: Registering gui handlers took 272.1 microseconds
[16:33:33] [Render thread/INFO]: Registering Runtime...
[16:33:33] [Render thread/INFO]: Starting JEI GUI
[16:33:33] [Render thread/INFO]: Building ingredient list...
[16:33:33] [Render thread/INFO]: Building ingredient list took 6.068 milliseconds
[16:33:33] [Render thread/INFO]: Building ingredient filter...
[16:33:33] [Render thread/INFO]: Adding 1688 ingredients
[16:33:33] [Render thread/INFO]: Added 1688 ingredients
[16:33:33] [Render thread/INFO]: Building ingredient filter took 46.83 milliseconds
[16:33:33] [Render thread/INFO]: Registering Runtime: jei:fabric_gui took 56.49 milliseconds
[16:33:33] [Render thread/INFO]: Registering Runtime took 56.95 milliseconds
[16:33:33] [Render thread/INFO]: Building runtime took 57.67 milliseconds
[16:33:33] [Render thread/INFO]: Sending Runtime...
[16:33:33] [Render thread/INFO]: Sending Runtime took 300.8 microseconds
[16:33:33] [Render thread/INFO]: Starting JEI took 89.25 milliseconds
[16:33:33] [Render thread/INFO]: Loaded 2 advancements
[16:33:37] [Render thread/INFO]: [System] [CHAT] Your game mode has been updated to Survival Mode
[16:33:41] [Render thread/INFO]: [System] [CHAT] Your game mode has been updated to Adventure Mode
[16:33:43] [Server thread/INFO]: Loaded 1290 recipes
[16:33:43] [Server thread/INFO]: Loaded 1401 advancements
[16:33:43] [Render thread/INFO]: Stopping JEI
[16:33:43] [Render thread/INFO]: Stopping JEI
[16:33:43] [Render thread/INFO]: Sending Runtime Unavailable...
[16:33:43] [Render thread/INFO]: Stopping JEI GUI
[16:33:43] [Render thread/INFO]: Sending Runtime Unavailable took 477.7 microseconds
[16:33:43] [Render thread/INFO]: Starting JEI...
[16:33:43] [Render thread/INFO]: Registering item subtypes...
[16:33:43] [Render thread/INFO]: Registering item subtypes took 178.2 microseconds
[16:33:43] [Render thread/INFO]: Registering fluid subtypes...
[16:33:43] [Render thread/INFO]: Registering fluid subtypes took 140.5 microseconds
[16:33:43] [Render thread/INFO]: Registering ingredients...
[16:33:43] [Render thread/INFO]: Registering ingredients took 6.740 milliseconds
[16:33:43] [Render thread/INFO]: Registering extra ingredients...
[16:33:43] [Render thread/INFO]: Registering extra ingredients took 283.8 microseconds
[16:33:43] [Render thread/INFO]: Registering search ingredient aliases...
[16:33:43] [Render thread/INFO]: Registering search ingredient aliases took 267.9 microseconds
[16:33:43] [Render thread/INFO]: Registering Mod Info...
[16:33:43] [Render thread/INFO]: Registering Mod Info took 269.9 microseconds
[16:33:43] [Render thread/INFO]: Registering categories...
[16:33:43] [Render thread/INFO]: Registering categories took 478.3 microseconds
[16:33:43] [Render thread/INFO]: Registering vanilla category extensions...
[16:33:43] [Render thread/INFO]: Registering vanilla category extensions took 318.1 microseconds
[16:33:43] [Render thread/INFO]: Registering recipe catalysts...
[16:33:43] [Render thread/INFO]: Registering recipe catalysts took 184.7 microseconds
[16:33:43] [Render thread/INFO]: Building recipe registry...
[16:33:43] [Render thread/INFO]: Building recipe registry took 196.5 microseconds
[16:33:43] [Render thread/INFO]: Registering advanced plugins...
[16:33:43] [Render thread/INFO]: Registering advanced plugins took 142.1 microseconds
[16:33:43] [Render thread/INFO]: Registering recipes...
[16:33:43] [Render thread/INFO]: Registering recipes: jei:minecraft took 21.88 milliseconds
[16:33:43] [Render thread/INFO]: Registering recipes took 22.25 milliseconds
[16:33:43] [Render thread/INFO]: Registering recipes transfer handlers...
[16:33:43] [Render thread/INFO]: Registering recipes transfer handlers took 323.7 microseconds
[16:33:43] [Render thread/INFO]: Building runtime...
[16:33:43] [Render thread/INFO]: Registering gui handlers...
[16:33:43] [Render thread/INFO]: Registering gui handlers took 261.4 microseconds
[16:33:43] [Render thread/INFO]: Registering Runtime...
[16:33:43] [Render thread/INFO]: Starting JEI GUI
[16:33:43] [Render thread/INFO]: Building ingredient list...
[16:33:43] [Render thread/INFO]: Building ingredient list took 5.319 milliseconds
[16:33:43] [Render thread/INFO]: Building ingredient filter...
[16:33:43] [Render thread/INFO]: Adding 1688 ingredients
[16:33:43] [Render thread/INFO]: Added 1688 ingredients
[16:33:43] [Render thread/INFO]: Building ingredient filter took 38.73 milliseconds
[16:33:43] [Render thread/INFO]: Registering Runtime: jei:fabric_gui took 47.63 milliseconds
[16:33:43] [Render thread/INFO]: Registering Runtime took 48.06 milliseconds
[16:33:43] [Render thread/INFO]: Building runtime took 48.60 milliseconds
[16:33:43] [Render thread/INFO]: Sending Runtime...
[16:33:43] [Render thread/INFO]: Sending Runtime took 364.6 microseconds
[16:33:43] [Render thread/INFO]: Starting JEI took 84.95 milliseconds
[16:33:43] [Render thread/INFO]: Stopping JEI
[16:33:43] [Render thread/INFO]: Stopping JEI
[16:33:43] [Render thread/INFO]: Sending Runtime Unavailable...
[16:33:43] [Render thread/INFO]: Stopping JEI GUI
[16:33:43] [Render thread/INFO]: Sending Runtime Unavailable took 245.6 microseconds
[16:33:43] [Render thread/INFO]: Starting JEI...
[16:33:43] [Render thread/INFO]: Registering item subtypes...
[16:33:43] [Render thread/INFO]: Registering item subtypes took 156.7 microseconds
[16:33:43] [Render thread/INFO]: Registering fluid subtypes...
[16:33:43] [Render thread/INFO]: Registering fluid subtypes took 100.2 microseconds
[16:33:43] [Render thread/INFO]: Registering ingredients...
[16:33:43] [Render thread/INFO]: Registering ingredients took 4.430 milliseconds
[16:33:43] [Render thread/INFO]: Registering extra ingredients...
[16:33:43] [Render thread/INFO]: Registering extra ingredients took 302.0 microseconds
[16:33:43] [Render thread/INFO]: Registering search ingredient aliases...
[16:33:43] [Render thread/INFO]: Registering search ingredient aliases took 199.5 microseconds
[16:33:43] [Render thread/INFO]: Registering Mod Info...
[16:33:43] [Render thread/INFO]: Registering Mod Info took 224.3 microseconds
[16:33:43] [Render thread/INFO]: Registering categories...
[16:33:43] [Render thread/INFO]: Registering categories took 416.8 microseconds
[16:33:43] [Render thread/INFO]: Registering vanilla category extensions...
[16:33:43] [Render thread/INFO]: Registering vanilla category extensions took 260.8 microseconds
[16:33:43] [Render thread/INFO]: Registering recipe catalysts...
[16:33:43] [Render thread/INFO]: Registering recipe catalysts took 231.7 microseconds
[16:33:43] [Render thread/INFO]: Building recipe registry...
[16:33:43] [Render thread/INFO]: Building recipe registry took 201.9 microseconds
[16:33:43] [Render thread/INFO]: Registering advanced plugins...
[16:33:43] [Render thread/INFO]: Registering advanced plugins took 216.2 microseconds
[16:33:43] [Render thread/INFO]: Registering recipes...
[16:33:43] [Render thread/INFO]: Registering recipes: jei:minecraft took 43.56 milliseconds
[16:33:43] [Render thread/INFO]: Registering recipes took 44.06 milliseconds
[16:33:43] [Render thread/INFO]: Registering recipes transfer handlers...
[16:33:43] [Render thread/INFO]: Registering recipes transfer handlers took 312.7 microseconds
[16:33:43] [Render thread/INFO]: Building runtime...
[16:33:43] [Render thread/INFO]: Registering gui handlers...
[16:33:43] [Render thread/INFO]: Registering gui handlers took 219.1 microseconds
[16:33:43] [Render thread/INFO]: Registering Runtime...
[16:33:43] [Render thread/INFO]: Starting JEI GUI
[16:33:43] [Render thread/INFO]: Building ingredient list...
[16:33:43] [Render thread/INFO]: Building ingredient list took 4.988 milliseconds
[16:33:43] [Render thread/INFO]: Building ingredient filter...
[16:33:43] [Render thread/INFO]: Adding 1688 ingredients
[16:33:43] [Render thread/INFO]: Added 1688 ingredients
[16:33:43] [Render thread/INFO]: Building ingredient filter took 37.39 milliseconds
[16:33:43] [Render thread/INFO]: Registering Runtime: jei:fabric_gui took 45.90 milliseconds
[16:33:43] [Render thread/INFO]: Registering Runtime took 46.24 milliseconds
[16:33:43] [Render thread/INFO]: Building runtime took 46.62 milliseconds
[16:33:43] [Render thread/INFO]: Sending Runtime...
[16:33:43] [Render thread/INFO]: Sending Runtime took 348.0 microseconds
[16:33:43] [Render thread/INFO]: Starting JEI took 101.5 milliseconds
[16:33:43] [Render thread/INFO]: Stopping JEI
[16:33:43] [Render thread/INFO]: Stopping JEI
[16:33:43] [Render thread/INFO]: Sending Runtime Unavailable...
[16:33:43] [Render thread/INFO]: Stopping JEI GUI
[16:33:43] [Render thread/INFO]: Sending Runtime Unavailable took 236.5 microseconds
[16:33:43] [Render thread/INFO]: Starting JEI...
[16:33:43] [Render thread/INFO]: Registering item subtypes...
[16:33:43] [Render thread/INFO]: Registering item subtypes took 298.0 microseconds
[16:33:43] [Render thread/INFO]: Registering fluid subtypes...
[16:33:43] [Render thread/INFO]: Registering fluid subtypes took 205.8 microseconds
[16:33:43] [Render thread/INFO]: Registering ingredients...
[16:33:43] [Render thread/INFO]: Registering ingredients took 5.624 milliseconds
[16:33:43] [Render thread/INFO]: Registering extra ingredients...
[16:33:43] [Render thread/INFO]: Registering extra ingredients took 248.0 microseconds
[16:33:43] [Render thread/INFO]: Registering search ingredient aliases...
[16:33:43] [Render thread/INFO]: Registering search ingredient aliases took 247.7 microseconds
[16:33:43] [Render thread/INFO]: Registering Mod Info...
[16:33:43] [Render thread/INFO]: Registering Mod Info took 239.2 microseconds
[16:33:43] [Render thread/INFO]: Registering categories...
[16:33:43] [Render thread/INFO]: Registering categories took 458.3 microseconds
[16:33:43] [Render thread/INFO]: Registering vanilla category extensions...
[16:33:43] [Render thread/INFO]: Registering vanilla category extensions took 199.0 microseconds
[16:33:43] [Render thread/INFO]: Registering recipe catalysts...
[16:33:43] [Render thread/INFO]: Registering recipe catalysts took 193.9 microseconds
[16:33:43] [Render thread/INFO]: Building recipe registry...
[16:33:43] [Render thread/INFO]: Building recipe registry took 179.2 microseconds
[16:33:43] [Render thread/INFO]: Registering advanced plugins...
[16:33:43] [Render thread/INFO]: Registering advanced plugins took 214.3 microseconds
[16:33:43] [Render thread/INFO]: Registering recipes...
[16:33:43] [Render thread/INFO]: Registering recipes: jei:minecraft took 15.04 milliseconds
[16:33:43] [Render thread/INFO]: Registering recipes took 15.60 milliseconds
[16:33:43] [Render thread/INFO]: Registering recipes transfer handlers...
[16:33:43] [Render thread/INFO]: Registering recipes transfer handlers took 295.4 microseconds
[16:33:43] [Render thread/INFO]: Building runtime...
[16:33:43] [Render thread/INFO]: Registering gui handlers...
[16:33:43] [Render thread/INFO]: Registering gui handlers took 284.2 microseconds
[16:33:43] [Render thread/INFO]: Registering Runtime...
[16:33:43] [Render thread/INFO]: Starting JEI GUI
[16:33:43] [Render thread/INFO]: Building ingredient list...
[16:33:43] [Render thread/INFO]: Building ingredient list took 4.450 milliseconds
[16:33:43] [Render thread/INFO]: Building ingredient filter...
[16:33:43] [Render thread/INFO]: Adding 1688 ingredients
[16:33:43] [Render thread/INFO]: Added 1688 ingredients
[16:33:43] [Render thread/INFO]: Building ingredient filter took 28.82 milliseconds
[16:33:43] [Render thread/INFO]: Registering Runtime: jei:fabric_gui took 36.40 milliseconds
[16:33:43] [Render thread/INFO]: Registering Runtime took 36.75 milliseconds
[16:33:43] [Render thread/INFO]: Building runtime took 37.26 milliseconds
[16:33:43] [Render thread/INFO]: Sending Runtime...
[16:33:43] [Render thread/INFO]: Sending Runtime took 219.3 microseconds
[16:33:43] [Render thread/INFO]: Starting JEI took 65.25 milliseconds
[16:33:43] [Render thread/INFO]: Loaded 2 advancements
[16:35:59] [Server thread/INFO]: Loaded 1290 recipes
[16:35:59] [Server thread/INFO]: Loaded 1401 advancements
[16:35:59] [Render thread/INFO]: Stopping JEI
[16:35:59] [Render thread/INFO]: Stopping JEI
[16:35:59] [Render thread/INFO]: Sending Runtime Unavailable...
[16:35:59] [Render thread/INFO]: Stopping JEI GUI
[16:35:59] [Render thread/INFO]: Sending Runtime Unavailable took 536.2 microseconds
[16:35:59] [Render thread/INFO]: Starting JEI...
[16:35:59] [Render thread/INFO]: Registering item subtypes...
[16:35:59] [Render thread/INFO]: Registering item subtypes took 152.9 microseconds
[16:35:59] [Render thread/INFO]: Registering fluid subtypes...
[16:35:59] [Render thread/INFO]: Registering fluid subtypes took 92.30 microseconds
[16:35:59] [Render thread/INFO]: Registering ingredients...
[16:35:59] [Render thread/INFO]: Registering ingredients took 5.744 milliseconds
[16:35:59] [Render thread/INFO]: Registering extra ingredients...
[16:35:59] [Render thread/INFO]: Registering extra ingredients took 210.9 microseconds
[16:35:59] [Render thread/INFO]: Registering search ingredient aliases...
[16:35:59] [Render thread/INFO]: Registering search ingredient aliases took 373.3 microseconds
[16:35:59] [Render thread/INFO]: Registering Mod Info...
[16:35:59] [Render thread/INFO]: Registering Mod Info took 213.3 microseconds
[16:35:59] [Render thread/INFO]: Registering categories...
[16:35:59] [Render thread/INFO]: Registering categories took 427.6 microseconds
[16:35:59] [Render thread/INFO]: Registering vanilla category extensions...
[16:35:59] [Render thread/INFO]: Registering vanilla category extensions took 172.4 microseconds
[16:35:59] [Render thread/INFO]: Registering recipe catalysts...
[16:35:59] [Render thread/INFO]: Registering recipe catalysts took 103.6 microseconds
[16:35:59] [Render thread/INFO]: Building recipe registry...
[16:35:59] [Render thread/INFO]: Building recipe registry took 175.0 microseconds
[16:35:59] [Render thread/INFO]: Registering advanced plugins...
[16:35:59] [Render thread/INFO]: Registering advanced plugins took 104.5 microseconds
[16:35:59] [Render thread/INFO]: Registering recipes...
[16:35:59] [Render thread/INFO]: Registering recipes: jei:minecraft took 14.36 milliseconds
[16:35:59] [Render thread/INFO]: Registering recipes took 14.81 milliseconds
[16:35:59] [Render thread/INFO]: Registering recipes transfer handlers...
[16:35:59] [Render thread/INFO]: Registering recipes transfer handlers took 315.0 microseconds
[16:35:59] [Render thread/INFO]: Building runtime...
[16:35:59] [Render thread/INFO]: Registering gui handlers...
[16:35:59] [Render thread/INFO]: Registering gui handlers took 336.9 microseconds
[16:35:59] [Render thread/INFO]: Registering Runtime...
[16:35:59] [Render thread/INFO]: Starting JEI GUI
[16:35:59] [Render thread/INFO]: Building ingredient list...
[16:35:59] [Render thread/INFO]: Building ingredient list took 4.234 milliseconds
[16:35:59] [Render thread/INFO]: Building ingredient filter...
[16:35:59] [Render thread/INFO]: Adding 1688 ingredients
[16:35:59] [Render thread/INFO]: Added 1688 ingredients
[16:35:59] [Render thread/INFO]: Building ingredient filter took 32.33 milliseconds
[16:35:59] [Render thread/INFO]: Registering Runtime: jei:fabric_gui took 40.41 milliseconds
[16:35:59] [Render thread/INFO]: Registering Runtime took 40.98 milliseconds
[16:35:59] [Render thread/INFO]: Building runtime took 41.62 milliseconds
[16:35:59] [Render thread/INFO]: Sending Runtime...
[16:35:59] [Render thread/INFO]: Sending Runtime took 301.4 microseconds
[16:35:59] [Render thread/INFO]: Starting JEI took 68.04 milliseconds
[16:35:59] [Render thread/INFO]: Stopping JEI
[16:35:59] [Render thread/INFO]: Stopping JEI
[16:35:59] [Render thread/INFO]: Sending Runtime Unavailable...
[16:35:59] [Render thread/INFO]: Stopping JEI GUI
[16:35:59] [Render thread/INFO]: Sending Runtime Unavailable took 262.0 microseconds
[16:35:59] [Render thread/INFO]: Starting JEI...
[16:35:59] [Render thread/INFO]: Registering item subtypes...
[16:35:59] [Render thread/INFO]: Registering item subtypes took 157.8 microseconds
[16:35:59] [Render thread/INFO]: Registering fluid subtypes...
[16:35:59] [Render thread/INFO]: Registering fluid subtypes took 104.9 microseconds
[16:35:59] [Render thread/INFO]: Registering ingredients...
[16:35:59] [Render thread/INFO]: Registering ingredients took 5.729 milliseconds
[16:35:59] [Render thread/INFO]: Registering extra ingredients...
[16:35:59] [Render thread/INFO]: Registering extra ingredients took 284.4 microseconds
[16:35:59] [Render thread/INFO]: Registering search ingredient aliases...
[16:35:59] [Render thread/INFO]: Registering search ingredient aliases took 275.1 microseconds
[16:35:59] [Render thread/INFO]: Registering Mod Info...
[16:35:59] [Render thread/INFO]: Registering Mod Info took 212.7 microseconds
[16:35:59] [Render thread/INFO]: Registering categories...
[16:35:59] [Render thread/INFO]: Registering categories took 356.6 microseconds
[16:35:59] [Render thread/INFO]: Registering vanilla category extensions...
[16:35:59] [Render thread/INFO]: Registering vanilla category extensions took 163.7 microseconds
[16:35:59] [Render thread/INFO]: Registering recipe catalysts...
[16:35:59] [Render thread/INFO]: Registering recipe catalysts took 197.0 microseconds
[16:35:59] [Render thread/INFO]: Building recipe registry...
[16:35:59] [Render thread/INFO]: Building recipe registry took 174.6 microseconds
[16:35:59] [Render thread/INFO]: Registering advanced plugins...
[16:35:59] [Render thread/INFO]: Registering advanced plugins took 136.1 microseconds
[16:35:59] [Render thread/INFO]: Registering recipes...
[16:35:59] [Render thread/INFO]: Registering recipes: jei:minecraft took 11.84 milliseconds
[16:35:59] [Render thread/INFO]: Registering recipes took 12.36 milliseconds
[16:35:59] [Render thread/INFO]: Registering recipes transfer handlers...
[16:35:59] [Render thread/INFO]: Registering recipes transfer handlers took 265.5 microseconds
[16:35:59] [Render thread/INFO]: Building runtime...
[16:35:59] [Render thread/INFO]: Registering gui handlers...
[16:35:59] [Render thread/INFO]: Registering gui handlers took 383.5 microseconds
[16:35:59] [Render thread/INFO]: Registering Runtime...
[16:35:59] [Render thread/INFO]: Starting JEI GUI
[16:35:59] [Render thread/INFO]: Building ingredient list...
[16:35:59] [Render thread/INFO]: Building ingredient list took 3.945 milliseconds
[16:35:59] [Render thread/INFO]: Building ingredient filter...
[16:35:59] [Render thread/INFO]: Adding 1688 ingredients
[16:35:59] [Render thread/INFO]: Added 1688 ingredients
[16:35:59] [Render thread/INFO]: Building ingredient filter took 36.93 milliseconds
[16:35:59] [Render thread/INFO]: Registering Runtime: jei:fabric_gui took 44.11 milliseconds
[16:35:59] [Render thread/INFO]: Registering Runtime took 44.47 milliseconds
[16:35:59] [Render thread/INFO]: Building runtime took 45.08 milliseconds
[16:35:59] [Render thread/INFO]: Sending Runtime...
[16:35:59] [Render thread/INFO]: Sending Runtime took 222.0 microseconds
[16:35:59] [Render thread/INFO]: Starting JEI took 69.08 milliseconds
[16:35:59] [Render thread/INFO]: Stopping JEI
[16:35:59] [Render thread/INFO]: Stopping JEI
[16:35:59] [Render thread/INFO]: Sending Runtime Unavailable...
[16:35:59] [Render thread/INFO]: Stopping JEI GUI
[16:35:59] [Render thread/INFO]: Sending Runtime Unavailable took 138.7 microseconds
[16:35:59] [Render thread/INFO]: Starting JEI...
[16:35:59] [Render thread/INFO]: Registering item subtypes...
[16:35:59] [Render thread/INFO]: Registering item subtypes took 108.7 microseconds
[16:35:59] [Render thread/INFO]: Registering fluid subtypes...
[16:35:59] [Render thread/INFO]: Registering fluid subtypes took 70.00 microseconds
[16:35:59] [Render thread/INFO]: Registering ingredients...
[16:35:59] [Render thread/INFO]: Registering ingredients took 5.840 milliseconds
[16:35:59] [Render thread/INFO]: Registering extra ingredients...
[16:35:59] [Render thread/INFO]: Registering extra ingredients took 279.1 microseconds
[16:35:59] [Render thread/INFO]: Registering search ingredient aliases...
[16:35:59] [Render thread/INFO]: Registering search ingredient aliases took 181.8 microseconds
[16:35:59] [Render thread/INFO]: Registering Mod Info...
[16:35:59] [Render thread/INFO]: Registering Mod Info took 341.7 microseconds
[16:35:59] [Render thread/INFO]: Registering categories...
[16:35:59] [Render thread/INFO]: Registering categories took 406.8 microseconds
[16:35:59] [Render thread/INFO]: Registering vanilla category extensions...
[16:35:59] [Render thread/INFO]: Registering vanilla category extensions took 227.0 microseconds
[16:35:59] [Render thread/INFO]: Registering recipe catalysts...
[16:35:59] [Render thread/INFO]: Registering recipe catalysts took 243.0 microseconds
[16:35:59] [Render thread/INFO]: Building recipe registry...
[16:35:59] [Render thread/INFO]: Building recipe registry took 227.5 microseconds
[16:35:59] [Render thread/INFO]: Registering advanced plugins...
[16:35:59] [Render thread/INFO]: Registering advanced plugins took 244.6 microseconds
[16:35:59] [Render thread/INFO]: Registering recipes...
[16:35:59] [Render thread/INFO]: Registering recipes: jei:minecraft took 12.98 milliseconds
[16:35:59] [Render thread/INFO]: Registering recipes took 13.41 milliseconds
[16:35:59] [Render thread/INFO]: Registering recipes transfer handlers...
[16:35:59] [Render thread/INFO]: Registering recipes transfer handlers took 362.1 microseconds
[16:35:59] [Render thread/INFO]: Building runtime...
[16:35:59] [Render thread/INFO]: Registering gui handlers...
[16:35:59] [Render thread/INFO]: Registering gui handlers took 289.2 microseconds
[16:35:59] [Render thread/INFO]: Registering Runtime...
[16:35:59] [Render thread/INFO]: Starting JEI GUI
[16:35:59] [Render thread/INFO]: Building ingredient list...
[16:35:59] [Render thread/INFO]: Building ingredient list took 4.752 milliseconds
[16:35:59] [Render thread/INFO]: Building ingredient filter...
[16:35:59] [Render thread/INFO]: Adding 1688 ingredients
[16:35:59] [Render thread/INFO]: Added 1688 ingredients
[16:35:59] [Render thread/INFO]: Building ingredient filter took 35.61 milliseconds
[16:35:59] [Render thread/INFO]: Registering Runtime: jei:fabric_gui took 43.58 milliseconds
[16:35:59] [Render thread/INFO]: Registering Runtime took 43.96 milliseconds
[16:35:59] [Render thread/INFO]: Building runtime took 44.44 milliseconds
[16:35:59] [Render thread/INFO]: Sending Runtime...
[16:35:59] [Render thread/INFO]: Sending Runtime took 206.5 microseconds
[16:35:59] [Render thread/INFO]: Starting JEI took 69.53 milliseconds
[16:35:59] [Render thread/INFO]: Loaded 2 advancements
[16:36:02] [Server thread/INFO]: [F0meG04: Killed F0meG04]
[16:36:02] [Render thread/INFO]: [System] [CHAT] Killed F0meG04
[16:36:02] [Render thread/INFO]: [System] [CHAT] <Ngoc_Cutee> gg ez
[16:36:02] [Render thread/INFO]: Minimap updated server level id: -1755197486 for world ResourceKey[minecraft:dimension / minecraft:overworld]
[16:36:04] [Render thread/INFO]: [System] [CHAT] Your game mode has been updated to Survival Mode
[16:36:09] [Render thread/INFO]: [System] [CHAT] Your game mode has been updated to Adventure Mode
[16:36:10] [Server thread/INFO]: Ngoc_Cutee has made the advancement [Cover Me with Diamonds]
[16:36:10] [Server thread/INFO]: Ngoc_Cutee has made the advancement [Ice Bucket Challenge]
[16:36:11] [Render thread/INFO]: [System] [CHAT] Ngoc_Cutee has made the advancement [Cover Me with Diamonds]
[16:36:11] [Render thread/INFO]: [System] [CHAT] Ngoc_Cutee has made the advancement [Ice Bucket Challenge]
[16:36:19] [Render thread/INFO]: [System] [CHAT] Your game mode has been updated to Survival Mode
[16:36:21] [Render thread/INFO]: [System] [CHAT] Your game mode has been updated to Adventure Mode
[16:41:40] [Server thread/INFO]: [F0meG04: Running function practicebot:reset]
[16:41:40] [Render thread/INFO]: [System] [CHAT] Running function practicebot:reset
[16:41:43] [Server thread/INFO]: Loaded 1290 recipes
[16:41:43] [Server thread/INFO]: Loaded 1401 advancements
[16:41:43] [Render thread/INFO]: Stopping JEI
[16:41:43] [Render thread/INFO]: Stopping JEI
[16:41:43] [Render thread/INFO]: Sending Runtime Unavailable...
[16:41:43] [Render thread/INFO]: Stopping JEI GUI
[16:41:43] [Render thread/INFO]: Sending Runtime Unavailable took 521.8 microseconds
[16:41:43] [Render thread/INFO]: Starting JEI...
[16:41:43] [Render thread/INFO]: Registering item subtypes...
[16:41:43] [Render thread/INFO]: Registering item subtypes took 179.7 microseconds
[16:41:43] [Render thread/INFO]: Registering fluid subtypes...
[16:41:43] [Render thread/INFO]: Registering fluid subtypes took 135.8 microseconds
[16:41:43] [Render thread/INFO]: Registering ingredients...
[16:41:43] [Render thread/INFO]: Registering ingredients took 5.412 milliseconds
[16:41:43] [Render thread/INFO]: Registering extra ingredients...
[16:41:43] [Render thread/INFO]: Registering extra ingredients took 256.5 microseconds
[16:41:43] [Render thread/INFO]: Registering search ingredient aliases...
[16:41:43] [Render thread/INFO]: Registering search ingredient aliases took 230.0 microseconds
[16:41:43] [Render thread/INFO]: Registering Mod Info...
[16:41:43] [Render thread/INFO]: Registering Mod Info took 183.3 microseconds
[16:41:43] [Render thread/INFO]: Registering categories...
[16:41:43] [Render thread/INFO]: Registering categories took 571.1 microseconds
[16:41:43] [Render thread/INFO]: Registering vanilla category extensions...
[16:41:43] [Render thread/INFO]: Registering vanilla category extensions took 139.2 microseconds
[16:41:43] [Render thread/INFO]: Registering recipe catalysts...
[16:41:43] [Render thread/INFO]: Registering recipe catalysts took 155.1 microseconds
[16:41:43] [Render thread/INFO]: Building recipe registry...
[16:41:43] [Render thread/INFO]: Building recipe registry took 213.7 microseconds
[16:41:43] [Render thread/INFO]: Registering advanced plugins...
[16:41:43] [Render thread/INFO]: Registering advanced plugins took 104.6 microseconds
[16:41:43] [Render thread/INFO]: Registering recipes...
[16:41:43] [Render thread/INFO]: Registering recipes: jei:minecraft took 17.54 milliseconds
[16:41:43] [Render thread/INFO]: Registering recipes took 17.90 milliseconds
[16:41:43] [Render thread/INFO]: Registering recipes transfer handlers...
[16:41:43] [Render thread/INFO]: Registering recipes transfer handlers took 342.7 microseconds
[16:41:43] [Render thread/INFO]: Building runtime...
[16:41:43] [Render thread/INFO]: Registering gui handlers...
[16:41:43] [Render thread/INFO]: Registering gui handlers took 195.6 microseconds
[16:41:43] [Render thread/INFO]: Registering Runtime...
[16:41:43] [Render thread/INFO]: Starting JEI GUI
[16:41:43] [Render thread/INFO]: Building ingredient list...
[16:41:43] [Render thread/INFO]: Building ingredient list took 4.826 milliseconds
[16:41:43] [Render thread/INFO]: Building ingredient filter...
[16:41:43] [Render thread/INFO]: Adding 1688 ingredients
[16:41:44] [Render thread/INFO]: Added 1688 ingredients
[16:41:44] [Render thread/INFO]: Building ingredient filter took 37.65 milliseconds
[16:41:44] [Render thread/INFO]: Registering Runtime: jei:fabric_gui took 45.80 milliseconds
[16:41:44] [Render thread/INFO]: Registering Runtime took 46.11 milliseconds
[16:41:44] [Render thread/INFO]: Building runtime took 46.46 milliseconds
[16:41:44] [Render thread/INFO]: Sending Runtime...
[16:41:44] [Render thread/INFO]: Sending Runtime took 187.8 microseconds
[16:41:44] [Render thread/INFO]: Starting JEI took 76.46 milliseconds
[16:41:44] [Render thread/INFO]: Stopping JEI
[16:41:44] [Render thread/INFO]: Stopping JEI
[16:41:44] [Render thread/INFO]: Sending Runtime Unavailable...
[16:41:44] [Render thread/INFO]: Stopping JEI GUI
[16:41:44] [Render thread/INFO]: Sending Runtime Unavailable took 137.0 microseconds
[16:41:44] [Render thread/INFO]: Starting JEI...
[16:41:44] [Render thread/INFO]: Registering item subtypes...
[16:41:44] [Render thread/INFO]: Registering item subtypes took 108.4 microseconds
[16:41:44] [Render thread/INFO]: Registering fluid subtypes...
[16:41:44] [Render thread/INFO]: Registering fluid subtypes took 88.00 microseconds
[16:41:44] [Render thread/INFO]: Registering ingredients...
[16:41:44] [Render thread/INFO]: Registering ingredients took 5.345 milliseconds
[16:41:44] [Render thread/INFO]: Registering extra ingredients...
[16:41:44] [Render thread/INFO]: Registering extra ingredients took 279.5 microseconds
[16:41:44] [Render thread/INFO]: Registering search ingredient aliases...
[16:41:44] [Render thread/INFO]: Registering search ingredient aliases took 152.0 microseconds
[16:41:44] [Render thread/INFO]: Registering Mod Info...
[16:41:44] [Render thread/INFO]: Registering Mod Info took 239.2 microseconds
[16:41:44] [Render thread/INFO]: Registering categories...
[16:41:44] [Render thread/INFO]: Registering categories took 295.0 microseconds
[16:41:44] [Render thread/INFO]: Registering vanilla category extensions...
[16:41:44] [Render thread/INFO]: Registering vanilla category extensions took 145.3 microseconds
[16:41:44] [Render thread/INFO]: Registering recipe catalysts...
[16:41:44] [Render thread/INFO]: Registering recipe catalysts took 154.9 microseconds
[16:41:44] [Render thread/INFO]: Building recipe registry...
[16:41:44] [Render thread/INFO]: Building recipe registry took 165.1 microseconds
[16:41:44] [Render thread/INFO]: Registering advanced plugins...
[16:41:44] [Render thread/INFO]: Registering advanced plugins took 176.2 microseconds
[16:41:44] [Render thread/INFO]: Registering recipes...
[16:41:44] [Render thread/INFO]: Registering recipes: jei:minecraft took 13.32 milliseconds
[16:41:44] [Render thread/INFO]: Registering recipes took 13.75 milliseconds
[16:41:44] [Render thread/INFO]: Registering recipes transfer handlers...
[16:41:44] [Render thread/INFO]: Registering recipes transfer handlers took 239.0 microseconds
[16:41:44] [Render thread/INFO]: Building runtime...
[16:41:44] [Render thread/INFO]: Registering gui handlers...
[16:41:44] [Render thread/INFO]: Registering gui handlers took 222.9 microseconds
[16:41:44] [Render thread/INFO]: Registering Runtime...
[16:41:44] [Render thread/INFO]: Starting JEI GUI
[16:41:44] [Render thread/INFO]: Building ingredient list...
[16:41:44] [Render thread/INFO]: Building ingredient list took 4.871 milliseconds
[16:41:44] [Render thread/INFO]: Building ingredient filter...
[16:41:44] [Render thread/INFO]: Adding 1688 ingredients
[16:41:44] [Render thread/INFO]: Added 1688 ingredients
[16:41:44] [Render thread/INFO]: Building ingredient filter took 48.86 milliseconds
[16:41:44] [Render thread/INFO]: Registering Runtime: jei:fabric_gui took 58.82 milliseconds
[16:41:44] [Render thread/INFO]: Registering Runtime took 59.30 milliseconds
[16:41:44] [Render thread/INFO]: Building runtime took 59.75 milliseconds
[16:41:44] [Render thread/INFO]: Sending Runtime...
[16:41:44] [Render thread/INFO]: Sending Runtime took 323.8 microseconds
[16:41:44] [Render thread/INFO]: Starting JEI took 83.90 milliseconds
[16:41:44] [Render thread/INFO]: Stopping JEI
[16:41:44] [Render thread/INFO]: Stopping JEI
[16:41:44] [Render thread/INFO]: Sending Runtime Unavailable...
[16:41:44] [Render thread/INFO]: Stopping JEI GUI
[16:41:44] [Render thread/INFO]: Sending Runtime Unavailable took 177.1 microseconds
[16:41:44] [Render thread/INFO]: Starting JEI...
[16:41:44] [Render thread/INFO]: Registering item subtypes...
[16:41:44] [Render thread/INFO]: Registering item subtypes took 129.3 microseconds
[16:41:44] [Render thread/INFO]: Registering fluid subtypes...
[16:41:44] [Render thread/INFO]: Registering fluid subtypes took 75.10 microseconds
[16:41:44] [Render thread/INFO]: Registering ingredients...
[16:41:44] [Render thread/INFO]: Registering ingredients took 6.147 milliseconds
[16:41:44] [Render thread/INFO]: Registering extra ingredients...
[16:41:44] [Render thread/INFO]: Registering extra ingredients took 277.7 microseconds
[16:41:44] [Render thread/INFO]: Registering search ingredient aliases...
[16:41:44] [Render thread/INFO]: Registering search ingredient aliases took 183.4 microseconds
[16:41:44] [Render thread/INFO]: Registering Mod Info...
[16:41:44] [Render thread/INFO]: Registering Mod Info took 278.3 microseconds
[16:41:44] [Render thread/INFO]: Registering categories...
[16:41:44] [Render thread/INFO]: Registering categories took 524.7 microseconds
[16:41:44] [Render thread/INFO]: Registering vanilla category extensions...
[16:41:44] [Render thread/INFO]: Registering vanilla category extensions took 187.2 microseconds
[16:41:44] [Render thread/INFO]: Registering recipe catalysts...
[16:41:44] [Render thread/INFO]: Registering recipe catalysts took 161.8 microseconds
[16:41:44] [Render thread/INFO]: Building recipe registry...
[16:41:44] [Render thread/INFO]: Building recipe registry took 228.3 microseconds
[16:41:44] [Render thread/INFO]: Registering advanced plugins...
[16:41:44] [Render thread/INFO]: Registering advanced plugins took 136.8 microseconds
[16:41:44] [Render thread/INFO]: Registering recipes...
[16:41:44] [Render thread/INFO]: Registering recipes: jei:minecraft took 18.07 milliseconds
[16:41:44] [Render thread/INFO]: Registering recipes took 18.46 milliseconds
[16:41:44] [Render thread/INFO]: Registering recipes transfer handlers...
[16:41:44] [Render thread/INFO]: Registering recipes transfer handlers took 301.0 microseconds
[16:41:44] [Render thread/INFO]: Building runtime...
[16:41:44] [Render thread/INFO]: Registering gui handlers...
[16:41:44] [Render thread/INFO]: Registering gui handlers took 384.1 microseconds
[16:41:44] [Render thread/INFO]: Registering Runtime...
[16:41:44] [Render thread/INFO]: Starting JEI GUI
[16:41:44] [Render thread/INFO]: Building ingredient list...
[16:41:44] [Render thread/INFO]: Building ingredient list took 5.633 milliseconds
[16:41:44] [Render thread/INFO]: Building ingredient filter...
[16:41:44] [Render thread/INFO]: Adding 1688 ingredients
[16:41:44] [Render thread/INFO]: Added 1688 ingredients
[16:41:44] [Render thread/INFO]: Building ingredient filter took 33.37 milliseconds
[16:41:44] [Render thread/INFO]: Registering Runtime: jei:fabric_gui took 42.33 milliseconds
[16:41:44] [Render thread/INFO]: Registering Runtime took 42.71 milliseconds
[16:41:44] [Render thread/INFO]: Building runtime took 43.32 milliseconds
[16:41:44] [Render thread/INFO]: Sending Runtime...
[16:41:44] [Render thread/INFO]: Sending Runtime took 295.4 microseconds
[16:41:44] [Render thread/INFO]: Starting JEI took 73.94 milliseconds
[16:41:44] [Render thread/INFO]: Loaded 2 advancements
[16:41:51] [Server thread/INFO]: Ngoc_Cutee lost connection: Killed
[16:41:51] [Server thread/INFO]: Ngoc_Cutee left the game
[16:41:51] [Render thread/INFO]: [System] [CHAT] Ngoc_Cutee left the game
[16:41:57] [Server thread/INFO]: [F0meG04: Player Ngoc_Cutee is already logged on]
[16:41:57] [Render thread/INFO]: [System] [CHAT] Player Ngoc_Cutee is already logged on
[16:41:58] [Render thread/WARN]: Ignoring player info update for unknown player cf49c072-f5b0-43fb-b3e7-9458855a2652 ([UPDATE_LATENCY])
[16:42:03] [Server thread/INFO]: [F0meG04: Reloading!]
[16:42:03] [Render thread/INFO]: [System] [CHAT] Reloading!
[16:42:03] [Server thread/INFO]: Loaded 1290 recipes
[16:42:03] [Server thread/INFO]: Loaded 1401 advancements
[16:42:03] [Render thread/INFO]: Stopping JEI
[16:42:03] [Render thread/INFO]: Stopping JEI
[16:42:03] [Render thread/INFO]: Sending Runtime Unavailable...
[16:42:03] [Render thread/INFO]: Stopping JEI GUI
[16:42:03] [Render thread/INFO]: Sending Runtime Unavailable took 491.3 microseconds
[16:42:03] [Render thread/INFO]: Starting JEI...
[16:42:03] [Render thread/INFO]: Registering item subtypes...
[16:42:03] [Render thread/INFO]: Registering item subtypes took 182.1 microseconds
[16:42:03] [Render thread/INFO]: Registering fluid subtypes...
[16:42:03] [Render thread/INFO]: Registering fluid subtypes took 110.9 microseconds
[16:42:03] [Render thread/INFO]: Registering ingredients...
[16:42:03] [Render thread/INFO]: Registering ingredients took 5.649 milliseconds
[16:42:03] [Render thread/INFO]: Registering extra ingredients...
[16:42:03] [Render thread/INFO]: Registering extra ingredients took 204.0 microseconds
[16:42:03] [Render thread/INFO]: Registering search ingredient aliases...
[16:42:03] [Render thread/INFO]: Registering search ingredient aliases took 176.0 microseconds
[16:42:03] [Render thread/INFO]: Registering Mod Info...
[16:42:03] [Render thread/INFO]: Registering Mod Info took 217.7 microseconds
[16:42:03] [Render thread/INFO]: Registering categories...
[16:42:03] [Render thread/INFO]: Registering categories took 398.2 microseconds
[16:42:03] [Render thread/INFO]: Registering vanilla category extensions...
[16:42:03] [Render thread/INFO]: Registering vanilla category extensions took 166.7 microseconds
[16:42:03] [Render thread/INFO]: Registering recipe catalysts...
[16:42:03] [Render thread/INFO]: Registering recipe catalysts took 182.8 microseconds
[16:42:03] [Render thread/INFO]: Building recipe registry...
[16:42:03] [Render thread/INFO]: Building recipe registry took 246.7 microseconds
[16:42:03] [Render thread/INFO]: Registering advanced plugins...
[16:42:03] [Render thread/INFO]: Registering advanced plugins took 171.2 microseconds
[16:42:03] [Render thread/INFO]: Registering recipes...
[16:42:03] [Render thread/INFO]: Registering recipes: jei:minecraft took 16.74 milliseconds
[16:42:03] [Render thread/INFO]: Registering recipes took 17.17 milliseconds
[16:42:03] [Render thread/INFO]: Registering recipes transfer handlers...
[16:42:03] [Render thread/INFO]: Registering recipes transfer handlers took 315.5 microseconds
[16:42:03] [Render thread/INFO]: Building runtime...
[16:42:03] [Render thread/INFO]: Registering gui handlers...
[16:42:03] [Render thread/INFO]: Registering gui handlers took 258.7 microseconds
[16:42:03] [Render thread/INFO]: Registering Runtime...
[16:42:03] [Render thread/INFO]: Starting JEI GUI
[16:42:03] [Render thread/INFO]: Building ingredient list...
[16:42:03] [Render thread/INFO]: Building ingredient list took 4.698 milliseconds
[16:42:03] [Render thread/INFO]: Building ingredient filter...
[16:42:03] [Render thread/INFO]: Adding 1688 ingredients
[16:42:03] [Render thread/INFO]: Added 1688 ingredients
[16:42:03] [Render thread/INFO]: Building ingredient filter took 36.43 milliseconds
[16:42:03] [Render thread/INFO]: Registering Runtime: jei:fabric_gui took 44.52 milliseconds
[16:42:03] [Render thread/INFO]: Registering Runtime took 44.90 milliseconds
[16:42:03] [Render thread/INFO]: Building runtime took 45.38 milliseconds
[16:42:03] [Render thread/INFO]: Sending Runtime...
[16:42:03] [Render thread/INFO]: Sending Runtime took 227.9 microseconds
[16:42:03] [Render thread/INFO]: Starting JEI took 74.28 milliseconds
[16:42:03] [Render thread/INFO]: Stopping JEI
[16:42:03] [Render thread/INFO]: Stopping JEI
[16:42:03] [Render thread/INFO]: Sending Runtime Unavailable...
[16:42:03] [Render thread/INFO]: Stopping JEI GUI
[16:42:03] [Render thread/INFO]: Sending Runtime Unavailable took 191.6 microseconds
[16:42:03] [Render thread/INFO]: Starting JEI...
[16:42:03] [Render thread/INFO]: Registering item subtypes...
[16:42:03] [Render thread/INFO]: Registering item subtypes took 178.6 microseconds
[16:42:03] [Render thread/INFO]: Registering fluid subtypes...
[16:42:03] [Render thread/INFO]: Registering fluid subtypes took 137.5 microseconds
[16:42:03] [Render thread/INFO]: Registering ingredients...
[16:42:03] [Render thread/INFO]: Registering ingredients took 4.209 milliseconds
[16:42:03] [Render thread/INFO]: Registering extra ingredients...
[16:42:03] [Render thread/INFO]: Registering extra ingredients took 276.9 microseconds
[16:42:03] [Render thread/INFO]: Registering search ingredient aliases...
[16:42:03] [Render thread/INFO]: Registering search ingredient aliases took 229.5 microseconds
[16:42:03] [Render thread/INFO]: Registering Mod Info...
[16:42:03] [Render thread/INFO]: Registering Mod Info took 236.7 microseconds
[16:42:03] [Render thread/INFO]: Registering categories...
[16:42:03] [Render thread/INFO]: Registering categories took 353.7 microseconds
[16:42:03] [Render thread/INFO]: Registering vanilla category extensions...
[16:42:03] [Render thread/INFO]: Registering vanilla category extensions took 207.3 microseconds
[16:42:03] [Render thread/INFO]: Registering recipe catalysts...
[16:42:03] [Render thread/INFO]: Registering recipe catalysts took 166.8 microseconds
[16:42:03] [Render thread/INFO]: Building recipe registry...
[16:42:03] [Render thread/INFO]: Building recipe registry took 213.0 microseconds
[16:42:03] [Render thread/INFO]: Registering advanced plugins...
[16:42:03] [Render thread/INFO]: Registering advanced plugins took 140.5 microseconds
[16:42:03] [Render thread/INFO]: Registering recipes...
[16:42:03] [Render thread/INFO]: Registering recipes: jei:minecraft took 13.10 milliseconds
[16:42:03] [Render thread/INFO]: Registering recipes took 13.47 milliseconds
[16:42:03] [Render thread/INFO]: Registering recipes transfer handlers...
[16:42:03] [Render thread/INFO]: Registering recipes transfer handlers took 270.0 microseconds
[16:42:03] [Render thread/INFO]: Building runtime...
[16:42:03] [Render thread/INFO]: Registering gui handlers...
[16:42:03] [Render thread/INFO]: Registering gui handlers took 288.5 microseconds
[16:42:03] [Render thread/INFO]: Registering Runtime...
[16:42:03] [Render thread/INFO]: Starting JEI GUI
[16:42:03] [Render thread/INFO]: Building ingredient list...
[16:42:03] [Render thread/INFO]: Building ingredient list took 5.131 milliseconds
[16:42:03] [Render thread/INFO]: Building ingredient filter...
[16:42:03] [Render thread/INFO]: Adding 1688 ingredients
[16:42:03] [Render thread/INFO]: Added 1688 ingredients
[16:42:03] [Render thread/INFO]: Building ingredient filter took 52.32 milliseconds
[16:42:03] [Render thread/INFO]: Registering Runtime: jei:fabric_gui took 61.87 milliseconds
[16:42:03] [Render thread/INFO]: Registering Runtime took 62.28 milliseconds
[16:42:03] [Render thread/INFO]: Building runtime took 62.79 milliseconds
[16:42:03] [Render thread/INFO]: Sending Runtime...
[16:42:03] [Render thread/INFO]: Sending Runtime took 323.4 microseconds
[16:42:03] [Render thread/INFO]: Starting JEI took 86.20 milliseconds
[16:42:03] [Render thread/INFO]: Stopping JEI
[16:42:03] [Render thread/INFO]: Stopping JEI
[16:42:03] [Render thread/INFO]: Sending Runtime Unavailable...
[16:42:03] [Render thread/INFO]: Stopping JEI GUI
[16:42:03] [Render thread/INFO]: Sending Runtime Unavailable took 229.8 microseconds
[16:42:03] [Render thread/INFO]: Starting JEI...
[16:42:03] [Render thread/INFO]: Registering item subtypes...
[16:42:03] [Render thread/INFO]: Registering item subtypes took 164.8 microseconds
[16:42:03] [Render thread/INFO]: Registering fluid subtypes...
[16:42:03] [Render thread/INFO]: Registering fluid subtypes took 84.60 microseconds
[16:42:03] [Render thread/INFO]: Registering ingredients...
[16:42:03] [Render thread/INFO]: Registering ingredients took 5.451 milliseconds
[16:42:03] [Render thread/INFO]: Registering extra ingredients...
[16:42:03] [Render thread/INFO]: Registering extra ingredients took 296.6 microseconds
[16:42:03] [Render thread/INFO]: Registering search ingredient aliases...
[16:42:03] [Render thread/INFO]: Registering search ingredient aliases took 198.3 microseconds
[16:42:03] [Render thread/INFO]: Registering Mod Info...
[16:42:03] [Render thread/INFO]: Registering Mod Info took 312.2 microseconds
[16:42:03] [Render thread/INFO]: Registering categories...
[16:42:03] [Render thread/INFO]: Registering categories took 566.2 microseconds
[16:42:03] [Render thread/INFO]: Registering vanilla category extensions...
[16:42:03] [Render thread/INFO]: Registering vanilla category extensions took 214.4 microseconds
[16:42:03] [Render thread/INFO]: Registering recipe catalysts...
[16:42:03] [Render thread/INFO]: Registering recipe catalysts took 228.0 microseconds
[16:42:03] [Render thread/INFO]: Building recipe registry...
[16:42:03] [Render thread/INFO]: Building recipe registry took 242.8 microseconds
[16:42:03] [Render thread/INFO]: Registering advanced plugins...
[16:42:03] [Render thread/INFO]: Registering advanced plugins took 196.6 microseconds
[16:42:03] [Render thread/INFO]: Registering recipes...
[16:42:03] [Render thread/INFO]: Registering recipes: jei:minecraft took 19.01 milliseconds
[16:42:03] [Render thread/INFO]: Registering recipes took 19.50 milliseconds
[16:42:03] [Render thread/INFO]: Registering recipes transfer handlers...
[16:42:03] [Render thread/INFO]: Registering recipes transfer handlers took 317.0 microseconds
[16:42:03] [Render thread/INFO]: Building runtime...
[16:42:03] [Render thread/INFO]: Registering gui handlers...
[16:42:03] [Render thread/INFO]: Registering gui handlers took 379.9 microseconds
[16:42:03] [Render thread/INFO]: Registering Runtime...
[16:42:03] [Render thread/INFO]: Starting JEI GUI
[16:42:03] [Render thread/INFO]: Building ingredient list...
[16:42:03] [Render thread/INFO]: Building ingredient list took 4.968 milliseconds
[16:42:03] [Render thread/INFO]: Building ingredient filter...
[16:42:03] [Render thread/INFO]: Adding 1688 ingredients
[16:42:03] [Render thread/INFO]: Added 1688 ingredients
[16:42:03] [Render thread/INFO]: Building ingredient filter took 32.63 milliseconds
[16:42:03] [Render thread/INFO]: Registering Runtime: jei:fabric_gui took 40.62 milliseconds
[16:42:03] [Render thread/INFO]: Registering Runtime took 40.95 milliseconds
[16:42:03] [Render thread/INFO]: Building runtime took 41.56 milliseconds
[16:42:03] [Render thread/INFO]: Sending Runtime...
[16:42:03] [Render thread/INFO]: Sending Runtime took 201.3 microseconds
[16:42:03] [Render thread/INFO]: Starting JEI took 72.86 milliseconds
[16:42:03] [Render thread/INFO]: Loaded 2 advancements
[16:42:06] [Render thread/WARN]: Ignoring player info update for unknown player cf49c072-f5b0-43fb-b3e7-9458855a2652 ([UPDATE_GAME_MODE])
[16:42:06] [Render thread/INFO]: [System] [CHAT] Your game mode has been updated to Survival Mode
[16:42:12] [Render thread/INFO]: [System] [CHAT] Your game mode has been updated to Adventure Mode
[16:42:12] [Render thread/WARN]: Ignoring player info update for unknown player cf49c072-f5b0-43fb-b3e7-9458855a2652 ([UPDATE_GAME_MODE])
[16:42:15] [Render thread/WARN]: Ignoring player info update for unknown player cf49c072-f5b0-43fb-b3e7-9458855a2652 ([UPDATE_GAME_MODE])
[16:42:15] [Render thread/INFO]: [System] [CHAT] Your game mode has been updated to Survival Mode
[16:42:28] [Render thread/WARN]: Ignoring player info update for unknown player cf49c072-f5b0-43fb-b3e7-9458855a2652 ([UPDATE_LATENCY])
[16:42:28] [Server thread/INFO]: [F0meG04: Killed F0meG04]
[16:42:28] [Render thread/INFO]: [System] [CHAT] Killed F0meG04
[16:42:28] [Render thread/INFO]: Minimap updated server level id: -1755197486 for world ResourceKey[minecraft:dimension / minecraft:overworld]
[16:42:28] [Render thread/WARN]: Ignoring player info update for unknown player cf49c072-f5b0-43fb-b3e7-9458855a2652 ([UPDATE_GAME_MODE])
[16:42:28] [Render thread/INFO]: [System] [CHAT] <Ngoc_Cutee> gg ez
[16:42:28] [Render thread/WARN]: Server attempted to add player prior to sending player info (Player id cf49c072-f5b0-43fb-b3e7-9458855a2652)
[16:42:28] [Render thread/WARN]: Skipping Entity with id entity.minecraft.player
[16:42:31] [Server thread/INFO]: [F0meG04: Reloading!]
[16:42:31] [Render thread/INFO]: [System] [CHAT] Reloading!
[16:42:31] [Server thread/INFO]: Loaded 1290 recipes
[16:42:31] [Server thread/INFO]: Loaded 1401 advancements
[16:42:31] [Render thread/INFO]: Stopping JEI
[16:42:31] [Render thread/INFO]: Stopping JEI
[16:42:31] [Render thread/INFO]: Sending Runtime Unavailable...
[16:42:31] [Render thread/INFO]: Stopping JEI GUI
[16:42:31] [Render thread/INFO]: Sending Runtime Unavailable took 507.9 microseconds
[16:42:31] [Render thread/INFO]: Starting JEI...
[16:42:31] [Render thread/INFO]: Registering item subtypes...
[16:42:31] [Render thread/INFO]: Registering item subtypes took 130.5 microseconds
[16:42:31] [Render thread/INFO]: Registering fluid subtypes...
[16:42:31] [Render thread/INFO]: Registering fluid subtypes took 108.4 microseconds
[16:42:31] [Render thread/INFO]: Registering ingredients...
[16:42:31] [Render thread/INFO]: Registering ingredients took 4.425 milliseconds
[16:42:31] [Render thread/INFO]: Registering extra ingredients...
[16:42:31] [Render thread/INFO]: Registering extra ingredients took 227.1 microseconds
[16:42:31] [Render thread/INFO]: Registering search ingredient aliases...
[16:42:31] [Render thread/INFO]: Registering search ingredient aliases took 166.9 microseconds
[16:42:31] [Render thread/INFO]: Registering Mod Info...
[16:42:31] [Render thread/INFO]: Registering Mod Info took 156.9 microseconds
[16:42:31] [Render thread/INFO]: Registering categories...
[16:42:31] [Render thread/INFO]: Registering categories took 396.7 microseconds
[16:42:31] [Render thread/INFO]: Registering vanilla category extensions...
[16:42:31] [Render thread/INFO]: Registering vanilla category extensions took 197.8 microseconds
[16:42:31] [Render thread/INFO]: Registering recipe catalysts...
[16:42:31] [Render thread/INFO]: Registering recipe catalysts took 152.7 microseconds
[16:42:31] [Render thread/INFO]: Building recipe registry...
[16:42:31] [Render thread/INFO]: Building recipe registry took 267.0 microseconds
[16:42:31] [Render thread/INFO]: Registering advanced plugins...
[16:42:31] [Render thread/INFO]: Registering advanced plugins took 189.0 microseconds
[16:42:31] [Render thread/INFO]: Registering recipes...
[16:42:31] [Render thread/INFO]: Registering recipes: jei:minecraft took 13.88 milliseconds
[16:42:31] [Render thread/INFO]: Registering recipes took 14.31 milliseconds
[16:42:31] [Render thread/INFO]: Registering recipes transfer handlers...
[16:42:31] [Render thread/INFO]: Registering recipes transfer handlers took 246.5 microseconds
[16:42:31] [Render thread/INFO]: Building runtime...
[16:42:31] [Render thread/INFO]: Registering gui handlers...
[16:42:31] [Render thread/INFO]: Registering gui handlers took 234.8 microseconds
[16:42:31] [Render thread/INFO]: Registering Runtime...
[16:42:31] [Render thread/INFO]: Starting JEI GUI
[16:42:31] [Render thread/INFO]: Building ingredient list...
[16:42:31] [Render thread/INFO]: Building ingredient list took 4.461 milliseconds
[16:42:31] [Render thread/INFO]: Building ingredient filter...
[16:42:31] [Render thread/INFO]: Adding 1688 ingredients
[16:42:31] [Render thread/INFO]: Added 1688 ingredients
[16:42:31] [Render thread/INFO]: Building ingredient filter took 27.81 milliseconds
[16:42:31] [Render thread/INFO]: Registering Runtime: jei:fabric_gui took 35.29 milliseconds
[16:42:31] [Render thread/INFO]: Registering Runtime took 35.71 milliseconds
[16:42:31] [Render thread/INFO]: Building runtime took 36.35 milliseconds
[16:42:31] [Render thread/INFO]: Sending Runtime...
[16:42:31] [Render thread/INFO]: Sending Runtime took 195.1 microseconds
[16:42:31] [Render thread/INFO]: Starting JEI took 60.65 milliseconds
[16:42:31] [Render thread/INFO]: Stopping JEI
[16:42:31] [Render thread/INFO]: Stopping JEI
[16:42:31] [Render thread/INFO]: Sending Runtime Unavailable...
[16:42:31] [Render thread/INFO]: Stopping JEI GUI
[16:42:31] [Render thread/INFO]: Sending Runtime Unavailable took 133.0 microseconds
[16:42:31] [Render thread/INFO]: Starting JEI...
[16:42:31] [Render thread/INFO]: Registering item subtypes...
[16:42:31] [Render thread/INFO]: Registering item subtypes took 71.20 microseconds
[16:42:31] [Render thread/INFO]: Registering fluid subtypes...
[16:42:31] [Render thread/INFO]: Registering fluid subtypes took 53.80 microseconds
[16:42:31] [Render thread/INFO]: Registering ingredients...
[16:42:31] [Render thread/INFO]: Registering ingredients took 3.523 milliseconds
[16:42:31] [Render thread/INFO]: Registering extra ingredients...
[16:42:31] [Render thread/INFO]: Registering extra ingredients took 175.1 microseconds
[16:42:31] [Render thread/INFO]: Registering search ingredient aliases...
[16:42:31] [Render thread/INFO]: Registering search ingredient aliases took 140.2 microseconds
[16:42:31] [Render thread/INFO]: Registering Mod Info...
[16:42:31] [Render thread/INFO]: Registering Mod Info took 179.9 microseconds
[16:42:31] [Render thread/INFO]: Registering categories...
[16:42:31] [Render thread/INFO]: Registering categories took 445.7 microseconds
[16:42:31] [Render thread/INFO]: Registering vanilla category extensions...
[16:42:31] [Render thread/INFO]: Registering vanilla category extensions took 155.6 microseconds
[16:42:31] [Render thread/INFO]: Registering recipe catalysts...
[16:42:31] [Render thread/INFO]: Registering recipe catalysts took 131.0 microseconds
[16:42:31] [Render thread/INFO]: Building recipe registry...
[16:42:31] [Render thread/INFO]: Building recipe registry took 164.3 microseconds
[16:42:31] [Render thread/INFO]: Registering advanced plugins...
[16:42:31] [Render thread/INFO]: Registering advanced plugins took 81.40 microseconds
[16:42:31] [Render thread/INFO]: Registering recipes...
[16:42:31] [Render thread/INFO]: Registering recipes took 10.93 milliseconds
[16:42:31] [Render thread/INFO]: Registering recipes transfer handlers...
[16:42:31] [Render thread/INFO]: Registering recipes transfer handlers took 249.3 microseconds
[16:42:31] [Render thread/INFO]: Building runtime...
[16:42:31] [Render thread/INFO]: Registering gui handlers...
[16:42:31] [Render thread/INFO]: Registering gui handlers took 205.6 microseconds
[16:42:31] [Render thread/INFO]: Registering Runtime...
[16:42:31] [Render thread/INFO]: Starting JEI GUI
[16:42:31] [Render thread/INFO]: Building ingredient list...
[16:42:31] [Render thread/INFO]: Building ingredient list took 3.933 milliseconds
[16:42:31] [Render thread/INFO]: Building ingredient filter...
[16:42:31] [Render thread/INFO]: Adding 1688 ingredients
[16:42:31] [Render thread/INFO]: Added 1688 ingredients
[16:42:31] [Render thread/INFO]: Building ingredient filter took 28.13 milliseconds
[16:42:31] [Render thread/INFO]: Registering Runtime: jei:fabric_gui took 57.27 milliseconds
[16:42:31] [Render thread/INFO]: Registering Runtime took 57.95 milliseconds
[16:42:31] [Render thread/INFO]: Building runtime took 58.39 milliseconds
[16:42:31] [Render thread/INFO]: Sending Runtime...
[16:42:31] [Render thread/INFO]: Sending Runtime took 330.9 microseconds
[16:42:31] [Render thread/INFO]: Starting JEI took 77.48 milliseconds
[16:42:31] [Render thread/INFO]: Stopping JEI
[16:42:31] [Render thread/INFO]: Stopping JEI
[16:42:31] [Render thread/INFO]: Sending Runtime Unavailable...
[16:42:31] [Render thread/INFO]: Stopping JEI GUI
[16:42:31] [Render thread/INFO]: Sending Runtime Unavailable took 155.1 microseconds
[16:42:31] [Render thread/INFO]: Starting JEI...
[16:42:31] [Render thread/INFO]: Registering item subtypes...
[16:42:31] [Render thread/INFO]: Registering item subtypes took 165.2 microseconds
[16:42:31] [Render thread/INFO]: Registering fluid subtypes...
[16:42:31] [Render thread/INFO]: Registering fluid subtypes took 120.1 microseconds
[16:42:31] [Render thread/INFO]: Registering ingredients...
[16:42:31] [Render thread/INFO]: Registering ingredients took 6.519 milliseconds
[16:42:31] [Render thread/INFO]: Registering extra ingredients...
[16:42:31] [Render thread/INFO]: Registering extra ingredients took 246.2 microseconds
[16:42:31] [Render thread/INFO]: Registering search ingredient aliases...
[16:42:31] [Render thread/INFO]: Registering search ingredient aliases took 197.8 microseconds
[16:42:31] [Render thread/INFO]: Registering Mod Info...
[16:42:31] [Render thread/INFO]: Registering Mod Info took 201.4 microseconds
[16:42:31] [Render thread/INFO]: Registering categories...
[16:42:31] [Render thread/INFO]: Registering categories took 439.7 microseconds
[16:42:31] [Render thread/INFO]: Registering vanilla category extensions...
[16:42:31] [Render thread/INFO]: Registering vanilla category extensions took 139.8 microseconds
[16:42:31] [Render thread/INFO]: Registering recipe catalysts...
[16:42:31] [Render thread/INFO]: Registering recipe catalysts took 110.2 microseconds
[16:42:31] [Render thread/INFO]: Building recipe registry...
[16:42:31] [Render thread/INFO]: Building recipe registry took 289.7 microseconds
[16:42:31] [Render thread/INFO]: Registering advanced plugins...
[16:42:31] [Render thread/INFO]: Registering advanced plugins took 178.4 microseconds
[16:42:31] [Render thread/INFO]: Registering recipes...
[16:42:31] [Render thread/INFO]: Registering recipes: jei:minecraft took 17.29 milliseconds
[16:42:31] [Render thread/INFO]: Registering recipes took 17.71 milliseconds
[16:42:31] [Render thread/INFO]: Registering recipes transfer handlers...
[16:42:31] [Render thread/INFO]: Registering recipes transfer handlers took 257.6 microseconds
[16:42:31] [Render thread/INFO]: Building runtime...
[16:42:31] [Render thread/INFO]: Registering gui handlers...
[16:42:31] [Render thread/INFO]: Registering gui handlers took 201.3 microseconds
[16:42:31] [Render thread/INFO]: Registering Runtime...
[16:42:31] [Render thread/INFO]: Starting JEI GUI
[16:42:31] [Render thread/INFO]: Building ingredient list...
[16:42:31] [Render thread/INFO]: Building ingredient list took 4.367 milliseconds
[16:42:31] [Render thread/INFO]: Building ingredient filter...
[16:42:31] [Render thread/INFO]: Adding 1688 ingredients
[16:42:31] [Render thread/INFO]: Added 1688 ingredients
[16:42:31] [Render thread/INFO]: Building ingredient filter took 34.78 milliseconds
[16:42:31] [Render thread/INFO]: Registering Runtime: jei:fabric_gui took 42.88 milliseconds
[16:42:31] [Render thread/INFO]: Registering Runtime took 43.22 milliseconds
[16:42:31] [Render thread/INFO]: Building runtime took 43.61 milliseconds
[16:42:31] [Render thread/INFO]: Sending Runtime...
[16:42:31] [Render thread/INFO]: Sending Runtime took 178.7 microseconds
[16:42:31] [Render thread/INFO]: Starting JEI took 73.52 milliseconds
[16:42:31] [Render thread/INFO]: Loaded 2 advancements
[16:42:39] [Server thread/INFO]: [F0meG04: Player Ngoc_Cutee is already logged on]
[16:42:39] [Render thread/INFO]: [System] [CHAT] Player Ngoc_Cutee is already logged on
[16:42:41] [Server thread/INFO]: Ngoc_Cutee lost connection: Killed
[16:42:41] [Server thread/INFO]: Ngoc_Cutee left the game
[16:42:41] [Render thread/INFO]: [System] [CHAT] Ngoc_Cutee left the game
[16:42:43] [Server thread/INFO]: Ngoc_Cutee[local] logged in with entity id 712 at (8.194878788236597, 13.0, 7.547247413332503)
[16:42:43] [Server thread/INFO]: Ngoc_Cutee joined the game
[16:42:43] [Render thread/INFO]: [System] [CHAT] Ngoc_Cutee joined the game
[16:42:45] [Server thread/INFO]: [F0meG04: Reloading!]
[16:42:45] [Render thread/INFO]: [System] [CHAT] Reloading!
[16:42:45] [Server thread/INFO]: Loaded 1290 recipes
[16:42:45] [Server thread/INFO]: Loaded 1401 advancements
[16:42:45] [Render thread/INFO]: Stopping JEI
[16:42:45] [Render thread/INFO]: Stopping JEI
[16:42:45] [Render thread/INFO]: Sending Runtime Unavailable...
[16:42:45] [Render thread/INFO]: Stopping JEI GUI
[16:42:45] [Render thread/INFO]: Sending Runtime Unavailable took 509.9 microseconds
[16:42:45] [Render thread/INFO]: Starting JEI...
[16:42:45] [Render thread/INFO]: Registering item subtypes...
[16:42:45] [Render thread/INFO]: Registering item subtypes took 211.3 microseconds
[16:42:45] [Render thread/INFO]: Registering fluid subtypes...
[16:42:45] [Render thread/INFO]: Registering fluid subtypes took 150.5 microseconds
[16:42:45] [Render thread/INFO]: Registering ingredients...
[16:42:45] [Render thread/INFO]: Registering ingredients took 4.440 milliseconds
[16:42:45] [Render thread/INFO]: Registering extra ingredients...
[16:42:45] [Render thread/INFO]: Registering extra ingredients took 244.2 microseconds
[16:42:45] [Render thread/INFO]: Registering search ingredient aliases...
[16:42:45] [Render thread/INFO]: Registering search ingredient aliases took 263.9 microseconds
[16:42:45] [Render thread/INFO]: Registering Mod Info...
[16:42:45] [Render thread/INFO]: Registering Mod Info took 362.6 microseconds
[16:42:45] [Render thread/INFO]: Registering categories...
[16:42:45] [Render thread/INFO]: Registering categories took 519.8 microseconds
[16:42:45] [Render thread/INFO]: Registering vanilla category extensions...
[16:42:45] [Render thread/INFO]: Registering vanilla category extensions took 260.8 microseconds
[16:42:45] [Render thread/INFO]: Registering recipe catalysts...
[16:42:45] [Render thread/INFO]: Registering recipe catalysts took 180.5 microseconds
[16:42:45] [Render thread/INFO]: Building recipe registry...
[16:42:45] [Render thread/INFO]: Building recipe registry took 208.4 microseconds
[16:42:45] [Render thread/INFO]: Registering advanced plugins...
[16:42:45] [Render thread/INFO]: Registering advanced plugins took 133.0 microseconds
[16:42:45] [Render thread/INFO]: Registering recipes...
[16:42:45] [Render thread/INFO]: Registering recipes: jei:minecraft took 17.86 milliseconds
[16:42:45] [Render thread/INFO]: Registering recipes took 18.27 milliseconds
[16:42:45] [Render thread/INFO]: Registering recipes transfer handlers...
[16:42:45] [Render thread/INFO]: Registering recipes transfer handlers took 410.8 microseconds
[16:42:45] [Render thread/INFO]: Building runtime...
[16:42:45] [Render thread/INFO]: Registering gui handlers...
[16:42:45] [Render thread/INFO]: Registering gui handlers took 296.0 microseconds
[16:42:45] [Render thread/INFO]: Registering Runtime...
[16:42:45] [Render thread/INFO]: Starting JEI GUI
[16:42:45] [Render thread/INFO]: Building ingredient list...
[16:42:45] [Render thread/INFO]: Building ingredient list took 3.966 milliseconds
[16:42:45] [Render thread/INFO]: Building ingredient filter...
[16:42:45] [Render thread/INFO]: Adding 1688 ingredients
[16:42:45] [Render thread/INFO]: Added 1688 ingredients
[16:42:45] [Render thread/INFO]: Building ingredient filter took 27.42 milliseconds
[16:42:45] [Render thread/INFO]: Registering Runtime: jei:fabric_gui took 34.40 milliseconds
[16:42:45] [Render thread/INFO]: Registering Runtime took 34.79 milliseconds
[16:42:45] [Render thread/INFO]: Building runtime took 35.36 milliseconds
[16:42:45] [Render thread/INFO]: Sending Runtime...
[16:42:45] [Render thread/INFO]: Sending Runtime took 218.7 microseconds
[16:42:45] [Render thread/INFO]: Starting JEI took 65.46 milliseconds
[16:42:45] [Render thread/INFO]: Stopping JEI
[16:42:45] [Render thread/INFO]: Stopping JEI
[16:42:45] [Render thread/INFO]: Sending Runtime Unavailable...
[16:42:45] [Render thread/INFO]: Stopping JEI GUI
[16:42:45] [Render thread/INFO]: Sending Runtime Unavailable took 124.5 microseconds
[16:42:45] [Render thread/INFO]: Starting JEI...
[16:42:45] [Render thread/INFO]: Registering item subtypes...
[16:42:45] [Render thread/INFO]: Registering item subtypes took 110.2 microseconds
[16:42:45] [Render thread/INFO]: Registering fluid subtypes...
[16:42:45] [Render thread/INFO]: Registering fluid subtypes took 89.80 microseconds
[16:42:45] [Render thread/INFO]: Registering ingredients...
[16:42:45] [Render thread/INFO]: Registering ingredients took 5.278 milliseconds
[16:42:45] [Render thread/INFO]: Registering extra ingredients...
[16:42:45] [Render thread/INFO]: Registering extra ingredients took 735.4 microseconds
[16:42:45] [Render thread/INFO]: Registering search ingredient aliases...
[16:42:45] [Render thread/INFO]: Registering search ingredient aliases took 235.0 microseconds
[16:42:45] [Render thread/INFO]: Registering Mod Info...
[16:42:45] [Render thread/INFO]: Registering Mod Info took 224.8 microseconds
[16:42:45] [Render thread/INFO]: Registering categories...
[16:42:45] [Render thread/INFO]: Registering categories took 257.1 microseconds
[16:42:45] [Render thread/INFO]: Registering vanilla category extensions...
[16:42:45] [Render thread/INFO]: Registering vanilla category extensions took 105.6 microseconds
[16:42:45] [Render thread/INFO]: Registering recipe catalysts...
[16:42:45] [Render thread/INFO]: Registering recipe catalysts took 84.10 microseconds
[16:42:45] [Render thread/INFO]: Building recipe registry...
[16:42:45] [Render thread/INFO]: Building recipe registry took 132.1 microseconds
[16:42:45] [Render thread/INFO]: Registering advanced plugins...
[16:42:45] [Render thread/INFO]: Registering advanced plugins took 102.3 microseconds
[16:42:45] [Render thread/INFO]: Registering recipes...
[16:42:45] [Render thread/INFO]: Registering recipes took 10.91 milliseconds
[16:42:45] [Render thread/INFO]: Registering recipes transfer handlers...
[16:42:45] [Render thread/INFO]: Registering recipes transfer handlers took 547.6 microseconds
[16:42:45] [Render thread/INFO]: Building runtime...
[16:42:45] [Render thread/INFO]: Registering gui handlers...
[16:42:45] [Render thread/INFO]: Registering gui handlers took 205.8 microseconds
[16:42:45] [Render thread/INFO]: Registering Runtime...
[16:42:45] [Render thread/INFO]: Starting JEI GUI
[16:42:45] [Render thread/INFO]: Building ingredient list...
[16:42:45] [Render thread/INFO]: Building ingredient list took 3.366 milliseconds
[16:42:45] [Render thread/INFO]: Building ingredient filter...
[16:42:45] [Render thread/INFO]: Adding 1688 ingredients
[16:42:45] [Render thread/INFO]: Added 1688 ingredients
[16:42:45] [Render thread/INFO]: Building ingredient filter took 29.59 milliseconds
[16:42:45] [Render thread/INFO]: Registering Runtime: jei:fabric_gui took 35.50 milliseconds
[16:42:45] [Render thread/INFO]: Registering Runtime took 35.82 milliseconds
[16:42:45] [Render thread/INFO]: Building runtime took 36.20 milliseconds
[16:42:45] [Render thread/INFO]: Sending Runtime...
[16:42:45] [Render thread/INFO]: Sending Runtime took 167.8 microseconds
[16:42:45] [Render thread/INFO]: Starting JEI took 59.39 milliseconds
[16:42:45] [Render thread/INFO]: Stopping JEI
[16:42:45] [Render thread/INFO]: Stopping JEI
[16:42:45] [Render thread/INFO]: Sending Runtime Unavailable...
[16:42:45] [Render thread/INFO]: Stopping JEI GUI
[16:42:45] [Render thread/INFO]: Sending Runtime Unavailable took 123.0 microseconds
[16:42:45] [Render thread/INFO]: Starting JEI...
[16:42:45] [Render thread/INFO]: Registering item subtypes...
[16:42:45] [Render thread/INFO]: Registering item subtypes took 145.6 microseconds
[16:42:45] [Render thread/INFO]: Registering fluid subtypes...
[16:42:45] [Render thread/INFO]: Registering fluid subtypes took 85.60 microseconds
[16:42:45] [Render thread/INFO]: Registering ingredients...
[16:42:45] [Render thread/INFO]: Registering ingredients took 4.829 milliseconds
[16:42:45] [Render thread/INFO]: Registering extra ingredients...
[16:42:45] [Render thread/INFO]: Registering extra ingredients took 276.1 microseconds
[16:42:45] [Render thread/INFO]: Registering search ingredient aliases...
[16:42:45] [Render thread/INFO]: Registering search ingredient aliases took 202.2 microseconds
[16:42:45] [Render thread/INFO]: Registering Mod Info...
[16:42:45] [Render thread/INFO]: Registering Mod Info took 327.1 microseconds
[16:42:45] [Render thread/INFO]: Registering categories...
[16:42:45] [Render thread/INFO]: Registering categories took 472.1 microseconds
[16:42:45] [Render thread/INFO]: Registering vanilla category extensions...
[16:42:45] [Render thread/INFO]: Registering vanilla category extensions took 205.2 microseconds
[16:42:45] [Render thread/INFO]: Registering recipe catalysts...
[16:42:45] [Render thread/INFO]: Registering recipe catalysts took 147.9 microseconds
[16:42:45] [Render thread/INFO]: Building recipe registry...
[16:42:45] [Render thread/INFO]: Building recipe registry took 206.9 microseconds
[16:42:45] [Render thread/INFO]: Registering advanced plugins...
[16:42:45] [Render thread/INFO]: Registering advanced plugins took 150.0 microseconds
[16:42:45] [Render thread/INFO]: Registering recipes...
[16:42:45] [Render thread/INFO]: Registering recipes: jei:minecraft took 14.97 milliseconds
[16:42:45] [Render thread/INFO]: Registering recipes took 15.36 milliseconds
[16:42:45] [Render thread/INFO]: Registering recipes transfer handlers...
[16:42:45] [Render thread/INFO]: Registering recipes transfer handlers took 296.3 microseconds
[16:42:45] [Render thread/INFO]: Building runtime...
[16:42:45] [Render thread/INFO]: Registering gui handlers...
[16:42:45] [Render thread/INFO]: Registering gui handlers took 305.0 microseconds
[16:42:45] [Render thread/INFO]: Registering Runtime...
[16:42:45] [Render thread/INFO]: Starting JEI GUI
[16:42:45] [Render thread/INFO]: Building ingredient list...
[16:42:45] [Render thread/INFO]: Building ingredient list took 4.520 milliseconds
[16:42:45] [Render thread/INFO]: Building ingredient filter...
[16:42:45] [Render thread/INFO]: Adding 1688 ingredients
[16:42:45] [Render thread/INFO]: Added 1688 ingredients
[16:42:45] [Render thread/INFO]: Building ingredient filter took 35.31 milliseconds
[16:42:45] [Render thread/INFO]: Registering Runtime: jei:fabric_gui took 43.06 milliseconds
[16:42:45] [Render thread/INFO]: Registering Runtime took 43.48 milliseconds
[16:42:45] [Render thread/INFO]: Building runtime took 44.06 milliseconds
[16:42:45] [Render thread/INFO]: Sending Runtime...
[16:42:45] [Render thread/INFO]: Sending Runtime took 202.4 microseconds
[16:42:45] [Render thread/INFO]: Starting JEI took 70.38 milliseconds
[16:42:45] [Render thread/INFO]: Loaded 2 advancements
[16:42:53] [Server thread/INFO]: [F0meG04: Running function practicebot:reset]
[16:42:53] [Render thread/INFO]: [System] [CHAT] Running function practicebot:reset
[16:43:12] [Render thread/INFO]: [System] [CHAT] <Ngoc_Cutee> .
[16:50:56] [Render thread/INFO]: Stopping!
[16:50:56] [Render thread/INFO]: IAS: Closing IAS...
[16:50:56] [Render thread/INFO]: IAS: Shutting down IAS executor...
[16:50:56] [Render thread/INFO]: IAS: IAS executor shut down.
[16:50:56] [Render thread/INFO]: IAS: IAS has been unloaded.
[16:50:56] [Render thread/INFO]: Xaero hud session finalized.
[16:50:56] [Render thread/INFO]: Finalizing world map session...
[16:50:56] [Thread-6/INFO]: World map force-cleaned!
[16:50:56] [Server thread/INFO]: F0meG04 lost connection: Disconnected
[16:50:56] [Server thread/INFO]: F0meG04 left the game
[16:50:56] [Server thread/INFO]: Stopping singleplayer server as player logged out
[16:50:56] [Render thread/INFO]: World map session finalized.
[16:50:56] [Server thread/INFO]: Stopping server
[16:50:56] [Server thread/INFO]: Saving players
[16:50:56] [Server thread/INFO]: Saving worlds
[16:50:56] [Server thread/INFO]: Saving chunks for level 'ServerLevel[§6Theobald's PVP Practice 1.3 §7- §a§l1.21]'/minecraft:overworld
[16:50:56] [Server thread/INFO]: Saving chunks for level 'ServerLevel[§6Theobald's PVP Practice 1.3 §7- §a§l1.21]'/minecraft:the_nether
[16:50:56] [Server thread/INFO]: Saving chunks for level 'ServerLevel[§6Theobald's PVP Practice 1.3 §7- §a§l1.21]'/minecraft:the_end
[16:50:57] [Server thread/INFO]: ThreadedAnvilChunkStorage (Theobald's PVP Practice 1.3): All chunks are saved
[16:50:57] [Server thread/INFO]: ThreadedAnvilChunkStorage (DIM-1): All chunks are saved
[16:50:57] [Server thread/INFO]: ThreadedAnvilChunkStorage (DIM1): All chunks are saved
[16:50:57] [Server thread/INFO]: ThreadedAnvilChunkStorage: All dimensions are saved
[16:50:57] [Render thread/INFO]: Stopping worker threads
