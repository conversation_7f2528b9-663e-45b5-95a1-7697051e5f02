[17:13:39] [main/INFO]: Loading Minecraft 1.21.1 with Fabric Loader 0.16.10
[17:13:39] [ForkJoinPool-1-worker-10/WARN]: Mod org_cloudburstmc_netty_netty-transport-raknet uses the version 1.0.0.CR3-SNAPSHOT which isn't compatible with Loader's extended semantic version format (Could not parse version number component 'CR3'!), <PERSON>m<PERSON>er is recommended for reliably evaluating dependencies and prioritizing newer version
[17:13:39] [ForkJoinPool-1-worker-4/WARN]: Mod com_github_oryxel_cubeconverter uses the version 5ae1e90e4f which isn't compatible with Loader's extended semantic version format (Could not parse version number component '5ae1e90e4f'!), <PERSON><PERSON><PERSON><PERSON> is recommended for reliably evaluating dependencies and prioritizing newer version
[17:13:39] [main/INFO]: Loading 113 mods:
	- appleskin 3.0.6+mc1.21
	   \-- cloth-config 15.0.127
	        \-- cloth-basic-math 0.6.1
	- bobby 5.2.4+mc1.21
	   |-- com_typesafe_config 1.4.2
	   |-- fabric-api-base 0.4.42+6573ed8c6a
	   |-- fabric-command-api-v2 2.2.28+6ced4dd96a
	   |-- io_leangen_geantyref_geantyref 1.3.13
	   |-- org_spongepowered_configurate-core 4.1.2
	   \-- org_spongepowered_configurate-hocon 4.1.2
	- carpet 1.4.147+v240613
	- clumps 19.0.0.1
	- fabric-api 0.116.4+1.21.1
	   |-- fabric-api-lookup-api-v1 1.6.71+b559734419
	   |-- fabric-biome-api-v1 13.0.31+d527f9fd19
	   |-- fabric-block-api-v1 1.1.0+0bc3503219
	   |-- fabric-block-view-api-v2 1.0.11+ebb2264e19
	   |-- fabric-blockrenderlayer-v1 1.1.52+0af3f5a719
	   |-- fabric-client-tags-api-v1 1.1.15+6573ed8c19
	   |-- fabric-command-api-v1 1.2.49+f71b366f19
	   |-- fabric-commands-v0 0.2.66+df3654b319
	   |-- fabric-content-registries-v0 8.0.19+b559734419
	   |-- fabric-convention-tags-v1 2.1.5+7f945d5b19
	   |-- fabric-convention-tags-v2 2.11.1+a406e79519
	   |-- fabric-crash-report-info-v1 0.2.29+0af3f5a719
	   |-- fabric-data-attachment-api-v1 1.4.5+6116a37819
	   |-- fabric-data-generation-api-v1 20.2.32+5b9dea2e19
	   |-- fabric-dimensions-v1 4.0.0+6fc22b9919
	   |-- fabric-entity-events-v1 1.8.0+2b27e0a419
	   |-- fabric-events-interaction-v0 0.7.13+ba9dae0619
	   |-- fabric-game-rule-api-v1 1.0.53+6ced4dd919
	   |-- fabric-item-api-v1 11.1.1+d5debaed19
	   |-- fabric-item-group-api-v1 4.1.7+def88e3a19
	   |-- fabric-key-binding-api-v1 1.0.47+0af3f5a719
	   |-- fabric-keybindings-v0 0.2.45+df3654b319
	   |-- fabric-lifecycle-events-v1 2.6.0+0865547519
	   |-- fabric-loot-api-v2 3.0.15+3f89f5a519
	   |-- fabric-loot-api-v3 1.0.3+3f89f5a519
	   |-- fabric-message-api-v1 6.0.14+8aaf3aca19
	   |-- fabric-model-loading-api-v1 2.0.0+fe474d6b19
	   |-- fabric-networking-api-v1 4.3.0+c7469b2119
	   |-- fabric-object-builder-api-v1 15.2.1+40875a9319
	   |-- fabric-particles-v1 4.0.2+6573ed8c19
	   |-- fabric-recipe-api-v1 5.0.14+248df81c19
	   |-- fabric-registry-sync-v0 5.3.1+e3eddc2119
	   |-- fabric-renderer-api-v1 3.4.0+c705a49c19
	   |-- fabric-renderer-indigo 1.7.0+c705a49c19
	   |-- fabric-renderer-registries-v1 3.2.69+df3654b319
	   |-- fabric-rendering-data-attachment-v1 0.3.49+73761d2e19
	   |-- fabric-rendering-fluids-v1 3.1.6+1daea21519
	   |-- fabric-rendering-v0 1.1.72+df3654b319
	   |-- fabric-rendering-v1 5.1.0+ab4c25a019
	   |-- fabric-resource-conditions-api-v1 4.3.0+8dc279b119
	   |-- fabric-resource-loader-v0 1.3.1+5b5275af19
	   |-- fabric-screen-api-v1 2.0.25+8b68f1c719
	   |-- fabric-screen-handler-api-v1 1.3.90+b559734419
	   |-- fabric-sound-api-v1 1.0.23+6573ed8c19
	   |-- fabric-transfer-api-v1 5.4.3+c24bd99419
	   \-- fabric-transitive-access-wideners-v1 6.2.0+45b9699719
	- fabricloader 0.16.10
	   \-- mixinextras 0.4.1
	- fpsreducer 1.21-2.9
	- ias 9.0.4
	- indium 1.0.35+mc1.21
	- iris 1.7.3+mc1.21
	   |-- io_github_douira_glsl-transformer 2.0.1
	   |-- org_anarres_jcpp 1.4.14
	   \-- org_antlr_antlr4-runtime 4.13.1
	- java 21
	- jei 19.22.0.315
	- justzoom 2.1.0
	- konkrete 1.9.9
	- lithium 0.15.0+mc1.21.1
	- minecraft 1.21.1
	- modmenu 11.0.3
	- mousetweaks 2.26
	- nvidium 0.3.1
	- placeholder-api 2.4.2+1.21
	- shulkerboxslot 6.0.0+1.21.1
	   \-- spectrelib 0.17.2+1.21
	        |-- com_electronwill_night-config_core 3.8.0
	        \-- com_electronwill_night-config_toml 3.8.0
	- shulkerboxtooltip 5.1.6+1.21.1
	- sodium 0.5.11+mc1.21
	- trinkets 3.10.0
	   |-- cardinal-components-base 6.1.0
	   \-- cardinal-components-entity 6.1.0
	- viafabricplus 3.4.9
	   |-- com_github_oryxel_cubeconverter 5ae1e90e4f
	   |-- com_google_code_findbugs_jsr305 3.0.2
	   |-- com_vdurmont_semver4j 3.1.0
	   |-- com_viaversion_viabackwards-common 5.1.2-SNAPSHOT
	   |-- com_viaversion_viaversion-common 5.1.2-SNAPSHOT
	   |-- de_florianmichael_classic4j 2.1.1-SNAPSHOT
	   |-- io_jsonwebtoken_jjwt-api 0.12.6
	   |-- io_jsonwebtoken_jjwt-gson 0.12.6
	   |-- io_jsonwebtoken_jjwt-impl 0.12.6
	   |-- io_netty_netty-codec-http 4.1.114
	   |-- net_jodah_expiringmap 0.5.10
	   |-- net_lenni0451_commons_httpclient 1.6.0
	   |-- net_lenni0451_mcping 1.4.2
	   |-- net_lenni0451_mcstructs-bedrock_forms 1.2.1
	   |-- net_lenni0451_mcstructs-bedrock_text 1.2.1
	   |-- net_lenni0451_reflect 1.3.4
	   |-- net_raphimc_minecraftauth 4.1.1
	   |-- net_raphimc_viaaprilfools-common 3.0.5-SNAPSHOT
	   |-- net_raphimc_viabedrock 0.0.13-SNAPSHOT
	   |-- net_raphimc_vialegacy 3.0.6-SNAPSHOT
	   |-- net_raphimc_vialoader 3.0.5-SNAPSHOT
	   |-- org_cloudburstmc_netty_netty-transport-raknet 1.0.0.CR3-SNAPSHOT
	   \-- org_lz4_lz4-pure-java 1.8.0
	- xaerominimap 25.2.6
	- xaeroworldmap 1.39.9
[17:13:39] [main/INFO]: SpongePowered MIXIN Subsystem Version=0.8.7 Source=file:/D:/Minecraft_Main/Minecraft_Launchers/ATLauncher/libraries/net/fabricmc/sponge-mixin/0.15.4+mixin.0.8.7/sponge-mixin-0.15.4+mixin.0.8.7.jar Service=Knot/Fabric Env=CLIENT
[17:13:39] [main/INFO]: Compatibility level set to JAVA_21
[17:13:39] [main/INFO]: OptiFine was NOT detected.
[17:13:39] [main/INFO]: OptiFabric was NOT detected.
[17:13:39] [main/INFO]: Option 'mixin.entity.collisions.fluid' requires 'mixin.util.block_tracking=true' but found 'false'. Setting 'mixin.entity.collisions.fluid=false'.
[17:13:39] [main/INFO]: Option 'mixin.experimental.entity.block_caching.block_support' requires 'mixin.util.block_tracking=true' but found 'false'. Setting 'mixin.experimental.entity.block_caching.block_support=false'.
[17:13:39] [main/INFO]: Option 'mixin.experimental.entity.block_caching.fluid_pushing' requires 'mixin.util.block_tracking=true' but found 'false'. Setting 'mixin.experimental.entity.block_caching.fluid_pushing=false'.
[17:13:39] [main/INFO]: Option 'mixin.experimental.entity.block_caching.block_touching' requires 'mixin.util.block_tracking=true' but found 'false'. Setting 'mixin.experimental.entity.block_caching.block_touching=false'.
[17:13:39] [main/INFO]: Option 'mixin.experimental.entity.block_caching.suffocation' requires 'mixin.util.block_tracking=true' but found 'false'. Setting 'mixin.experimental.entity.block_caching.suffocation=false'.
[17:13:39] [main/INFO]: Option 'mixin.experimental.entity.block_caching' requires 'mixin.util.block_tracking=true' but found 'false'. Setting 'mixin.experimental.entity.block_caching=false'.
[17:13:39] [main/INFO]: Loaded configuration file for Lithium: 149 options available, 0 override(s) found
[17:13:40] [main/INFO]: Loaded configuration file for Sodium: 42 options available, 3 override(s) found
