tag TheobaldTheBot add refilling
player <PERSON><PERSON><PERSON><PERSON>heBot stop
player <PERSON><PERSON><PERSON><PERSON>heB<PERSON> hotbar 9

execute as <PERSON>baldTheB<PERSON> if score @s refill matches 5 run item replace entity @s weapon.offhand with totem_of_undying
execute as <PERSON>baldTheBot if score @s refill matches 10 run item replace entity @s hotbar.8 with totem_of_undying
execute as <PERSON>baldTheB<PERSON> if score @s difficulty matches 1 if entity @s[tag=shield] run item replace entity @s hotbar.8 with shield[enchantments={levels:{unbreaking:3,mending:1}},minecraft:unbreakable={}]
execute as TheobaldTheBot if score @s difficulty matches 1 if entity @s[tag=shield] run item replace entity @s weapon.offhand with totem_of_undying