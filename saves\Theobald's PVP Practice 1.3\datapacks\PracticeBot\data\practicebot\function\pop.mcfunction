tag <PERSON><PERSON>_<PERSON>ee add refilling
player <PERSON><PERSON><PERSON><PERSON>ee stop
player <PERSON><PERSON><PERSON><PERSON>ee hotbar 9

execute as <PERSON><PERSON><PERSON><PERSON>ee if score @s refill matches 5 run item replace entity @s weapon.offhand with totem_of_undying
execute as <PERSON><PERSON>_<PERSON>ee if score @s refill matches 10 run item replace entity @s hotbar.8 with totem_of_undying
execute as <PERSON><PERSON><PERSON>Cutee if score @s difficulty matches 1 if entity @s[tag=shield] run item replace entity @s hotbar.8 with shield[enchantments={levels:{unbreaking:3,mending:1}},minecraft:unbreakable={}]
execute as <PERSON><PERSON>_Cutee if score @s difficulty matches 1 if entity @s[tag=shield] run item replace entity @s weapon.offhand with totem_of_undying