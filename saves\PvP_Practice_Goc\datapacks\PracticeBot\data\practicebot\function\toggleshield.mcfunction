execute if entity @a[name=TheobaldTheBot,tag=!shield] run tag TheobaldTheBot add addshield
tag TheobaldTheBot remove shield
execute if entity @a[name=TheobaldTheBot,tag=addshield] run tag TheobaldTheBot add shield
tag TheobaldTheBot remove addshield

execute if entity @a[name=TheobaldTheBot,tag=shield] run title @a actionbar [{"text":"SHIELDING ","color":"yellow"},{"text":"ON", "color":"green"}]
execute if entity @a[name=TheobaldTheBot,tag=!shield] run title @a actionbar [{"text":"SHIELDING ","color":"yellow"},{"text":"OFF", "color":"red"}]

execute as TheobaldTheBot if entity @s[tag=shield,tag=sword] run item replace entity @a[scores={kitloaded=0}] weapon.offhand with shield[minecraft:unbreakable={}]
execute as TheobaldTheBot if entity @s[tag=!shield,tag=sword] run item replace entity @a[scores={kitloaded=0}] weapon.offhand with air
execute as TheobaldTheBot if entity @s[tag=shield] as @a[name=!TheobaldTheBot] if score @s kitloaded matches 0 run item replace entity @a hotbar.2 with minecraft:diamond_axe[minecraft:unbreakable={}]
execute as TheobaldTheBot if entity @s[tag=!shield] as @a[name=!TheobaldTheBot] if score @s kitloaded matches 0 run item replace entity @a hotbar.2 with air

execute as TheobaldTheBot if entity @s[tag=shield] run tag @s add tempshield
execute as TheobaldTheBot if entity @s[tag=!shield] run tag @s remove tempshield

execute as TheobaldTheBot if entity @s[tag=sword,tag=!shield,tag=neth] run item replace entity @s hotbar.0 with minecraft:netherite_sword[minecraft:enchantments={levels:{"minecraft:sharpness":5}},minecraft:unbreakable={}]
execute as TheobaldTheBot if entity @s[tag=sword,tag=shield,tag=neth] run item replace entity @s hotbar.0 with minecraft:netherite_sword[minecraft:enchantments={levels:{"minecraft:sharpness":5,"minecraft:knockback":1}},minecraft:unbreakable={}]