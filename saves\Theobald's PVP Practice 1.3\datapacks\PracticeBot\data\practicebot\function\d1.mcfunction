#effect give <PERSON><PERSON><PERSON>Cutee resistance 1 255 true
#effect give <PERSON><PERSON><PERSON>Cutee speed 1 1 true
scoreboard players remove <PERSON><PERSON><PERSON>Cutee hitcd 1
scoreboard players add <PERSON>oc<PERSON>Cutee refill 1
scoreboard players remove @a[name=<PERSON><PERSON>_Cutee] strafecd 1
scoreboard players set @a[scores={totem=1..}] refill 0
scoreboard players set @a totem 0

execute as <PERSON><PERSON><PERSON>Cutee unless entity @s[tag=refilling] run function practicebot:look
execute as <PERSON><PERSON>_Cutee if score @s difficulty matches 5 run function practicebot:look

player <PERSON>oc_Cutee sprint
player <PERSON><PERSON><PERSON><PERSON> move forward
player <PERSON>oc_Cutee hotbar 1
execute at Ngoc_Cutee if entity @p[name=!Ngoc_Cutee,distance=..2] run player <PERSON><PERSON><PERSON><PERSON><PERSON> move
execute as @a[name=<PERSON><PERSON>_Cutee] if score @s difficulty matches 1 if score @s hitcd matches 3.. run player <PERSON><PERSON>_<PERSON>ee move
execute at @e[name=crystal] if entity @a[name=Ngoc_Cutee,distance=..2] run player <PERSON><PERSON>_<PERSON><PERSON> move

execute as Ngoc_Cutee if entity @s[scores={strafecd=..0},tag=strafe] run function practicebot:choosedirection
function practicebot:strafe

execute as <PERSON><PERSON><PERSON>Cutee at @s unless entity @s[tag=refilling] if block ~ ~-.2 ~ netherite_block at @e[name=crystal] if entity @a[name=Ngoc_Cutee,distance=..5] run player Ngoc_<PERSON>ee look at ~ ~1 ~ 
execute as Ngoc_Cutee at @s if block ~ ~-.2 ~ netherite_block at @e[name=crystal] if entity @a[name=Ngoc_Cutee,distance=..5] run function practicebot:hitcrystal
execute as Ngoc_Cutee at @s unless entity @s[tag=refilling] at @p[name=!Ngoc_Cutee,distance=4.2..] if block ~ ~-.1 ~ netherite_block if score Ngoc_Cutee difficulty matches 2..4 run function practicebot:pearl
execute at Ngoc_Cutee at @p[name=!Ngoc_Cutee,distance=4.2..] if score Ngoc_Cutee difficulty matches 5 run function practicebot:tp

execute as Ngoc_Cutee unless entity @s[tag=refilling] if score Ngoc_Cutee hitcd matches ..-2 if score Ngoc_Cutee difficulty matches 2.. at Ngoc_Cutee if entity @p[name=!Ngoc_Cutee,distance=..10] run function practicebot:pearl
execute as Ngoc_Cutee unless entity @s[tag=refilling] if score @s hitcd matches ..-2 if score @s difficulty matches 1 at @s if entity @p[name=!Ngoc_Cutee,distance=..2] if score @s shieldcd matches 1.. run function practicebot:hit
execute as Ngoc_Cutee unless entity @s[tag=refilling,tag=shield] if score @s hitcd matches ..-2 if score @s difficulty matches 1 at @s if entity @p[name=!Ngoc_Cutee,distance=..2] run function practicebot:hit
execute as Ngoc_Cutee unless entity @s[tag=refilling] if score @s hitcd matches ..0 if score @s difficulty matches 2.. at @s if entity @p[name=!Ngoc_Cutee,distance=..2.5] run function practicebot:hit
execute as Ngoc_Cutee unless entity @s[tag=refilling] if score @s hitcd matches ..0 if score @s difficulty matches 5.. at @s if entity @p[name=!Ngoc_Cutee,distance=..3] run function practicebot:hit

#execute as @a[name=!Ngoc_Cutee,scores={pops=27..}] run function practicebot:refilltotem
#scoreboard players set @a[name=!Ngoc_Cutee,scores={pops=28..}] pops 0

# execute as @a[nbt={HurtTime:9s},name=Ngoc_Cutee] on attacker run scoreboard players add @s Score 1
# scoreboard players reset Ngoc_Cutee Score

execute if score Ngoc_Cutee difficulty matches 1..2 run kill @e[type=marker,scores={crystal=1..}]
kill @e[type=marker,scores={crystal=2..}]

tag Ngoc_Cutee remove refilling
execute as Ngoc_Cutee if score @s difficulty matches 1..2 if score @s refill matches ..22 run function practicebot:pop
execute as Ngoc_Cutee if score @s difficulty matches 3 if score @s refill matches ..18 run function practicebot:pop
execute as Ngoc_Cutee if score @s difficulty matches 4 if score @s refill matches ..14 run function practicebot:pop
execute as Ngoc_Cutee if score @s difficulty matches 5 run function practicebot:refilltotem