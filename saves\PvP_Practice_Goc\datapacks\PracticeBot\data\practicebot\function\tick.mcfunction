execute if entity @a[name=TheobaldTheBot,tag=speed] run effect give @a speed 2 1 true
execute at @a[name=TheobaldTheBot,gamemode=creative] run function practicebot:look
execute as TheobaldTheBot if entity @s[gamemode=!creative,tag=sword] run function practicebot:sword
execute as TheobaldTheBot if entity @s[gamemode=!creative,tag=crystal] run function practicebot:crystal
execute as @e[type=#arrows,nbt={inGround:1b}] run kill @s
function practicebot:setbutton
#execute if entity @p[name=TheobaldTheBot,gamemode=creative] run effect give @a resistance 2 255 true
#execute if entity @p[name=TheobaldTheBot,gamemode=creative] run effect give @a saturation 2 255 true

execute as @a[scores={death=1..},name=!TheobaldTheBot] run function practicebot:reset
#execute as TheobaldTheBot at @a[name=!TheobaldTheBot] if entity @s[tag=!fire] run fill ~-15 61 ~-15 ~15 62 ~15 air replace fire
execute as TheobaldTheBot if entity @s[tag=sword] if entity @a[name=!TheobaldTheBot,nbt=!{Inventory:[{id:"minecraft:barrier",Slot:17b}]},scores={kitloaded=0}] run function practicebot:reset

execute as @e[type=snowball] at @s run item modify entity @p weapon.mainhand practicebot:refill
execute as @e[type=snowball] at @s run tp TheobaldTheBot ~ ~ ~
execute as @e[type=snowball] at @s run kill @s

execute as @e[type=egg] at @s run item modify entity @p weapon.mainhand practicebot:refill
execute as @e[type=egg] at @s run function practicebot:reset
execute as @e[type=egg] run kill @s

execute as @e[type=interaction] if data entity @s attack run tellraw @p [{"text":" IF YOU LIKE THE MAP, ","bold":true,"color": "white","underlined": false},{"text":"SUBSCRIBE!!","bold":true,"underlined":true,"color":"red","clickEvent":{"action":"open_url","value":"https://www.youtube.com/@TheobaldTheBird?sub_confirmation=1"},"hoverEvent":{"action":"show_text","contents":[{"text":"SUBSCRIBE","bold":true,"color":"red"}]}}]
execute as @e[type=interaction] if data entity @s interaction run tellraw @p [{"text":" IF YOU LIKE THE MAP, ","bold":true,"color": "white","underlined": false},{"text":"SUBSCRIBE!!","bold":true,"underlined":true,"color":"red","clickEvent":{"action":"open_url","value":"https://www.youtube.com/@TheobaldTheBird?sub_confirmation=1"},"hoverEvent":{"action":"show_text","contents":[{"text":"SUBSCRIBE","bold":true,"color":"red"}]}}]

execute as @e[type=interaction] run data remove entity @s attack
execute as @e[type=interaction] run data remove entity @s interaction

execute as @p[name=!TheobaldTheBot] store result score @s dtick run data get entity @s HurtTime 1
