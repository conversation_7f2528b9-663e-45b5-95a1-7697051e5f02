player <PERSON><PERSON><PERSON><PERSON><PERSON> stop
player <PERSON><PERSON><PERSON><PERSON><PERSON> move forward
player <PERSON><PERSON><PERSON><PERSON><PERSON> sprint

execute as <PERSON><PERSON><PERSON><PERSON>ee if entity @s[tag=sword,tag=shield] if score @p[name=!<PERSON><PERSON>_Cutee] shieldcount matches 1.. if score @s difficulty matches 2.. run player <PERSON><PERSON>_<PERSON>ee hotbar 4
execute as <PERSON><PERSON><PERSON><PERSON>ee if entity @s[tag=sword,tag=shield] if score @p[name=!Ng<PERSON>_Cutee] shieldcount matches 1.. if score @s difficulty matches 2.. run schedule function practicebot:hotbar1 2t
scoreboard players set @a[name=!Ngoc_Cutee] shieldcount 0

player <PERSON><PERSON>_<PERSON>ee attack once

execute if entity @a[name=Ngoc_Cutee,tag=crystal] if score Ngoc_Cutee difficulty matches 2.. run function practicebot:spawncrystal
scoreboard players set Ng<PERSON>_Cutee hitcd 11

# schedule function practicebot:attack 4t

execute as <PERSON><PERSON>_Cutee if entity @s[tag=shield,tag=sword] run schedule function practicebot:usecontinuous 2t
# execute if entity @a[name=Ngoc_Cutee,tag=crystal] run scoreboard players set Ngoc_Cutee hitcd 21