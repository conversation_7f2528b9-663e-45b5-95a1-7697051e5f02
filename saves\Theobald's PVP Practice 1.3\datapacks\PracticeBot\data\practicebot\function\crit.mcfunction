player <PERSON><PERSON><PERSON><PERSON><PERSON> stop
player <PERSON><PERSON><PERSON><PERSON> move forward
player <PERSON><PERSON><PERSON><PERSON><PERSON> unsprint
player <PERSON><PERSON><PERSON><PERSON> attack

execute as <PERSON><PERSON><PERSON><PERSON>ee if entity @s[tag=sword,tag=shield] if score @p[name=!<PERSON><PERSON>_Cutee] shieldcount matches 1.. if score @s difficulty matches 2.. run player <PERSON><PERSON><PERSON><PERSON>ee hotbar 4
execute as <PERSON><PERSON><PERSON><PERSON>ee if entity @s[tag=sword,tag=shield] if score @p[name=!Ng<PERSON>_Cutee] shieldcount matches 1.. if score @s difficulty matches 2.. run schedule function practicebot:hotbar1 2t
scoreboard players set @a[name=!Ngoc_Cutee] shieldcount 0

scoreboard players set <PERSON><PERSON>_<PERSON>ee hitcd 11
execute as <PERSON><PERSON>_<PERSON>ee if entity @s[tag=shield,tag=sword] run schedule function practicebot:usecontinuous 2t