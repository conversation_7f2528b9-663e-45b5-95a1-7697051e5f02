clear @s
execute as <PERSON><PERSON><PERSON><PERSON><PERSON> run function practicebot:refilltotem

item replace entity @s armor.head with minecraft:netherite_helmet[minecraft:enchantments={levels:{"minecraft:protection":4}},minecraft:unbreakable={}]
item replace entity @s armor.chest with minecraft:netherite_chestplate[minecraft:enchantments={levels:{"minecraft:protection":4}},minecraft:unbreakable={}]
item replace entity @s armor.legs with minecraft:netherite_leggings[minecraft:enchantments={levels:{"minecraft:blast_protection":4}},minecraft:unbreakable={}]
item replace entity @s armor.feet with minecraft:netherite_boots[minecraft:enchantments={levels:{"minecraft:feather_falling":4,"minecraft:protection":4}},minecraft:unbreakable={}]
execute as Ngoc_Cutee if entity @s[tag=doublebp] run item replace entity @s armor.feet with minecraft:netherite_boots[minecraft:enchantments={levels:{"minecraft:feather_falling":4,"minecraft:blast_protection":4}},minecraft:unbreakable={}]


item replace entity @s hotbar.0 with minecraft:netherite_sword[minecraft:enchantments={levels:{"minecraft:sharpness":5,"minecraft:knockback":1}},minecraft:unbreakable={}]
item replace entity @s hotbar.1 with minecraft:obsidian[minecraft:can_place_on={predicates:[{blocks:"netherite_block"},{blocks:"obsidian"},{blocks:"glowstone"},{blocks:"respawn_anchor"},{blocks:"fire"}]}] 64
item replace entity @s hotbar.2 with minecraft:end_crystal[minecraft:can_place_on={predicates:[{blocks:"obsidian"}]}] 64
item replace entity @s hotbar.3 with minecraft:netherite_axe[minecraft:enchantments={levels:{"sharpness":5}},minecraft:unbreakable={}]
item replace entity @s hotbar.4 with minecraft:golden_apple 64
item replace entity @s hotbar.5 with minecraft:respawn_anchor[minecraft:can_place_on={predicates:[{blocks:"netherite_block"},{blocks:"obsidian"},{blocks:"glowstone"},{blocks:"respawn_anchor"},{blocks:"fire"}]}] 64
item replace entity @s hotbar.6 with minecraft:glowstone[minecraft:can_place_on={predicates:[{blocks:"netherite_block"},{blocks:"obsidian"},{blocks:"glowstone"},{blocks:"respawn_anchor"},{blocks:"fire"}]}] 64
item replace entity @s inventory.0 with minecraft:tipped_arrow[minecraft:potion_contents={potion:"minecraft:long_slow_falling"}] 64
item replace entity @s hotbar.7 with minecraft:ender_pearl 16
item replace entity @s hotbar.8 with totem_of_undying

execute as Ngoc_Cutee if entity @s[tag=sword,tag=!shield] run item replace entity @s hotbar.0 with minecraft:netherite_sword[minecraft:enchantments={levels:{"minecraft:sharpness":5}},minecraft:unbreakable={}]