# Fix function to repair common issues
tellraw @a [{"text":"=== ATTEMPTING TO FIX BOT ===","color":"yellow"}]

# Kill any existing bot
player <PERSON><PERSON><PERSON><PERSON><PERSON> kill
tellraw @a [{"text":"✓ Killed existing bot","color":"green"}]

# Reset scoreboard
scoreboard objectives add hitcd dummy
scoreboard objectives add strafecd dummy
scoreboard objectives add hit minecraft.custom:damage_dealt
scoreboard objectives add difficulty dummy
scoreboard objectives add direction dummy
scoreboard objectives add crystal dummy
scoreboard objectives add crystalcd dummy
scoreboard objectives add despawn dummy
scoreboard objectives add pops minecraft.used:minecraft.totem_of_undying
scoreboard objectives add death deathCount
scoreboard objectives add Score dummy
scoreboard objectives add x dummy
scoreboard objectives add y dummy
scoreboard objectives add z dummy
scoreboard objectives add oldx dummy
scoreboard objectives add oldy dummy
scoreboard objectives add oldz dummy
scoreboard objectives add vx dummy
scoreboard objectives add vy dummy
scoreboard objectives add vz dummy
scoreboard objectives add fx dummy
scoreboard objectives add fz dummy
scoreboard objectives add rx dummy
scoreboard objectives add rz dummy
tellraw @a [{"text":"✓ Reset scoreboards","color":"green"}]

# Spawn bot at correct position
player <PERSON><PERSON><PERSON><PERSON>ee spawn at 8.50 13.00 14.50 facing 180 0
tellraw @a [{"text":"✓ Spawned bot at correct position","color":"green"}]

# Set up bot
tag Ngoc_Cutee add bot
gamemode creative Ngoc_Cutee
scoreboard players set Ngoc_Cutee difficulty 0
tellraw @a [{"text":"✓ Set up bot tags and gamemode","color":"green"}]

# Set up markers
kill @e[type=marker]
summon minecraft:marker ~ ~ ~ {CustomName:'{"text":"pforward"}'}
summon minecraft:marker ~ ~ ~ {CustomName:'{"text":"pright"}'}
summon minecraft:marker ~ ~ ~ {CustomName:'{"text":"bforward"}'}
summon minecraft:marker ~ ~ ~ {CustomName:'{"text":"bright"}'}
tellraw @a [{"text":"✓ Set up markers","color":"green"}]

# Set sword mode
function practicebot:setsword
tellraw @a [{"text":"✓ Set sword mode","color":"green"}]

# Reset
function practicebot:reset
tellraw @a [{"text":"✓ Reset complete","color":"green"}]

tellraw @a [{"text":"=== FIX COMPLETE ===","color":"yellow"}]
tellraw @a [{"text":"Run /function practicebot:debug to check status","color":"gold"}]
